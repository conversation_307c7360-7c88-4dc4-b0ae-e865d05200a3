//
//  ContentView.swift
//  NIRA-Tamil
//
//  Created by <PERSON><PERSON><PERSON> on 6/17/25.
//

//
//  ContentView.swift
//  NIRA-Tamil
//
//  Created by MAGESH DHANASEKARAN on 5/22/25.
//

import SwiftUI
import SwiftData

struct ContentView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) var colorScheme
    @StateObject private var authService = AuthenticationService.shared
    @State private var selectedTab = 0
    @State private var showOnboarding = false

    var body: some View {
        Group {
            if showOnboarding {
                OnboardingView {
                    showOnboarding = false
                }
            } else if !authService.isAuthenticated {
                // Temporarily bypass authentication for testing
                MainTabView(selectedTab: $selectedTab)
                    .onAppear {
                        // Auto-enable guest mode for testing
                        Task {
                            await authService.continueAsGuest()
                        }
                    }
            } else {
                MainTabView(selectedTab: $selectedTab)
            }
        }
        .onAppear {
            checkOnboardingStatus()
        }
        .alert("Authentication Error", isPresented: .constant(authService.errorMessage != nil)) {
            Button("OK") {
                authService.errorMessage = nil
            }
        } message: {
            if let errorMessage = authService.errorMessage {
                Text(errorMessage)
            }
        }
    }

    private func checkOnboardingStatus() {
        // Temporarily skip onboarding for testing
        showOnboarding = false
        // let hasCompletedOnboarding = UserDefaults.standard.bool(forKey: "hasCompletedOnboarding")
        // showOnboarding = !hasCompletedOnboarding
    }
}

// MARK: - Color Extensions moved to Config/ColorExtensions.swift

// MARK: - Main Tab View

struct MainTabView: View {
    @Binding var selectedTab: Int
    @State private var currentStreak = 7
    @State private var userName = "Alex"
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        ZStack {
            // Dynamic background that changes with time of day
            dynamicBackground
                .ignoresSafeArea()

            // Universal 5-tab navigation focused on Tamil learning
            universalNavigationView
        }
    }

    // MARK: - Universal Navigation for Tamil Learning

    private var universalNavigationView: some View {
        TabView(selection: $selectedTab) {
            // Tab 0: Tamil Cultural Dashboard
            TamilCulturalDashboardView()
                .tabItem {
                    TabItemView(
                        icon: selectedTab == 0 ? "star.fill" : "star",
                        title: "Tamil Culture",
                        isSelected: selectedTab == 0,
                        color: .emojiOrange
                    )
                }
                .tag(0)

            // Tab 1: Learn Tamil Lessons (now includes Read & Write)
            EnhancedLessonsView()
                .tabItem {
                    TabItemView(
                        icon: selectedTab == 1 ? "book.fill" : "book",
                        title: "Lessons",
                        isSelected: selectedTab == 1,
                        color: .emojiBlue
                    )
                }
                .tag(1)

            // Tab 2: Explore Tamil Culture (replaces Practice)
            ExploreView()
                .tabItem {
                    TabItemView(
                        icon: selectedTab == 2 ? "safari.fill" : "safari",
                        title: "Explore",
                        isSelected: selectedTab == 2,
                        color: .emojiGreen
                    )
                }
                .tag(2)

            // Tab 3: Tamil Live Assist
            AgentsView()
                .tabItem {
                    TabItemView(
                        icon: selectedTab == 3 ? "person.3.fill" : "person.3",
                        title: "Tamil Assist",
                        isSelected: selectedTab == 3,
                        color: .emojiPurple
                    )
                }
                .tag(3)

            // Tab 4: More Tamil Features
            MoreView()
                .tabItem {
                    TabItemView(
                        icon: selectedTab == 4 ? "ellipsis.circle.fill" : "ellipsis.circle",
                        title: "More",
                        isSelected: selectedTab == 4,
                        color: .emojiPink
                    )
                }
                .tag(4)
        }
        .accentColor(Color("TamilColor"))
        .onAppear {
            configureTabBarAppearance()
        }
    }

    // MARK: - Dynamic Background

    private var dynamicBackground: some View {
        LinearGradient(
            gradient: Gradient(colors: [
                Color.blue.opacity(0.1),
                Color.purple.opacity(0.1)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    // MARK: - Tab Bar Configuration

    private func configureTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.systemBackground

        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

// MARK: - Tab Item View

struct TabItemView: View {
    let icon: String
    let title: String
    let isSelected: Bool
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: isSelected ? .semibold : .regular))
                .foregroundColor(isSelected ? color : .secondary)

            Text(title)
                .font(.caption2)
                .fontWeight(isSelected ? .semibold : .regular)
                .foregroundColor(isSelected ? color : .secondary)
        }
    }
}
