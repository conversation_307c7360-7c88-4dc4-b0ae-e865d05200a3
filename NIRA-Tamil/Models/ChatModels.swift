//
//  ChatModels.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import SwiftUI
import Foundation

// MARK: - Chat View Model

@MainActor
class ChatViewModel: ObservableObject {
    @Published var messages: [AIChatMessage] = []
    @Published var isLoading = false
    @Published var isOnline = true
    @Published var isTyping = false
    @Published var isRecording = false
    @Published var suggestions: [String] = []
    @Published var showingAttachmentPicker = false
    @Published var showingLiveVoiceInterface = false
    
    private var currentAgent: LanguageTutor?
    
    func initializeChat(with agent: LanguageTutor) {
        self.currentAgent = agent
        self.isOnline = true
        
        // Add welcome message
        let welcomeMessage = AIChatMessage(
            content: "Hello! I'm \(agent.name), your \(agent.language.displayName) tutor. How can I help you today?",
            isFromUser: false,
            timestamp: Date()
        )
        messages.append(welcomeMessage)
        
        // Set initial suggestions
        suggestions = getSuggestionsForLanguage(agent.language)
    }
    
    func sendMessage(_ text: String) async {
        guard let agent = currentAgent else { return }
        
        // Add user message
        let userMessage = AIChatMessage(
            content: text,
            isFromUser: true,
            timestamp: Date()
        )
        messages.append(userMessage)
        
        // Clear suggestions
        suggestions = []
        
        // Show typing indicator
        isTyping = true
        isLoading = true
        
        // Simulate AI response delay
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
        
        // Generate AI response
        let response = generateAIResponse(for: text, agent: agent)
        let aiMessage = AIChatMessage(
            content: response.content,
            isFromUser: false,
            timestamp: Date(),
            responseTime: response.responseTime,
            vocabularyHighlights: response.vocabularyHighlights,
            grammarTips: response.grammarTips,
            culturalNotes: response.culturalNotes
        )
        
        messages.append(aiMessage)
        
        // Hide typing indicator
        isTyping = false
        isLoading = false
        
        // Update suggestions
        suggestions = getFollowUpSuggestions(for: text, language: agent.language)
    }
    
    func sendAttachment(_ attachment: MessageAttachment) {
        let attachmentMessage = AIChatMessage(
            content: "Shared: \(attachment.name)",
            isFromUser: true,
            timestamp: Date(),
            attachments: [attachment]
        )
        messages.append(attachmentMessage)
    }
    
    func startLiveVoiceConversation() {
        showingLiveVoiceInterface = true
    }
    
    func toggleVoiceRecording() {
        isRecording.toggle()
        if isRecording {
            startVoiceRecording()
        } else {
            stopVoiceRecording()
        }
    }
    
    func startVoiceRecording() {
        // TODO: Implement voice recording
        print("Starting voice recording...")
    }
    
    func stopVoiceRecording() {
        // TODO: Implement voice recording stop
        print("Stopping voice recording...")
    }
    
    // MARK: - Helper Methods
    
    private func getSuggestionsForLanguage(_ language: Language) -> [String] {
        switch language {
        case .french:
            return ["Bonjour!", "Comment allez-vous?", "Merci beaucoup", "Au revoir"]
        case .spanish:
            return ["¡Hola!", "¿Cómo está usted?", "Muchas gracias", "Adiós"]
        case .japanese:
            return ["こんにちは", "元気ですか？", "ありがとう", "さようなら"]
        case .tamil:
            return ["வணக்கம்", "எப்படி இருக்கீங்க?", "நன்றி", "போய்ட்டு வரேன்"]
        default:
            return ["Hello!", "How are you?", "Thank you", "Goodbye"]
        }
    }
    
    private func getFollowUpSuggestions(for message: String, language: Language) -> [String] {
        // Simple suggestion logic based on message content
        if message.lowercased().contains("hello") || message.lowercased().contains("hi") {
            return ["Tell me about yourself", "What should I learn first?", "Can you help me with pronunciation?"]
        } else if message.lowercased().contains("thank") {
            return ["What's next?", "Can we practice more?", "Tell me about culture"]
        } else {
            return ["Can you explain more?", "Give me an example", "What about grammar?"]
        }
    }
    
    private func generateAIResponse(for message: String, agent: LanguageTutor) -> (content: String, responseTime: Double, vocabularyHighlights: [VocabularyHighlight]?, grammarTips: [String]?, culturalNotes: [String]?) {
        
        let responseTime = Double.random(in: 0.5...2.0)
        
        // Simple response generation based on message content
        var content = ""
        var vocabularyHighlights: [VocabularyHighlight]? = nil
        var grammarTips: [String]? = nil
        var culturalNotes: [String]? = nil
        
        if message.lowercased().contains("hello") || message.lowercased().contains("hi") {
            content = "Hello! I'm excited to help you learn \(agent.language.displayName). What would you like to focus on today?"
        } else if message.lowercased().contains("pronunciation") {
            content = "Great question! Pronunciation is key to being understood. Let's start with some basic sounds in \(agent.language.displayName)."
            grammarTips = ["Focus on vowel sounds first", "Practice with native audio"]
        } else if message.lowercased().contains("culture") {
            content = "Culture and language go hand in hand! Understanding cultural context will make your \(agent.language.displayName) much more natural."
            culturalNotes = ["Greetings vary by time of day", "Formal vs informal speech is important"]
        } else {
            content = "That's a great point! Let me help you understand this better in \(agent.language.displayName)."
            vocabularyHighlights = [
                VocabularyHighlight(word: "understand", definition: "to comprehend or grasp the meaning"),
                VocabularyHighlight(word: "better", definition: "in a more excellent way")
            ]
        }
        
        return (content, responseTime, vocabularyHighlights, grammarTips, culturalNotes)
    }
}

// MARK: - AI Chat Message

struct AIChatMessage: Identifiable {
    let id = UUID()
    let content: String
    let isFromUser: Bool
    let timestamp: Date
    let status: MessageStatus
    let attachments: [MessageAttachment]?
    let responseTime: Double?
    let vocabularyHighlights: [VocabularyHighlight]?
    let grammarTips: [String]?
    let culturalNotes: [String]?
    
    init(
        content: String,
        isFromUser: Bool,
        timestamp: Date,
        status: MessageStatus = .sent,
        attachments: [MessageAttachment]? = nil,
        responseTime: Double? = nil,
        vocabularyHighlights: [VocabularyHighlight]? = nil,
        grammarTips: [String]? = nil,
        culturalNotes: [String]? = nil
    ) {
        self.content = content
        self.isFromUser = isFromUser
        self.timestamp = timestamp
        self.status = status
        self.attachments = attachments
        self.responseTime = responseTime
        self.vocabularyHighlights = vocabularyHighlights
        self.grammarTips = grammarTips
        self.culturalNotes = culturalNotes
    }
}

// MARK: - Message Status

enum MessageStatus {
    case sending
    case sent
    case delivered
    case read
    case failed
}

// MARK: - Message Attachment

struct MessageAttachment: Identifiable {
    let id = UUID()
    let type: AttachmentType
    let url: String
    let name: String
    let size: Int64
    
    init(type: AttachmentType, url: String, name: String, size: Int64) {
        self.type = type
        self.url = url
        self.name = name
        self.size = size
    }
}

// MARK: - Attachment Type

enum AttachmentType {
    case image
    case audio
    case document
    case video
}

// MARK: - Vocabulary Highlight

struct VocabularyHighlight {
    let word: String
    let definition: String
    let pronunciation: String?
    let example: String?
    
    init(word: String, definition: String, pronunciation: String? = nil, example: String? = nil) {
        self.word = word
        self.definition = definition
        self.pronunciation = pronunciation
        self.example = example
    }
}

// MARK: - Live Voice Interface View
// Note: LiveVoiceInterfaceView is defined in Views/LiveVoiceInterfaceView.swift 