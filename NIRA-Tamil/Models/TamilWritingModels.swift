//
//  TamilWritingModels.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import Foundation
import SwiftUI

// MARK: - Core Tamil Character Models

/// Represents a Tamil character with all its properties and metadata
struct TamilCharacter: Identifiable, Codable, Hashable {
    let id: UUID
    let character: String
    let characterType: CharacterType
    let unicodeValue: String
    let romanization: String
    let ipaPronunciation: String?
    let characterNameEnglish: String
    let characterNameTamil: String
    let difficultyLevel: Int
    let frequencyRank: Int?
    let strokeCount: Int
    let writingComplexity: WritingComplexity
    let characterCategory: String?
    let learningOrder: Int?
    let strokeOrders: [TamilStrokeOrder]
    let createdAt: Date
    let updatedAt: Date
    
    enum CharacterType: String, Codable, CaseIterable {
        case vowel = "vowel"
        case consonant = "consonant"
        case combined = "combined"
        case special = "special"
        
        var displayName: String {
            switch self {
            case .vowel: return "Vowel"
            case .consonant: return "Consonant"
            case .combined: return "Combined"
            case .special: return "Special"
            }
        }
        
        var icon: String {
            switch self {
            case .vowel: return "a.circle"
            case .consonant: return "textformat"
            case .combined: return "textformat.abc"
            case .special: return "star.circle"
            }
        }
    }
    
    enum WritingComplexity: String, Codable, CaseIterable {
        case simple = "simple"
        case moderate = "moderate"
        case complex = "complex"
        
        var displayName: String {
            switch self {
            case .simple: return "Simple"
            case .moderate: return "Moderate"
            case .complex: return "Complex"
            }
        }
        
        var color: Color {
            switch self {
            case .simple: return .green
            case .moderate: return .orange
            case .complex: return .red
            }
        }
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case character
        case characterType = "character_type"
        case unicodeValue = "unicode_value"
        case romanization
        case ipaPronunciation = "ipa_pronunciation"
        case characterNameEnglish = "character_name_english"
        case characterNameTamil = "character_name_tamil"
        case difficultyLevel = "difficulty_level"
        case frequencyRank = "frequency_rank"
        case strokeCount = "stroke_count"
        case writingComplexity = "writing_complexity"
        case characterCategory = "character_category"
        case learningOrder = "learning_order"
        case strokeOrders = "stroke_orders"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

/// Represents stroke order information for Tamil characters
struct TamilStrokeOrder: Identifiable, Codable, Hashable {
    let id: UUID
    let characterId: UUID
    let strokeNumber: Int
    let strokePath: StrokePath
    let strokeDirection: StrokeDirection
    let strokeType: StrokeType
    let timingDuration: Int // milliseconds
    let pressureVariation: [PressurePoint]
    let startPoint: CGPoint
    let endPoint: CGPoint
    let controlPoints: [CGPoint]?
    let strokeDescription: String?
    let createdAt: Date
    
    enum StrokeDirection: String, Codable, CaseIterable {
        case leftToRight = "left-to-right"
        case topToBottom = "top-to-bottom"
        case rightToLeft = "right-to-left"
        case bottomToTop = "bottom-to-top"
        case clockwise = "clockwise"
        case counterClockwise = "counter-clockwise"
        
        var displayName: String {
            switch self {
            case .leftToRight: return "Left to Right"
            case .topToBottom: return "Top to Bottom"
            case .rightToLeft: return "Right to Left"
            case .bottomToTop: return "Bottom to Top"
            case .clockwise: return "Clockwise"
            case .counterClockwise: return "Counter-clockwise"
            }
        }
        
        var arrow: String {
            switch self {
            case .leftToRight: return "→"
            case .topToBottom: return "↓"
            case .rightToLeft: return "←"
            case .bottomToTop: return "↑"
            case .clockwise: return "↻"
            case .counterClockwise: return "↺"
            }
        }
    }
    
    enum StrokeType: String, Codable, CaseIterable {
        case horizontal = "horizontal"
        case vertical = "vertical"
        case curve = "curve"
        case dot = "dot"
        case hook = "hook"
        case diagonal = "diagonal"
        
        var displayName: String {
            switch self {
            case .horizontal: return "Horizontal"
            case .vertical: return "Vertical"
            case .curve: return "Curve"
            case .dot: return "Dot"
            case .hook: return "Hook"
            case .diagonal: return "Diagonal"
            }
        }
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case characterId = "character_id"
        case strokeNumber = "stroke_number"
        case strokePath = "stroke_path"
        case strokeDirection = "stroke_direction"
        case strokeType = "stroke_type"
        case timingDuration = "timing_duration"
        case pressureVariation = "pressure_variation"
        case startPoint = "start_point"
        case endPoint = "end_point"
        case controlPoints = "control_points"
        case strokeDescription = "stroke_description"
        case createdAt = "created_at"
    }
}

/// Represents stroke path data for drawing
struct StrokePath: Codable, Hashable {
    let pathData: String // SVG path string
    let boundingBox: CGRect
    let pathLength: Double
    
    enum CodingKeys: String, CodingKey {
        case pathData = "path_data"
        case boundingBox = "bounding_box"
        case pathLength = "path_length"
    }
}

/// Represents pressure variation points for realistic stroke demonstration
struct PressurePoint: Codable, Hashable {
    let position: Double // 0.0 to 1.0 along the stroke
    let pressure: Double // 0.0 to 1.0 pressure value
    let timestamp: Double // relative time in stroke
}

/// Represents character combinations (consonant + vowel)
struct TamilCharacterCombination: Identifiable, Codable, Hashable {
    let id: UUID
    let baseCharacterId: UUID
    let modifierCharacterId: UUID
    let combinedCharacter: String
    let combinationRule: String?
    let visualTransformation: String?
    let difficultyLevel: Int
    let frequencyRank: Int?
    let learningOrder: Int?
    let formationNotes: String?
    let createdAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id
        case baseCharacterId = "base_character_id"
        case modifierCharacterId = "modifier_character_id"
        case combinedCharacter = "combined_character"
        case combinationRule = "combination_rule"
        case visualTransformation = "visual_transformation"
        case difficultyLevel = "difficulty_level"
        case frequencyRank = "frequency_rank"
        case learningOrder = "learning_order"
        case formationNotes = "formation_notes"
        case createdAt = "created_at"
    }
}

// MARK: - Writing Practice Content Models

/// Represents writing practice content for different levels and modes
struct TamilWritingContent: Identifiable, Codable {
    let id: UUID
    let contentType: ContentType
    let cefrLevel: CEFRLevel
    let writingMode: WritingMode
    let practiceText: String
    let practiceTextRomanized: String?
    let practiceTextEnglish: String?
    let targetCharacters: [UUID]
    let lessonId: String?
    let vocabularyId: String?
    let difficultyScore: Int
    let estimatedTimeMinutes: Int
    let successCriteria: SuccessCriteria
    let hints: [WritingHint]
    let culturalContext: String?
    let learningObjectives: [String]
    let prerequisiteContentIds: [UUID]
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date
    
    enum ContentType: String, Codable, CaseIterable {
        case character = "character"
        case word = "word"
        case phrase = "phrase"
        case sentence = "sentence"
        case paragraph = "paragraph"
        
        var displayName: String {
            switch self {
            case .character: return "Character"
            case .word: return "Word"
            case .phrase: return "Phrase"
            case .sentence: return "Sentence"
            case .paragraph: return "Paragraph"
            }
        }
        
        var icon: String {
            switch self {
            case .character: return "textformat"
            case .word: return "textformat.abc"
            case .phrase: return "text.quote"
            case .sentence: return "text.alignleft"
            case .paragraph: return "text.justify"
            }
        }
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case contentType = "content_type"
        case cefrLevel = "cefr_level"
        case writingMode = "writing_mode"
        case practiceText = "practice_text"
        case practiceTextRomanized = "practice_text_romanized"
        case practiceTextEnglish = "practice_text_english"
        case targetCharacters = "target_characters"
        case lessonId = "lesson_id"
        case vocabularyId = "vocabulary_id"
        case difficultyScore = "difficulty_score"
        case estimatedTimeMinutes = "estimated_time_minutes"
        case successCriteria = "success_criteria"
        case hints
        case culturalContext = "cultural_context"
        case learningObjectives = "learning_objectives"
        case prerequisiteContentIds = "prerequisite_content_ids"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

/// Success criteria for writing exercises
struct SuccessCriteria: Codable {
    let accuracyThreshold: Double // 0.0 to 100.0
    let completionRequired: Bool
    let timeLimit: Int? // seconds
    let minimumStrokes: Int?
    let maximumAttempts: Int?
    
    enum CodingKeys: String, CodingKey {
        case accuracyThreshold = "accuracy_threshold"
        case completionRequired = "completion_required"
        case timeLimit = "time_limit"
        case minimumStrokes = "minimum_strokes"
        case maximumAttempts = "maximum_attempts"
    }
}

/// Writing hints and guidance
struct WritingHint: Codable, Identifiable {
    let id: UUID
    let hintType: HintType
    let content: String
    let triggerCondition: String? // When to show this hint
    let priority: Int // 1-5, higher is more important
    
    enum HintType: String, Codable, CaseIterable {
        case strokeOrder = "stroke_order"
        case pressure = "pressure"
        case timing = "timing"
        case formation = "formation"
        case cultural = "cultural"
        
        var displayName: String {
            switch self {
            case .strokeOrder: return "Stroke Order"
            case .pressure: return "Pressure"
            case .timing: return "Timing"
            case .formation: return "Formation"
            case .cultural: return "Cultural"
            }
        }
        
        var icon: String {
            switch self {
            case .strokeOrder: return "arrow.triangle.2.circlepath"
            case .pressure: return "hand.draw"
            case .timing: return "timer"
            case .formation: return "pencil.tip"
            case .cultural: return "globe"
            }
        }
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case hintType = "hint_type"
        case content
        case triggerCondition = "trigger_condition"
        case priority
    }
}

// MARK: - Writing Modes

enum WritingMode: String, Codable, CaseIterable {
    case guided = "guided"
    case freeform = "freeform"
    case assessment = "assessment"
    
    var displayName: String {
        switch self {
        case .guided: return "Guided"
        case .freeform: return "Freeform"
        case .assessment: return "Assessment"
        }
    }
    
    var icon: String {
        switch self {
        case .guided: return "hand.draw"
        case .freeform: return "pencil"
        case .assessment: return "checkmark.circle"
        }
    }
    
    var description: String {
        switch self {
        case .guided:
            return "Follow step-by-step guidance with stroke order demonstrations and real-time feedback."
        case .freeform:
            return "Practice writing freely with character recognition and accuracy scoring."
        case .assessment:
            return "Test your writing skills with timed challenges and comprehensive evaluation."
        }
    }
    
    var color: Color {
        switch self {
        case .guided: return .blue
        case .freeform: return .green
        case .assessment: return .orange
        }
    }
}

// MARK: - Extensions for CGPoint Codable Support

extension CGPoint: Codable {
    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(x, forKey: .x)
        try container.encode(y, forKey: .y)
    }
    
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let x = try container.decode(CGFloat.self, forKey: .x)
        let y = try container.decode(CGFloat.self, forKey: .y)
        self.init(x: x, y: y)
    }
    
    private enum CodingKeys: String, CodingKey {
        case x, y
    }
}

extension CGRect: Codable {
    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(origin.x, forKey: .x)
        try container.encode(origin.y, forKey: .y)
        try container.encode(size.width, forKey: .width)
        try container.encode(size.height, forKey: .height)
    }
    
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let x = try container.decode(CGFloat.self, forKey: .x)
        let y = try container.decode(CGFloat.self, forKey: .y)
        let width = try container.decode(CGFloat.self, forKey: .width)
        let height = try container.decode(CGFloat.self, forKey: .height)
        self.init(x: x, y: y, width: width, height: height)
    }
    
    private enum CodingKeys: String, CodingKey {
        case x, y, width, height
    }
}

// MARK: - User Progress Models

/// Represents user's writing progress for individual characters/content
struct UserWritingProgress: Identifiable, Codable {
    let id: UUID
    let userId: UUID
    let characterId: UUID?
    let writingContentId: UUID?
    let practiceSessionId: UUID?
    let accuracyScore: Double?
    let completionTimeSeconds: Int?
    let strokeAccuracy: [StrokeAccuracy]
    let improvementAreas: [ImprovementArea]
    let feedbackProvided: [WritingFeedback]
    let practiceDate: Date
    let deviceType: DeviceType
    let writingMode: WritingMode
    let attemptsCount: Int
    let isCompleted: Bool
    let masteryLevel: MasteryLevel

    enum DeviceType: String, Codable, CaseIterable {
        case iPhone = "iPhone"
        case iPad = "iPad"
        case appleWatch = "AppleWatch"
        case mac = "Mac"

        var displayName: String {
            switch self {
            case .iPhone: return "iPhone"
            case .iPad: return "iPad"
            case .appleWatch: return "Apple Watch"
            case .mac: return "Mac"
            }
        }

        var icon: String {
            switch self {
            case .iPhone: return "iphone"
            case .iPad: return "ipad"
            case .appleWatch: return "applewatch"
            case .mac: return "desktopcomputer"
            }
        }
    }

    enum MasteryLevel: String, Codable, CaseIterable {
        case beginner = "beginner"
        case intermediate = "intermediate"
        case advanced = "advanced"
        case mastered = "mastered"

        var displayName: String {
            switch self {
            case .beginner: return "Beginner"
            case .intermediate: return "Intermediate"
            case .advanced: return "Advanced"
            case .mastered: return "Mastered"
            }
        }

        var color: Color {
            switch self {
            case .beginner: return .red
            case .intermediate: return .orange
            case .advanced: return .blue
            case .mastered: return .green
            }
        }

        var progress: Double {
            switch self {
            case .beginner: return 0.25
            case .intermediate: return 0.5
            case .advanced: return 0.75
            case .mastered: return 1.0
            }
        }
    }

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case characterId = "character_id"
        case writingContentId = "writing_content_id"
        case practiceSessionId = "practice_session_id"
        case accuracyScore = "accuracy_score"
        case completionTimeSeconds = "completion_time_seconds"
        case strokeAccuracy = "stroke_accuracy"
        case improvementAreas = "improvement_areas"
        case feedbackProvided = "feedback_provided"
        case practiceDate = "practice_date"
        case deviceType = "device_type"
        case writingMode = "writing_mode"
        case attemptsCount = "attempts_count"
        case isCompleted = "is_completed"
        case masteryLevel = "mastery_level"
    }
}

/// Represents accuracy data for individual strokes
struct StrokeAccuracy: Codable, Identifiable {
    let id: UUID
    let strokeNumber: Int
    let accuracyPercentage: Double
    let timingAccuracy: Double
    let pressureAccuracy: Double
    let pathDeviation: Double
    let feedback: String?

    enum CodingKeys: String, CodingKey {
        case id
        case strokeNumber = "stroke_number"
        case accuracyPercentage = "accuracy_percentage"
        case timingAccuracy = "timing_accuracy"
        case pressureAccuracy = "pressure_accuracy"
        case pathDeviation = "path_deviation"
        case feedback
    }
}

/// Represents areas where user needs improvement
struct ImprovementArea: Codable, Identifiable {
    let id: UUID
    let areaType: AreaType
    let description: String
    let severity: Severity
    let suggestions: [String]
    let practiceRecommendations: [String]

    enum AreaType: String, Codable, CaseIterable {
        case strokeOrder = "stroke_order"
        case characterFormation = "character_formation"
        case spacing = "spacing"
        case pressure = "pressure"
        case timing = "timing"
        case consistency = "consistency"

        var displayName: String {
            switch self {
            case .strokeOrder: return "Stroke Order"
            case .characterFormation: return "Character Formation"
            case .spacing: return "Spacing"
            case .pressure: return "Pressure Control"
            case .timing: return "Writing Timing"
            case .consistency: return "Consistency"
            }
        }
    }

    enum Severity: String, Codable, CaseIterable {
        case low = "low"
        case medium = "medium"
        case high = "high"
        case critical = "critical"

        var color: Color {
            switch self {
            case .low: return .green
            case .medium: return .yellow
            case .high: return .orange
            case .critical: return .red
            }
        }
    }

    enum CodingKeys: String, CodingKey {
        case id
        case areaType = "area_type"
        case description
        case severity
        case suggestions
        case practiceRecommendations = "practice_recommendations"
    }
}

/// Represents AI-generated feedback for writing practice
struct WritingFeedback: Codable, Identifiable {
    let id: UUID
    let feedbackType: FeedbackType
    let message: String
    let isPositive: Bool
    let actionable: Bool
    let priority: Int
    let relatedStroke: Int?
    let timestamp: Date

    enum FeedbackType: String, Codable, CaseIterable {
        case encouragement = "encouragement"
        case correction = "correction"
        case tip = "tip"
        case achievement = "achievement"
        case warning = "warning"

        var icon: String {
            switch self {
            case .encouragement: return "hand.thumbsup"
            case .correction: return "exclamationmark.triangle"
            case .tip: return "lightbulb"
            case .achievement: return "star"
            case .warning: return "exclamationmark.circle"
            }
        }

        var color: Color {
            switch self {
            case .encouragement: return .green
            case .correction: return .orange
            case .tip: return .blue
            case .achievement: return .yellow
            case .warning: return .red
            }
        }
    }

    enum CodingKeys: String, CodingKey {
        case id
        case feedbackType = "feedback_type"
        case message
        case isPositive = "is_positive"
        case actionable
        case priority
        case relatedStroke = "related_stroke"
        case timestamp
    }
}

// MARK: - Writing Session Models

/// Represents a complete writing practice session
struct WritingSession: Identifiable, Codable {
    let id: UUID
    let userId: UUID
    let sessionType: SessionType
    let lessonId: String?
    let totalCharactersPracticed: Int
    let totalAccuracyScore: Double?
    let sessionDurationSeconds: Int?
    let charactersMastered: Int
    let improvementAreas: [ImprovementArea]
    let sessionNotes: String?
    let deviceType: UserWritingProgress.DeviceType
    let startedAt: Date
    let completedAt: Date?
    let isCompleted: Bool

    enum SessionType: String, Codable, CaseIterable {
        case characterPractice = "character_practice"
        case wordPractice = "word_practice"
        case lessonWriting = "lesson_writing"
        case freePractice = "free_practice"

        var displayName: String {
            switch self {
            case .characterPractice: return "Character Practice"
            case .wordPractice: return "Word Practice"
            case .lessonWriting: return "Lesson Writing"
            case .freePractice: return "Free Practice"
            }
        }

        var icon: String {
            switch self {
            case .characterPractice: return "textformat"
            case .wordPractice: return "textformat.abc"
            case .lessonWriting: return "book"
            case .freePractice: return "pencil"
            }
        }
    }

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case sessionType = "session_type"
        case lessonId = "lesson_id"
        case totalCharactersPracticed = "total_characters_practiced"
        case totalAccuracyScore = "total_accuracy_score"
        case sessionDurationSeconds = "session_duration_seconds"
        case charactersMastered = "characters_mastered"
        case improvementAreas = "improvement_areas"
        case sessionNotes = "session_notes"
        case deviceType = "device_type"
        case startedAt = "started_at"
        case completedAt = "completed_at"
        case isCompleted = "is_completed"
    }
}

/// Represents writing achievements and milestones
struct WritingAchievement: Identifiable, Codable {
    let id: UUID
    let userId: UUID
    let achievementType: AchievementType
    let achievementName: String
    let achievementDescription: String?
    let criteriaMet: [String: String] // Flexible criteria storage
    let pointsAwarded: Int
    let badgeIcon: String?
    let unlockedAt: Date
    let isActive: Bool

    enum AchievementType: String, Codable, CaseIterable {
        case firstCharacter = "first_character"
        case characterMastery = "character_mastery"
        case streakMilestone = "streak_milestone"
        case accuracyMilestone = "accuracy_milestone"
        case speedMilestone = "speed_milestone"
        case lessonCompletion = "lesson_completion"
        case perfectSession = "perfect_session"
        case consistencyAward = "consistency_award"

        var displayName: String {
            switch self {
            case .firstCharacter: return "First Character"
            case .characterMastery: return "Character Master"
            case .streakMilestone: return "Streak Champion"
            case .accuracyMilestone: return "Accuracy Expert"
            case .speedMilestone: return "Speed Writer"
            case .lessonCompletion: return "Lesson Complete"
            case .perfectSession: return "Perfect Session"
            case .consistencyAward: return "Consistency Award"
            }
        }

        var defaultIcon: String {
            switch self {
            case .firstCharacter: return "star.fill"
            case .characterMastery: return "crown.fill"
            case .streakMilestone: return "flame.fill"
            case .accuracyMilestone: return "target"
            case .speedMilestone: return "bolt.fill"
            case .lessonCompletion: return "checkmark.circle.fill"
            case .perfectSession: return "sparkles"
            case .consistencyAward: return "calendar.badge.checkmark"
            }
        }

        var color: Color {
            switch self {
            case .firstCharacter: return .yellow
            case .characterMastery: return .purple
            case .streakMilestone: return .orange
            case .accuracyMilestone: return .blue
            case .speedMilestone: return .green
            case .lessonCompletion: return .mint
            case .perfectSession: return .pink
            case .consistencyAward: return .indigo
            }
        }
    }

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case achievementType = "achievement_type"
        case achievementName = "achievement_name"
        case achievementDescription = "achievement_description"
        case criteriaMet = "criteria_met"
        case pointsAwarded = "points_awarded"
        case badgeIcon = "badge_icon"
        case unlockedAt = "unlocked_at"
        case isActive = "is_active"
    }
}

// MARK: - Helper Extensions

extension TamilCharacter {
    /// Returns true if this is a basic vowel character
    var isBasicVowel: Bool {
        return characterType == .vowel && difficultyLevel <= 2
    }

    /// Returns true if this is a basic consonant character
    var isBasicConsonant: Bool {
        return characterType == .consonant && difficultyLevel <= 2
    }

    /// Returns the estimated learning time in minutes
    var estimatedLearningTime: Int {
        switch writingComplexity {
        case .simple: return 5
        case .moderate: return 10
        case .complex: return 15
        }
    }
}

extension WritingMode {
    /// Returns the recommended practice duration for this mode
    var recommendedDuration: Int {
        switch self {
        case .guided: return 10 // minutes
        case .freeform: return 15
        case .assessment: return 20
        }
    }
}

extension UserWritingProgress {
    /// Returns true if the user has mastered this character/content
    var isMastered: Bool {
        return masteryLevel == .mastered && (accuracyScore ?? 0) >= 90.0
    }

    /// Returns the overall progress percentage
    var progressPercentage: Double {
        return masteryLevel.progress * 100
    }
}
