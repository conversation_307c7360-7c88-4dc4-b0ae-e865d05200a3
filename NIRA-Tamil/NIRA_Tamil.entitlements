<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>aps-environment</key>
	<string>development</string>
	<key>com.apple.developer.healthkit</key>
	<true/>
	<key>com.apple.developer.healthkit.access</key>
	<array/>
	<key>com.apple.developer.icloud-container-identifiers</key>
	<array/>
	<key>com.apple.developer.icloud-services</key>
	<array>
		<string>CloudKit</string>
		<string>CloudDocuments</string>
	</array>
	<key>com.apple.developer.ubiquity-container-identifiers</key>
	<array/>
	<key>com.apple.developer.ubiquity-kvstore-identifier</key>
	<string>$(TeamIdentifierPrefix)$(CFBundleIdentifier)</string>
	<key>com.apple.security.app-sandbox</key>
	<true/>
	<key>com.apple.security.files.user-selected.read-only</key>
	<true/>

	<!-- TEMPORARILY DISABLED - These entitlements require iOS 18 beta or special approval -->
	<!-- Enable these when you have access to iOS 18 beta provisioning profiles -->

	<!-- Basic iOS 18+ Features
	<key>com.apple.developer.appintents-extension</key>
	<true/>
	<key>com.apple.developer.live-activities</key>
	<true/>
	<key>com.apple.developer.controls</key>
	<true/>
	<key>com.apple.developer.widgets</key>
	<true/>
	-->

	<!-- Camera and Photo Library Access
	<key>com.apple.developer.camera</key>
	<true/>
	<key>com.apple.developer.photo-library</key>
	<true/>
	-->

	<!-- Machine Learning and Vision Framework
	<key>com.apple.developer.coreml</key>
	<true/>
	<key>com.apple.developer.vision</key>
	<true/>
	-->

	<!-- Background Processing
	<key>com.apple.developer.background-processing</key>
	<array>
		<string>com.nira-tamil.content-sync</string>
		<string>com.nira-tamil.model-update</string>
	</array>
	-->

	<!-- Siri and Shortcuts Integration - Only enable if you have Siri capability in portal
	<key>com.apple.developer.siri</key>
	<true/>
	<key>com.apple.developer.shortcuts</key>
	<true/>
	-->

	<!-- App Groups for Widget and Extension Data Sharing - Only if configured in portal -->
	<!--
	<key>com.apple.security.application-groups</key>
	<array>
		<string>group.com.nira-tamil.shared</string>
	</array>
	-->

	<!-- Keychain Sharing for Secure API Key Storage - Only if configured in portal -->
	<!--
	<key>keychain-access-groups</key>
	<array>
		<string>$(AppIdentifierPrefix)com.nira-tamil.keychain</string>
	</array>
	-->

	<!-- BETA/FUTURE FEATURES - Uncomment when available -->
	<!-- Apple Intelligence (requires special approval)
	<key>com.apple.developer.apple-intelligence</key>
	<true/>
	-->

	<!-- iOS 26 Visual Intelligence Features (not yet available)
	<key>com.apple.developer.visual-intelligence</key>
	<true/>
	<key>com.apple.developer.visual-intelligence.content-search</key>
	<true/>
	<key>com.apple.developer.visual-intelligence.text-recognition</key>
	<true/>
	<key>com.apple.developer.visual-intelligence.cultural-content</key>
	<true/>
	-->
</dict>
</plist>
