//
//  TamilWritingAIFeedbackService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import Foundation
import CoreML
import Vision
import Combine
import UIKit

@MainActor
class TamilWritingAIFeedbackService: ObservableObject {
    static let shared = TamilWritingAIFeedbackService()
    
    @Published var isAnalyzing = false
    @Published var feedbackResults: [WritingFeedbackResult] = []
    @Published var personalizedSuggestions: [PersonalizedSuggestion] = []
    @Published var learningInsights: [LearningInsight] = []
    
    private var coreMLModel: MLModel?
    private var visionModel: VNCoreMLModel?
    private let feedbackQueue = DispatchQueue(label: "com.nira.tamil.ai.feedback", qos: .userInitiated)
    
    // MARK: - AI Models
    
    enum AIModelType {
        case strokeAnalysis
        case characterRecognition
        case writingQuality
        case progressPrediction
        case personalizedRecommendation
    }
    
    struct WritingFeedbackResult: Identifiable, Codable {
        let id = UUID()
        let characterId: UUID
        let analysisTimestamp: Date
        let overallScore: Double
        let feedbackCategories: [FeedbackCategory]
        let improvementSuggestions: [ImprovementSuggestion]
        let strengthAreas: [StrengthArea]
        let nextSteps: [NextStep]
        let confidenceLevel: Double
        
        struct FeedbackCategory: Identifiable, Codable {
            let id = UUID()
            let category: CategoryType
            let score: Double
            let analysis: String
            let specificIssues: [String]
            let recommendations: [String]
            
            enum CategoryType: String, Codable, CaseIterable {
                case strokeOrder = "stroke_order"
                case characterShape = "character_shape"
                case proportions = "proportions"
                case consistency = "consistency"
                case fluency = "fluency"
                case culturalAccuracy = "cultural_accuracy"
                
                var displayName: String {
                    switch self {
                    case .strokeOrder: return "Stroke Order"
                    case .characterShape: return "Character Shape"
                    case .proportions: return "Proportions"
                    case .consistency: return "Consistency"
                    case .fluency: return "Writing Fluency"
                    case .culturalAccuracy: return "Cultural Accuracy"
                    }
                }
                
                var icon: String {
                    switch self {
                    case .strokeOrder: return "arrow.triangle.turn.up.right.diamond"
                    case .characterShape: return "scribble.variable"
                    case .proportions: return "rectangle.ratio.3.to.4"
                    case .consistency: return "equal"
                    case .fluency: return "speedometer"
                    case .culturalAccuracy: return "globe.asia.australia"
                    }
                }
                
                var color: Color {
                    switch self {
                    case .strokeOrder: return .blue
                    case .characterShape: return .green
                    case .proportions: return .orange
                    case .consistency: return .purple
                    case .fluency: return .red
                    case .culturalAccuracy: return .indigo
                    }
                }
            }
        }
        
        struct ImprovementSuggestion: Identifiable, Codable {
            let id = UUID()
            let priority: Priority
            let title: String
            let description: String
            let actionSteps: [String]
            let estimatedImpact: Double
            let practiceExercises: [String]
            
            enum Priority: String, Codable, CaseIterable {
                case high = "high"
                case medium = "medium"
                case low = "low"
                
                var color: Color {
                    switch self {
                    case .high: return .red
                    case .medium: return .orange
                    case .low: return .blue
                    }
                }
            }
        }
        
        struct StrengthArea: Identifiable, Codable {
            let id = UUID()
            let area: String
            let description: String
            let score: Double
            let encouragement: String
        }
        
        struct NextStep: Identifiable, Codable {
            let id = UUID()
            let stepNumber: Int
            let title: String
            let description: String
            let estimatedTime: Int // minutes
            let difficulty: Difficulty
            
            enum Difficulty: String, Codable, CaseIterable {
                case easy = "easy"
                case medium = "medium"
                case hard = "hard"
                
                var color: Color {
                    switch self {
                    case .easy: return .green
                    case .medium: return .orange
                    case .hard: return .red
                    }
                }
            }
        }
    }
    
    struct PersonalizedSuggestion: Identifiable, Codable {
        let id = UUID()
        let userId: String
        let suggestionType: SuggestionType
        let title: String
        let description: String
        let reasoning: String
        let actionItems: [ActionItem]
        let expectedOutcome: String
        let confidence: Double
        let createdAt: Date
        let expiresAt: Date?
        
        enum SuggestionType: String, Codable, CaseIterable {
            case practiceSchedule = "practice_schedule"
            case characterFocus = "character_focus"
            case difficultyAdjustment = "difficulty_adjustment"
            case learningPath = "learning_path"
            case motivationalBoost = "motivational_boost"
            
            var icon: String {
                switch self {
                case .practiceSchedule: return "calendar"
                case .characterFocus: return "target"
                case .difficultyAdjustment: return "slider.horizontal.3"
                case .learningPath: return "map"
                case .motivationalBoost: return "star.fill"
                }
            }
        }
        
        struct ActionItem: Identifiable, Codable {
            let id = UUID()
            let action: String
            let estimatedTime: Int
            let priority: Int
        }
    }
    
    struct LearningInsight: Identifiable, Codable {
        let id = UUID()
        let insightType: InsightType
        let title: String
        let description: String
        let dataPoints: [DataPoint]
        let trend: Trend
        let recommendation: String
        let confidence: Double
        
        enum InsightType: String, Codable, CaseIterable {
            case progressPattern = "progress_pattern"
            case learningVelocity = "learning_velocity"
            case difficultyPreference = "difficulty_preference"
            case timeOptimization = "time_optimization"
            case strengthIdentification = "strength_identification"
            
            var displayName: String {
                switch self {
                case .progressPattern: return "Progress Pattern"
                case .learningVelocity: return "Learning Speed"
                case .difficultyPreference: return "Difficulty Preference"
                case .timeOptimization: return "Time Optimization"
                case .strengthIdentification: return "Strength Areas"
                }
            }
        }
        
        struct DataPoint: Codable {
            let label: String
            let value: Double
            let timestamp: Date
        }
        
        enum Trend: String, Codable {
            case improving = "improving"
            case stable = "stable"
            case declining = "declining"
            case fluctuating = "fluctuating"
            
            var color: Color {
                switch self {
                case .improving: return .green
                case .stable: return .blue
                case .declining: return .red
                case .fluctuating: return .orange
                }
            }
            
            var icon: String {
                switch self {
                case .improving: return "arrow.up.right"
                case .stable: return "arrow.right"
                case .declining: return "arrow.down.right"
                case .fluctuating: return "arrow.up.and.down"
                }
            }
        }
    }
    
    private init() {
        loadAIModels()
    }
    
    // MARK: - AI Model Loading
    
    private func loadAIModels() {
        Task {
            await loadCoreMLModels()
        }
    }
    
    private func loadCoreMLModels() async {
        do {
            // Load Tamil character recognition model
            if let modelURL = Bundle.main.url(forResource: "TamilCharacterRecognition", withExtension: "mlmodelc") {
                coreMLModel = try MLModel(contentsOf: modelURL)
                visionModel = try VNCoreMLModel(for: coreMLModel!)
                print("✅ Tamil AI models loaded successfully")
            } else {
                print("⚠️ Using mock AI models for development")
                setupMockModels()
            }
        } catch {
            print("❌ Failed to load AI models: \(error)")
            setupMockModels()
        }
    }
    
    private func setupMockModels() {
        // Setup mock models for development
        print("🔧 Mock AI models configured")
    }
    
    // MARK: - AI-Powered Feedback Analysis
    
    /// Analyze writing sample and provide comprehensive AI feedback
    func analyzeWritingSample(
        character: TamilCharacter,
        userDrawing: UIImage,
        strokeData: [StrokeData],
        writingContext: WritingContext
    ) async -> WritingFeedbackResult {
        
        isAnalyzing = true
        defer { isAnalyzing = false }
        
        return await withTaskGroup(of: WritingFeedbackResult.FeedbackCategory?.self) { group in
            
            // Analyze stroke order
            group.addTask {
                await self.analyzeStrokeOrder(character: character, strokeData: strokeData)
            }
            
            // Analyze character shape
            group.addTask {
                await self.analyzeCharacterShape(character: character, userDrawing: userDrawing)
            }
            
            // Analyze proportions
            group.addTask {
                await self.analyzeProportions(character: character, userDrawing: userDrawing)
            }
            
            // Analyze consistency
            group.addTask {
                await self.analyzeConsistency(strokeData: strokeData, context: writingContext)
            }
            
            // Analyze fluency
            group.addTask {
                await self.analyzeFluency(strokeData: strokeData, context: writingContext)
            }
            
            // Analyze cultural accuracy
            group.addTask {
                await self.analyzeCulturalAccuracy(character: character, userDrawing: userDrawing)
            }
            
            var categories: [WritingFeedbackResult.FeedbackCategory] = []
            
            for await category in group {
                if let category = category {
                    categories.append(category)
                }
            }
            
            // Generate comprehensive feedback
            let overallScore = calculateOverallScore(categories: categories)
            let suggestions = generateImprovementSuggestions(categories: categories, character: character)
            let strengths = identifyStrengthAreas(categories: categories)
            let nextSteps = generateNextSteps(categories: categories, character: character)
            
            return WritingFeedbackResult(
                characterId: character.id,
                analysisTimestamp: Date(),
                overallScore: overallScore,
                feedbackCategories: categories,
                improvementSuggestions: suggestions,
                strengthAreas: strengths,
                nextSteps: nextSteps,
                confidenceLevel: calculateConfidenceLevel(categories: categories)
            )
        }
    }
    
    // MARK: - Individual Analysis Methods
    
    private func analyzeStrokeOrder(character: TamilCharacter, strokeData: [StrokeData]) async -> WritingFeedbackResult.FeedbackCategory {
        
        // AI analysis of stroke order correctness
        let correctOrder = getCorrectStrokeOrder(for: character)
        let userOrder = extractStrokeOrder(from: strokeData)
        
        let accuracy = calculateStrokeOrderAccuracy(correct: correctOrder, user: userOrder)
        let issues = identifyStrokeOrderIssues(correct: correctOrder, user: userOrder)
        let recommendations = generateStrokeOrderRecommendations(issues: issues)
        
        return WritingFeedbackResult.FeedbackCategory(
            category: .strokeOrder,
            score: accuracy,
            analysis: generateStrokeOrderAnalysis(accuracy: accuracy, issues: issues),
            specificIssues: issues,
            recommendations: recommendations
        )
    }
    
    private func analyzeCharacterShape(character: TamilCharacter, userDrawing: UIImage) async -> WritingFeedbackResult.FeedbackCategory {
        
        // Use Vision framework for shape analysis
        let shapeAccuracy = await performVisionShapeAnalysis(userDrawing: userDrawing, targetCharacter: character)
        let shapeIssues = identifyShapeIssues(accuracy: shapeAccuracy)
        let recommendations = generateShapeRecommendations(issues: shapeIssues)
        
        return WritingFeedbackResult.FeedbackCategory(
            category: .characterShape,
            score: shapeAccuracy,
            analysis: "Character shape analysis using computer vision",
            specificIssues: shapeIssues,
            recommendations: recommendations
        )
    }
    
    private func analyzeProportions(character: TamilCharacter, userDrawing: UIImage) async -> WritingFeedbackResult.FeedbackCategory {
        
        let proportionScore = await analyzeCharacterProportions(userDrawing: userDrawing, character: character)
        let proportionIssues = identifyProportionIssues(score: proportionScore)
        let recommendations = generateProportionRecommendations(issues: proportionIssues)
        
        return WritingFeedbackResult.FeedbackCategory(
            category: .proportions,
            score: proportionScore,
            analysis: "Proportion analysis of character elements",
            specificIssues: proportionIssues,
            recommendations: recommendations
        )
    }
    
    private func analyzeConsistency(strokeData: [StrokeData], context: WritingContext) async -> WritingFeedbackResult.FeedbackCategory {
        
        let consistencyScore = calculateConsistencyScore(strokeData: strokeData, context: context)
        let consistencyIssues = identifyConsistencyIssues(score: consistencyScore)
        let recommendations = generateConsistencyRecommendations(issues: consistencyIssues)
        
        return WritingFeedbackResult.FeedbackCategory(
            category: .consistency,
            score: consistencyScore,
            analysis: "Consistency analysis across multiple attempts",
            specificIssues: consistencyIssues,
            recommendations: recommendations
        )
    }
    
    private func analyzeFluency(strokeData: [StrokeData], context: WritingContext) async -> WritingFeedbackResult.FeedbackCategory {
        
        let fluencyScore = calculateFluencyScore(strokeData: strokeData, context: context)
        let fluencyIssues = identifyFluencyIssues(score: fluencyScore)
        let recommendations = generateFluencyRecommendations(issues: fluencyIssues)
        
        return WritingFeedbackResult.FeedbackCategory(
            category: .fluency,
            score: fluencyScore,
            analysis: "Writing fluency and speed analysis",
            specificIssues: fluencyIssues,
            recommendations: recommendations
        )
    }
    
    private func analyzeCulturalAccuracy(character: TamilCharacter, userDrawing: UIImage) async -> WritingFeedbackResult.FeedbackCategory {
        
        let culturalScore = await analyzeCulturalElements(character: character, userDrawing: userDrawing)
        let culturalIssues = identifyCulturalIssues(score: culturalScore)
        let recommendations = generateCulturalRecommendations(issues: culturalIssues)
        
        return WritingFeedbackResult.FeedbackCategory(
            category: .culturalAccuracy,
            score: culturalScore,
            analysis: "Cultural and traditional writing style analysis",
            specificIssues: culturalIssues,
            recommendations: recommendations
        )
    }
    
    // MARK: - Vision Framework Integration
    
    private func performVisionShapeAnalysis(userDrawing: UIImage, targetCharacter: TamilCharacter) async -> Double {
        
        return await withCheckedContinuation { continuation in
            guard let cgImage = userDrawing.cgImage,
                  let visionModel = visionModel else {
                continuation.resume(returning: 0.75) // Mock score
                return
            }
            
            let request = VNCoreMLRequest(model: visionModel) { request, error in
                if let error = error {
                    print("Vision analysis error: \(error)")
                    continuation.resume(returning: 0.75)
                    return
                }
                
                guard let results = request.results as? [VNClassificationObservation],
                      let topResult = results.first else {
                    continuation.resume(returning: 0.75)
                    return
                }
                
                // Calculate accuracy based on confidence and character match
                let accuracy = topResult.identifier == targetCharacter.character ? 
                    Double(topResult.confidence) : Double(topResult.confidence) * 0.5
                
                continuation.resume(returning: accuracy)
            }
            
            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            
            do {
                try handler.perform([request])
            } catch {
                print("Vision request error: \(error)")
                continuation.resume(returning: 0.75)
            }
        }
    }
    
    // MARK: - Helper Methods (Mock implementations for development)
    
    private func getCorrectStrokeOrder(for character: TamilCharacter) -> [Int] {
        // Return correct stroke order sequence
        return Array(1...character.strokeCount)
    }
    
    private func extractStrokeOrder(from strokeData: [StrokeData]) -> [Int] {
        // Extract stroke order from user's drawing
        return strokeData.enumerated().map { $0.offset + 1 }
    }
    
    private func calculateStrokeOrderAccuracy(correct: [Int], user: [Int]) -> Double {
        let matches = zip(correct, user).filter { $0.0 == $0.1 }.count
        return Double(matches) / Double(max(correct.count, user.count))
    }
    
    private func identifyStrokeOrderIssues(correct: [Int], user: [Int]) -> [String] {
        var issues: [String] = []
        
        if user.count != correct.count {
            issues.append("Incorrect number of strokes")
        }
        
        for (index, (correctStroke, userStroke)) in zip(correct, user).enumerated() {
            if correctStroke != userStroke {
                issues.append("Stroke \(index + 1) order incorrect")
            }
        }
        
        return issues
    }
    
    private func generateStrokeOrderRecommendations(issues: [String]) -> [String] {
        var recommendations: [String] = []
        
        for issue in issues {
            if issue.contains("number of strokes") {
                recommendations.append("Practice counting strokes before writing")
            } else if issue.contains("order incorrect") {
                recommendations.append("Follow the animated stroke order guide")
            }
        }
        
        return recommendations
    }
    
    private func generateStrokeOrderAnalysis(accuracy: Double, issues: [String]) -> String {
        if accuracy > 0.9 {
            return "Excellent stroke order! You're following the traditional Tamil writing sequence correctly."
        } else if accuracy > 0.7 {
            return "Good stroke order with minor issues. Focus on the specific strokes that need improvement."
        } else {
            return "Stroke order needs improvement. Practice with the guided mode to learn the correct sequence."
        }
    }
    
    private func calculateOverallScore(categories: [WritingFeedbackResult.FeedbackCategory]) -> Double {
        let totalScore = categories.reduce(0.0) { $0 + $1.score }
        return totalScore / Double(categories.count)
    }
    
    private func calculateConfidenceLevel(categories: [WritingFeedbackResult.FeedbackCategory]) -> Double {
        // Calculate confidence based on analysis quality
        return 0.85 // Mock confidence level
    }
    
    // Additional helper methods would be implemented here...
    private func identifyShapeIssues(accuracy: Double) -> [String] { return [] }
    private func generateShapeRecommendations(issues: [String]) -> [String] { return [] }
    private func analyzeCharacterProportions(userDrawing: UIImage, character: TamilCharacter) async -> Double { return 0.8 }
    private func identifyProportionIssues(score: Double) -> [String] { return [] }
    private func generateProportionRecommendations(issues: [String]) -> [String] { return [] }
    private func calculateConsistencyScore(strokeData: [StrokeData], context: WritingContext) -> Double { return 0.75 }
    private func identifyConsistencyIssues(score: Double) -> [String] { return [] }
    private func generateConsistencyRecommendations(issues: [String]) -> [String] { return [] }
    private func calculateFluencyScore(strokeData: [StrokeData], context: WritingContext) -> Double { return 0.7 }
    private func identifyFluencyIssues(score: Double) -> [String] { return [] }
    private func generateFluencyRecommendations(issues: [String]) -> [String] { return [] }
    private func analyzeCulturalElements(character: TamilCharacter, userDrawing: UIImage) async -> Double { return 0.85 }
    private func identifyCulturalIssues(score: Double) -> [String] { return [] }
    private func generateCulturalRecommendations(issues: [String]) -> [String] { return [] }
    
    private func generateImprovementSuggestions(categories: [WritingFeedbackResult.FeedbackCategory], character: TamilCharacter) -> [WritingFeedbackResult.ImprovementSuggestion] {
        return []
    }
    
    private func identifyStrengthAreas(categories: [WritingFeedbackResult.FeedbackCategory]) -> [WritingFeedbackResult.StrengthArea] {
        return []
    }
    
    private func generateNextSteps(categories: [WritingFeedbackResult.FeedbackCategory], character: TamilCharacter) -> [WritingFeedbackResult.NextStep] {
        return []
    }
}

// MARK: - Supporting Data Models

struct StrokeData: Codable {
    let strokeIndex: Int
    let points: [CGPoint]
    let timestamp: Date
    let pressure: [Float]
    let velocity: [Float]
}

struct WritingContext: Codable {
    let sessionType: String
    let previousAttempts: Int
    let userLevel: String
    let timeSpent: TimeInterval
}

// MARK: - Personalized Learning Engine

extension TamilWritingAIFeedbackService {

    /// Generate personalized suggestions based on user's writing history and patterns
    func generatePersonalizedSuggestions(userId: String, writingHistory: [WritingFeedbackResult]) async -> [PersonalizedSuggestion] {

        var suggestions: [PersonalizedSuggestion] = []

        // Analyze learning patterns
        let patterns = analyzeLearningPatterns(writingHistory: writingHistory)

        // Generate practice schedule suggestions
        if let scheduleOptimization = optimizePracticeSchedule(patterns: patterns) {
            suggestions.append(scheduleOptimization)
        }

        // Generate character focus suggestions
        if let characterFocus = identifyCharacterFocusAreas(patterns: patterns) {
            suggestions.append(characterFocus)
        }

        // Generate difficulty adjustment suggestions
        if let difficultyAdjustment = suggestDifficultyAdjustment(patterns: patterns) {
            suggestions.append(difficultyAdjustment)
        }

        // Generate motivational suggestions
        if let motivationalBoost = generateMotivationalSuggestion(patterns: patterns) {
            suggestions.append(motivationalBoost)
        }

        personalizedSuggestions = suggestions
        return suggestions
    }

    /// Generate learning insights from user data
    func generateLearningInsights(userId: String, writingHistory: [WritingFeedbackResult]) async -> [LearningInsight] {

        var insights: [LearningInsight] = []

        // Progress pattern analysis
        if let progressInsight = analyzeProgressPattern(writingHistory: writingHistory) {
            insights.append(progressInsight)
        }

        // Learning velocity analysis
        if let velocityInsight = analyzeLearningVelocity(writingHistory: writingHistory) {
            insights.append(velocityInsight)
        }

        // Time optimization analysis
        if let timeInsight = analyzeTimeOptimization(writingHistory: writingHistory) {
            insights.append(timeInsight)
        }

        // Strength identification
        if let strengthInsight = analyzeStrengthAreas(writingHistory: writingHistory) {
            insights.append(strengthInsight)
        }

        learningInsights = insights
        return insights
    }

    // MARK: - Pattern Analysis Methods

    private func analyzeLearningPatterns(writingHistory: [WritingFeedbackResult]) -> LearningPatterns {
        let recentResults = writingHistory.suffix(20)

        let averageScore = recentResults.map { $0.overallScore }.reduce(0, +) / Double(recentResults.count)
        let scoreVariance = calculateVariance(scores: recentResults.map { $0.overallScore })
        let improvementRate = calculateImprovementRate(results: Array(recentResults))
        let consistencyScore = calculateConsistencyFromHistory(results: Array(recentResults))

        return LearningPatterns(
            averageScore: averageScore,
            scoreVariance: scoreVariance,
            improvementRate: improvementRate,
            consistencyScore: consistencyScore,
            strongCategories: identifyStrongCategories(results: Array(recentResults)),
            weakCategories: identifyWeakCategories(results: Array(recentResults)),
            practiceFrequency: calculatePracticeFrequency(results: Array(recentResults))
        )
    }

    private func optimizePracticeSchedule(patterns: LearningPatterns) -> PersonalizedSuggestion? {
        guard patterns.practiceFrequency < 0.7 else { return nil }

        let actionItems = [
            PersonalizedSuggestion.ActionItem(action: "Practice 15 minutes daily", estimatedTime: 15, priority: 1),
            PersonalizedSuggestion.ActionItem(action: "Focus on weak categories", estimatedTime: 10, priority: 2),
            PersonalizedSuggestion.ActionItem(action: "Review previous characters", estimatedTime: 5, priority: 3)
        ]

        return PersonalizedSuggestion(
            userId: "current-user",
            suggestionType: .practiceSchedule,
            title: "Optimize Your Practice Schedule",
            description: "Based on your learning patterns, a more consistent practice schedule could improve your progress by 40%.",
            reasoning: "Your current practice frequency is \(Int(patterns.practiceFrequency * 100))%. Regular practice leads to better retention and faster improvement.",
            actionItems: actionItems,
            expectedOutcome: "Improved consistency and 40% faster character mastery",
            confidence: 0.85,
            createdAt: Date(),
            expiresAt: Calendar.current.date(byAdding: .day, value: 7, to: Date())
        )
    }

    private func identifyCharacterFocusAreas(patterns: LearningPatterns) -> PersonalizedSuggestion? {
        guard !patterns.weakCategories.isEmpty else { return nil }

        let focusArea = patterns.weakCategories.first!

        let actionItems = [
            PersonalizedSuggestion.ActionItem(action: "Practice \(focusArea) exercises", estimatedTime: 20, priority: 1),
            PersonalizedSuggestion.ActionItem(action: "Review \(focusArea) guidelines", estimatedTime: 5, priority: 2),
            PersonalizedSuggestion.ActionItem(action: "Use guided mode for \(focusArea)", estimatedTime: 15, priority: 3)
        ]

        return PersonalizedSuggestion(
            userId: "current-user",
            suggestionType: .characterFocus,
            title: "Focus on \(focusArea.capitalized)",
            description: "Your \(focusArea) skills need attention. Targeted practice in this area will significantly improve your overall writing.",
            reasoning: "Analysis shows \(focusArea) is your weakest area with room for 30% improvement.",
            actionItems: actionItems,
            expectedOutcome: "30% improvement in \(focusArea) accuracy",
            confidence: 0.78,
            createdAt: Date(),
            expiresAt: Calendar.current.date(byAdding: .day, value: 5, to: Date())
        )
    }

    private func suggestDifficultyAdjustment(patterns: LearningPatterns) -> PersonalizedSuggestion? {
        if patterns.averageScore > 0.9 && patterns.consistencyScore > 0.85 {
            // Suggest increasing difficulty
            let actionItems = [
                PersonalizedSuggestion.ActionItem(action: "Try advanced characters", estimatedTime: 25, priority: 1),
                PersonalizedSuggestion.ActionItem(action: "Practice complex combinations", estimatedTime: 20, priority: 2)
            ]

            return PersonalizedSuggestion(
                userId: "current-user",
                suggestionType: .difficultyAdjustment,
                title: "Ready for Advanced Challenges",
                description: "Your consistent high performance indicates you're ready for more challenging Tamil characters.",
                reasoning: "Average score of \(Int(patterns.averageScore * 100))% with high consistency suggests readiness for advancement.",
                actionItems: actionItems,
                expectedOutcome: "Continued growth with appropriate challenge level",
                confidence: 0.92,
                createdAt: Date(),
                expiresAt: nil
            )
        } else if patterns.averageScore < 0.6 {
            // Suggest reducing difficulty
            let actionItems = [
                PersonalizedSuggestion.ActionItem(action: "Focus on basic characters", estimatedTime: 20, priority: 1),
                PersonalizedSuggestion.ActionItem(action: "Use more guidance", estimatedTime: 15, priority: 2)
            ]

            return PersonalizedSuggestion(
                userId: "current-user",
                suggestionType: .difficultyAdjustment,
                title: "Strengthen Your Foundation",
                description: "Focusing on fundamental characters will build confidence and improve your overall writing skills.",
                reasoning: "Current performance suggests benefit from reinforcing basic skills before advancing.",
                actionItems: actionItems,
                expectedOutcome: "Stronger foundation and improved confidence",
                confidence: 0.88,
                createdAt: Date(),
                expiresAt: nil
            )
        }

        return nil
    }

    private func generateMotivationalSuggestion(patterns: LearningPatterns) -> PersonalizedSuggestion? {
        if patterns.improvementRate > 0 {
            let actionItems = [
                PersonalizedSuggestion.ActionItem(action: "Celebrate your progress", estimatedTime: 2, priority: 1),
                PersonalizedSuggestion.ActionItem(action: "Share achievement with friends", estimatedTime: 3, priority: 2)
            ]

            return PersonalizedSuggestion(
                userId: "current-user",
                suggestionType: .motivationalBoost,
                title: "Celebrate Your Progress!",
                description: "You've shown consistent improvement in your Tamil writing. Your dedication is paying off!",
                reasoning: "Improvement rate of \(Int(patterns.improvementRate * 100))% shows excellent progress.",
                actionItems: actionItems,
                expectedOutcome: "Increased motivation and continued engagement",
                confidence: 0.95,
                createdAt: Date(),
                expiresAt: Calendar.current.date(byAdding: .day, value: 3, to: Date())
            )
        }

        return nil
    }

    // MARK: - Insight Generation Methods

    private func analyzeProgressPattern(writingHistory: [WritingFeedbackResult]) -> LearningInsight? {
        let recentScores = writingHistory.suffix(10).map { $0.overallScore }
        guard recentScores.count >= 5 else { return nil }

        let trend = calculateTrend(scores: Array(recentScores))
        let dataPoints = recentScores.enumerated().map { index, score in
            LearningInsight.DataPoint(
                label: "Session \(index + 1)",
                value: score,
                timestamp: Date().addingTimeInterval(-Double(recentScores.count - index) * 86400)
            )
        }

        return LearningInsight(
            insightType: .progressPattern,
            title: "Your Learning Trajectory",
            description: generateProgressDescription(trend: trend, scores: Array(recentScores)),
            dataPoints: dataPoints,
            trend: trend,
            recommendation: generateProgressRecommendation(trend: trend),
            confidence: 0.87
        )
    }

    private func analyzeLearningVelocity(writingHistory: [WritingFeedbackResult]) -> LearningInsight? {
        // Calculate learning velocity based on improvement over time
        let velocityData = calculateLearningVelocity(history: writingHistory)

        return LearningInsight(
            insightType: .learningVelocity,
            title: "Learning Speed Analysis",
            description: "Your learning velocity shows how quickly you master new characters.",
            dataPoints: velocityData,
            trend: .improving,
            recommendation: "Maintain current pace for optimal retention",
            confidence: 0.82
        )
    }

    private func analyzeTimeOptimization(writingHistory: [WritingFeedbackResult]) -> LearningInsight? {
        // Analyze optimal practice times and durations
        return LearningInsight(
            insightType: .timeOptimization,
            title: "Optimal Practice Times",
            description: "Analysis of when you perform best during practice sessions.",
            dataPoints: [],
            trend: .stable,
            recommendation: "Practice during your peak performance hours for better results",
            confidence: 0.75
        )
    }

    private func analyzeStrengthAreas(writingHistory: [WritingFeedbackResult]) -> LearningInsight? {
        // Identify user's strongest writing categories
        return LearningInsight(
            insightType: .strengthIdentification,
            title: "Your Writing Strengths",
            description: "Areas where you consistently excel in Tamil writing.",
            dataPoints: [],
            trend: .improving,
            recommendation: "Leverage your strengths to tackle challenging characters",
            confidence: 0.90
        )
    }

    // MARK: - Helper Methods for Analysis

    private func calculateVariance(scores: [Double]) -> Double {
        let mean = scores.reduce(0, +) / Double(scores.count)
        let squaredDifferences = scores.map { pow($0 - mean, 2) }
        return squaredDifferences.reduce(0, +) / Double(scores.count)
    }

    private func calculateImprovementRate(results: [WritingFeedbackResult]) -> Double {
        guard results.count >= 2 else { return 0 }

        let firstHalf = results.prefix(results.count / 2)
        let secondHalf = results.suffix(results.count / 2)

        let firstAverage = firstHalf.map { $0.overallScore }.reduce(0, +) / Double(firstHalf.count)
        let secondAverage = secondHalf.map { $0.overallScore }.reduce(0, +) / Double(secondHalf.count)

        return (secondAverage - firstAverage) / firstAverage
    }

    private func calculateConsistencyFromHistory(results: [WritingFeedbackResult]) -> Double {
        let scores = results.map { $0.overallScore }
        let variance = calculateVariance(scores: scores)
        return max(0, 1 - variance) // Higher consistency = lower variance
    }

    private func identifyStrongCategories(results: [WritingFeedbackResult]) -> [String] {
        // Analyze which categories user performs best in
        return ["stroke_order", "character_shape"] // Mock data
    }

    private func identifyWeakCategories(results: [WritingFeedbackResult]) -> [String] {
        // Analyze which categories need improvement
        return ["proportions", "fluency"] // Mock data
    }

    private func calculatePracticeFrequency(results: [WritingFeedbackResult]) -> Double {
        // Calculate how frequently user practices
        return 0.65 // Mock frequency
    }

    private func calculateTrend(scores: [Double]) -> LearningInsight.Trend {
        guard scores.count >= 3 else { return .stable }

        let firstThird = scores.prefix(scores.count / 3)
        let lastThird = scores.suffix(scores.count / 3)

        let firstAverage = firstThird.reduce(0, +) / Double(firstThird.count)
        let lastAverage = lastThird.reduce(0, +) / Double(lastThird.count)

        let improvement = (lastAverage - firstAverage) / firstAverage

        if improvement > 0.1 {
            return .improving
        } else if improvement < -0.1 {
            return .declining
        } else {
            return .stable
        }
    }

    private func generateProgressDescription(trend: LearningInsight.Trend, scores: [Double]) -> String {
        switch trend {
        case .improving:
            return "Your writing skills are steadily improving with consistent practice."
        case .stable:
            return "Your performance is stable. Consider new challenges to continue growing."
        case .declining:
            return "Recent performance shows some decline. Review fundamentals and practice regularly."
        case .fluctuating:
            return "Your performance varies. Focus on consistency in practice routine."
        }
    }

    private func generateProgressRecommendation(trend: LearningInsight.Trend) -> String {
        switch trend {
        case .improving:
            return "Keep up the excellent work! Consider advancing to more challenging characters."
        case .stable:
            return "Try varying your practice routine or increasing difficulty level."
        case .declining:
            return "Return to basics and ensure regular practice sessions."
        case .fluctuating:
            return "Establish a consistent practice schedule for better results."
        }
    }

    private func calculateLearningVelocity(history: [WritingFeedbackResult]) -> [LearningInsight.DataPoint] {
        // Mock velocity data
        return [
            LearningInsight.DataPoint(label: "Week 1", value: 0.6, timestamp: Date().addingTimeInterval(-604800)),
            LearningInsight.DataPoint(label: "Week 2", value: 0.7, timestamp: Date().addingTimeInterval(-432000)),
            LearningInsight.DataPoint(label: "Week 3", value: 0.75, timestamp: Date().addingTimeInterval(-259200)),
            LearningInsight.DataPoint(label: "Week 4", value: 0.8, timestamp: Date())
        ]
    }
}

// MARK: - Learning Patterns Data Structure

struct LearningPatterns {
    let averageScore: Double
    let scoreVariance: Double
    let improvementRate: Double
    let consistencyScore: Double
    let strongCategories: [String]
    let weakCategories: [String]
    let practiceFrequency: Double
}
