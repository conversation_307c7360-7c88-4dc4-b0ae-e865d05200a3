//
//  TamilWritingAnalyticsService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import Foundation
import Combine
import Charts

@MainActor
class TamilWritingAnalyticsService: ObservableObject {
    static let shared = TamilWritingAnalyticsService()
    
    @Published var analyticsData: AnalyticsData?
    @Published var isLoadingAnalytics = false
    @Published var selectedTimeRange: TimeRange = .week
    @Published var selectedMetric: AnalyticsMetric = .accuracy
    
    private let supabase = SupabaseService.shared.client
    private var cancellables = Set<AnyCancellable>()
    
    enum TimeRange: String, CaseIterable {
        case day = "24 Hours"
        case week = "7 Days"
        case month = "30 Days"
        case quarter = "3 Months"
        case year = "1 Year"
        case all = "All Time"
        
        var days: Int {
            switch self {
            case .day: return 1
            case .week: return 7
            case .month: return 30
            case .quarter: return 90
            case .year: return 365
            case .all: return Int.max
            }
        }
        
        var chartGranularity: ChartGranularity {
            switch self {
            case .day: return .hour
            case .week: return .day
            case .month: return .day
            case .quarter: return .week
            case .year: return .month
            case .all: return .month
            }
        }
    }
    
    enum ChartGranularity {
        case hour, day, week, month
    }
    
    enum AnalyticsMetric: String, CaseIterable {
        case accuracy = "Accuracy"
        case speed = "Writing Speed"
        case consistency = "Consistency"
        case practiceTime = "Practice Time"
        case charactersLearned = "Characters Learned"
        case streakDays = "Streak Days"
        
        var unit: String {
            switch self {
            case .accuracy, .consistency: return "%"
            case .speed: return "chars/min"
            case .practiceTime: return "minutes"
            case .charactersLearned: return "characters"
            case .streakDays: return "days"
            }
        }
        
        var color: Color {
            switch self {
            case .accuracy: return .green
            case .speed: return .blue
            case .consistency: return .purple
            case .practiceTime: return .orange
            case .charactersLearned: return .red
            case .streakDays: return .yellow
            }
        }
        
        var icon: String {
            switch self {
            case .accuracy: return "target"
            case .speed: return "speedometer"
            case .consistency: return "equal"
            case .practiceTime: return "clock"
            case .charactersLearned: return "textformat.abc"
            case .streakDays: return "flame"
            }
        }
    }
    
    struct AnalyticsData: Codable {
        let userId: String
        let timeRange: String
        let generatedAt: Date
        
        // Overview metrics
        let overviewMetrics: OverviewMetrics
        
        // Progress tracking
        let progressData: ProgressData
        
        // Performance analytics
        let performanceAnalytics: PerformanceAnalytics
        
        // Learning insights
        let learningInsights: LearningInsights
        
        // Character mastery
        let characterMastery: CharacterMastery
        
        // Practice patterns
        let practicePatterns: PracticePatterns
        
        // Achievements and milestones
        let achievements: Achievements
        
        struct OverviewMetrics: Codable {
            let totalPracticeTime: Int // minutes
            let charactersLearned: Int
            let averageAccuracy: Double
            let currentStreak: Int
            let longestStreak: Int
            let totalSessions: Int
            let improvementRate: Double
            let weeklyGoalProgress: Double
        }
        
        struct ProgressData: Codable {
            let dailyProgress: [DailyProgress]
            let weeklyProgress: [WeeklyProgress]
            let monthlyProgress: [MonthlyProgress]
            let skillProgression: [SkillProgression]
            
            struct DailyProgress: Codable, Identifiable {
                let id = UUID()
                let date: Date
                let practiceTime: Int
                let accuracy: Double
                let charactersLearned: Int
                let sessionsCompleted: Int
            }
            
            struct WeeklyProgress: Codable, Identifiable {
                let id = UUID()
                let weekStartDate: Date
                let totalPracticeTime: Int
                let averageAccuracy: Double
                let charactersLearned: Int
                let consistencyScore: Double
            }
            
            struct MonthlyProgress: Codable, Identifiable {
                let id = UUID()
                let month: String
                let year: Int
                let totalPracticeTime: Int
                let averageAccuracy: Double
                let charactersLearned: Int
                let milestones: [String]
            }
            
            struct SkillProgression: Codable, Identifiable {
                let id = UUID()
                let skillCategory: String
                let currentLevel: Double
                let progressToNext: Double
                let trend: String
                let lastImprovement: Date
            }
        }
        
        struct PerformanceAnalytics: Codable {
            let accuracyTrends: [DataPoint]
            let speedTrends: [DataPoint]
            let consistencyTrends: [DataPoint]
            let difficultyProgression: [DifficultyLevel]
            let errorPatterns: [ErrorPattern]
            let timeOfDayPerformance: [TimePerformance]
            
            struct DataPoint: Codable, Identifiable {
                let id = UUID()
                let timestamp: Date
                let value: Double
                let label: String?
            }
            
            struct DifficultyLevel: Codable, Identifiable {
                let id = UUID()
                let level: Int
                let accuracy: Double
                let timeSpent: Int
                let charactersInLevel: Int
                let masteredCharacters: Int
            }
            
            struct ErrorPattern: Codable, Identifiable {
                let id = UUID()
                let errorType: String
                let frequency: Int
                let affectedCharacters: [String]
                let improvementSuggestion: String
            }
            
            struct TimePerformance: Codable, Identifiable {
                let id = UUID()
                let hour: Int
                let averageAccuracy: Double
                let averageSpeed: Double
                let sessionCount: Int
            }
        }
        
        struct LearningInsights: Codable {
            let learningVelocity: Double
            let retentionRate: Double
            let optimalPracticeTime: Int
            let strongestSkills: [String]
            let improvementAreas: [String]
            let personalizedRecommendations: [String]
            let predictedMilestones: [PredictedMilestone]
            
            struct PredictedMilestone: Codable, Identifiable {
                let id = UUID()
                let milestone: String
                let estimatedDate: Date
                let confidence: Double
                let requirements: [String]
            }
        }
        
        struct CharacterMastery: Codable {
            let masteredCharacters: [CharacterProgress]
            let inProgressCharacters: [CharacterProgress]
            let notStartedCharacters: [CharacterProgress]
            let masteryByCategory: [CategoryMastery]
            let difficultyDistribution: [DifficultyDistribution]
            
            struct CharacterProgress: Codable, Identifiable {
                let id = UUID()
                let character: String
                let characterId: String
                let accuracy: Double
                let practiceCount: Int
                let lastPracticed: Date
                let masteryLevel: String
                let timeToMaster: Int? // minutes
            }
            
            struct CategoryMastery: Codable, Identifiable {
                let id = UUID()
                let category: String
                let totalCharacters: Int
                let masteredCharacters: Int
                let averageAccuracy: Double
                let completionPercentage: Double
            }
            
            struct DifficultyDistribution: Codable, Identifiable {
                let id = UUID()
                let difficultyLevel: Int
                let characterCount: Int
                let masteredCount: Int
                let averageAccuracy: Double
            }
        }
        
        struct PracticePatterns: Codable {
            let dailyPracticeHours: [HourlyPractice]
            let weeklyPracticePattern: [DayOfWeekPractice]
            let sessionLengthDistribution: [SessionLength]
            let practiceConsistency: Double
            let optimalSessionLength: Int
            let preferredPracticeTime: String
            
            struct HourlyPractice: Codable, Identifiable {
                let id = UUID()
                let hour: Int
                let practiceMinutes: Int
                let sessionCount: Int
                let averageAccuracy: Double
            }
            
            struct DayOfWeekPractice: Codable, Identifiable {
                let id = UUID()
                let dayOfWeek: String
                let practiceMinutes: Int
                let sessionCount: Int
                let averageAccuracy: Double
            }
            
            struct SessionLength: Codable, Identifiable {
                let id = UUID()
                let lengthRange: String
                let sessionCount: Int
                let averageAccuracy: Double
                let completionRate: Double
            }
        }
        
        struct Achievements: Codable {
            let unlockedAchievements: [Achievement]
            let inProgressAchievements: [Achievement]
            let upcomingAchievements: [Achievement]
            let totalPoints: Int
            let currentLevel: Int
            let pointsToNextLevel: Int
            
            struct Achievement: Codable, Identifiable {
                let id = UUID()
                let title: String
                let description: String
                let icon: String
                let points: Int
                let unlockedDate: Date?
                let progress: Double
                let category: String
            }
        }
    }
    
    private init() {
        setupAnalyticsTracking()
    }
    
    // MARK: - Analytics Generation
    
    /// Generate comprehensive analytics for the specified time range
    func generateAnalytics(for timeRange: TimeRange, userId: String) async {
        isLoadingAnalytics = true
        defer { isLoadingAnalytics = false }
        
        do {
            let analytics = await generateComprehensiveAnalytics(
                timeRange: timeRange,
                userId: userId
            )
            
            analyticsData = analytics
            
            // Cache analytics data
            await cacheAnalyticsData(analytics)
            
            print("✅ Analytics generated successfully for \(timeRange.rawValue)")
            
        } catch {
            print("❌ Failed to generate analytics: \(error)")
        }
    }
    
    /// Generate real-time analytics dashboard
    func generateRealTimeAnalytics(userId: String) async -> AnalyticsData {
        return await generateComprehensiveAnalytics(timeRange: .week, userId: userId)
    }
    
    /// Get analytics for specific metric
    func getMetricAnalytics(metric: AnalyticsMetric, timeRange: TimeRange) -> [AnalyticsData.PerformanceAnalytics.DataPoint] {
        guard let analytics = analyticsData else { return [] }
        
        switch metric {
        case .accuracy:
            return analytics.performanceAnalytics.accuracyTrends
        case .speed:
            return analytics.performanceAnalytics.speedTrends
        case .consistency:
            return analytics.performanceAnalytics.consistencyTrends
        case .practiceTime:
            return generatePracticeTimeData(from: analytics)
        case .charactersLearned:
            return generateCharactersLearnedData(from: analytics)
        case .streakDays:
            return generateStreakData(from: analytics)
        }
    }
    
    // MARK: - Private Analytics Generation
    
    private func generateComprehensiveAnalytics(timeRange: TimeRange, userId: String) async -> AnalyticsData {
        
        // Generate overview metrics
        let overviewMetrics = await generateOverviewMetrics(timeRange: timeRange, userId: userId)
        
        // Generate progress data
        let progressData = await generateProgressData(timeRange: timeRange, userId: userId)
        
        // Generate performance analytics
        let performanceAnalytics = await generatePerformanceAnalytics(timeRange: timeRange, userId: userId)
        
        // Generate learning insights
        let learningInsights = await generateLearningInsights(timeRange: timeRange, userId: userId)
        
        // Generate character mastery data
        let characterMastery = await generateCharacterMastery(userId: userId)
        
        // Generate practice patterns
        let practicePatterns = await generatePracticePatterns(timeRange: timeRange, userId: userId)
        
        // Generate achievements
        let achievements = await generateAchievements(userId: userId)
        
        return AnalyticsData(
            userId: userId,
            timeRange: timeRange.rawValue,
            generatedAt: Date(),
            overviewMetrics: overviewMetrics,
            progressData: progressData,
            performanceAnalytics: performanceAnalytics,
            learningInsights: learningInsights,
            characterMastery: characterMastery,
            practicePatterns: practicePatterns,
            achievements: achievements
        )
    }
    
    private func generateOverviewMetrics(timeRange: TimeRange, userId: String) async -> AnalyticsData.OverviewMetrics {
        // Mock data for development - would fetch from database in production
        return AnalyticsData.OverviewMetrics(
            totalPracticeTime: 450, // 7.5 hours
            charactersLearned: 47,
            averageAccuracy: 0.78,
            currentStreak: 12,
            longestStreak: 18,
            totalSessions: 34,
            improvementRate: 0.15,
            weeklyGoalProgress: 0.85
        )
    }
    
    private func generateProgressData(timeRange: TimeRange, userId: String) async -> AnalyticsData.ProgressData {
        let dailyProgress = generateDailyProgressData(timeRange: timeRange)
        let weeklyProgress = generateWeeklyProgressData(timeRange: timeRange)
        let monthlyProgress = generateMonthlyProgressData(timeRange: timeRange)
        let skillProgression = generateSkillProgressionData()
        
        return AnalyticsData.ProgressData(
            dailyProgress: dailyProgress,
            weeklyProgress: weeklyProgress,
            monthlyProgress: monthlyProgress,
            skillProgression: skillProgression
        )
    }
    
    private func generatePerformanceAnalytics(timeRange: TimeRange, userId: String) async -> AnalyticsData.PerformanceAnalytics {
        let accuracyTrends = generateAccuracyTrends(timeRange: timeRange)
        let speedTrends = generateSpeedTrends(timeRange: timeRange)
        let consistencyTrends = generateConsistencyTrends(timeRange: timeRange)
        let difficultyProgression = generateDifficultyProgression()
        let errorPatterns = generateErrorPatterns()
        let timeOfDayPerformance = generateTimeOfDayPerformance()
        
        return AnalyticsData.PerformanceAnalytics(
            accuracyTrends: accuracyTrends,
            speedTrends: speedTrends,
            consistencyTrends: consistencyTrends,
            difficultyProgression: difficultyProgression,
            errorPatterns: errorPatterns,
            timeOfDayPerformance: timeOfDayPerformance
        )
    }
    
    private func generateLearningInsights(timeRange: TimeRange, userId: String) async -> AnalyticsData.LearningInsights {
        let predictedMilestones = [
            AnalyticsData.LearningInsights.PredictedMilestone(
                milestone: "Master all vowels",
                estimatedDate: Calendar.current.date(byAdding: .day, value: 14, to: Date()) ?? Date(),
                confidence: 0.85,
                requirements: ["Practice 15 minutes daily", "Focus on difficult vowels"]
            ),
            AnalyticsData.LearningInsights.PredictedMilestone(
                milestone: "Complete A1 level",
                estimatedDate: Calendar.current.date(byAdding: .month, value: 2, to: Date()) ?? Date(),
                confidence: 0.72,
                requirements: ["Maintain current pace", "Complete all basic consonants"]
            )
        ]
        
        return AnalyticsData.LearningInsights(
            learningVelocity: 2.3, // characters per week
            retentionRate: 0.82,
            optimalPracticeTime: 20, // minutes
            strongestSkills: ["Vowel formation", "Stroke order"],
            improvementAreas: ["Character proportions", "Writing speed"],
            personalizedRecommendations: [
                "Practice during morning hours for better retention",
                "Focus on consonant combinations",
                "Increase practice session frequency"
            ],
            predictedMilestones: predictedMilestones
        )
    }
    
    private func generateCharacterMastery(userId: String) async -> AnalyticsData.CharacterMastery {
        // Mock character mastery data
        let masteredCharacters = generateMasteredCharacters()
        let inProgressCharacters = generateInProgressCharacters()
        let notStartedCharacters = generateNotStartedCharacters()
        let masteryByCategory = generateMasteryByCategory()
        let difficultyDistribution = generateDifficultyDistribution()
        
        return AnalyticsData.CharacterMastery(
            masteredCharacters: masteredCharacters,
            inProgressCharacters: inProgressCharacters,
            notStartedCharacters: notStartedCharacters,
            masteryByCategory: masteryByCategory,
            difficultyDistribution: difficultyDistribution
        )
    }
    
    private func generatePracticePatterns(timeRange: TimeRange, userId: String) async -> AnalyticsData.PracticePatterns {
        let dailyPracticeHours = generateDailyPracticeHours()
        let weeklyPracticePattern = generateWeeklyPracticePattern()
        let sessionLengthDistribution = generateSessionLengthDistribution()
        
        return AnalyticsData.PracticePatterns(
            dailyPracticeHours: dailyPracticeHours,
            weeklyPracticePattern: weeklyPracticePattern,
            sessionLengthDistribution: sessionLengthDistribution,
            practiceConsistency: 0.75,
            optimalSessionLength: 18, // minutes
            preferredPracticeTime: "Evening (6-8 PM)"
        )
    }
    
    private func generateAchievements(userId: String) async -> AnalyticsData.Achievements {
        let unlockedAchievements = generateUnlockedAchievements()
        let inProgressAchievements = generateInProgressAchievements()
        let upcomingAchievements = generateUpcomingAchievements()
        
        return AnalyticsData.Achievements(
            unlockedAchievements: unlockedAchievements,
            inProgressAchievements: inProgressAchievements,
            upcomingAchievements: upcomingAchievements,
            totalPoints: 1250,
            currentLevel: 8,
            pointsToNextLevel: 350
        )
    }
    
    // MARK: - Helper Methods for Data Generation
    
    private func setupAnalyticsTracking() {
        // Setup real-time analytics tracking
        Timer.scheduledTimer(withTimeInterval: 300, repeats: true) { _ in
            Task {
                await self.updateRealTimeMetrics()
            }
        }
    }
    
    private func updateRealTimeMetrics() async {
        // Update real-time metrics
    }
    
    private func cacheAnalyticsData(_ analytics: AnalyticsData) async {
        // Cache analytics data for offline access
    }
    
    // Mock data generation methods (would be replaced with real database queries)
    private func generateDailyProgressData(timeRange: TimeRange) -> [AnalyticsData.ProgressData.DailyProgress] { return [] }
    private func generateWeeklyProgressData(timeRange: TimeRange) -> [AnalyticsData.ProgressData.WeeklyProgress] { return [] }
    private func generateMonthlyProgressData(timeRange: TimeRange) -> [AnalyticsData.ProgressData.MonthlyProgress] { return [] }
    private func generateSkillProgressionData() -> [AnalyticsData.ProgressData.SkillProgression] { return [] }
    private func generateAccuracyTrends(timeRange: TimeRange) -> [AnalyticsData.PerformanceAnalytics.DataPoint] { return [] }
    private func generateSpeedTrends(timeRange: TimeRange) -> [AnalyticsData.PerformanceAnalytics.DataPoint] { return [] }
    private func generateConsistencyTrends(timeRange: TimeRange) -> [AnalyticsData.PerformanceAnalytics.DataPoint] { return [] }
    private func generateDifficultyProgression() -> [AnalyticsData.PerformanceAnalytics.DifficultyLevel] { return [] }
    private func generateErrorPatterns() -> [AnalyticsData.PerformanceAnalytics.ErrorPattern] { return [] }
    private func generateTimeOfDayPerformance() -> [AnalyticsData.PerformanceAnalytics.TimePerformance] { return [] }
    private func generateMasteredCharacters() -> [AnalyticsData.CharacterMastery.CharacterProgress] { return [] }
    private func generateInProgressCharacters() -> [AnalyticsData.CharacterMastery.CharacterProgress] { return [] }
    private func generateNotStartedCharacters() -> [AnalyticsData.CharacterMastery.CharacterProgress] { return [] }
    private func generateMasteryByCategory() -> [AnalyticsData.CharacterMastery.CategoryMastery] { return [] }
    private func generateDifficultyDistribution() -> [AnalyticsData.CharacterMastery.DifficultyDistribution] { return [] }
    private func generateDailyPracticeHours() -> [AnalyticsData.PracticePatterns.HourlyPractice] { return [] }
    private func generateWeeklyPracticePattern() -> [AnalyticsData.PracticePatterns.DayOfWeekPractice] { return [] }
    private func generateSessionLengthDistribution() -> [AnalyticsData.PracticePatterns.SessionLength] { return [] }
    private func generateUnlockedAchievements() -> [AnalyticsData.Achievements.Achievement] { return [] }
    private func generateInProgressAchievements() -> [AnalyticsData.Achievements.Achievement] { return [] }
    private func generateUpcomingAchievements() -> [AnalyticsData.Achievements.Achievement] { return [] }
    private func generatePracticeTimeData(from analytics: AnalyticsData) -> [AnalyticsData.PerformanceAnalytics.DataPoint] { return [] }
    private func generateCharactersLearnedData(from analytics: AnalyticsData) -> [AnalyticsData.PerformanceAnalytics.DataPoint] { return [] }
    private func generateStreakData(from analytics: AnalyticsData) -> [AnalyticsData.PerformanceAnalytics.DataPoint] { return [] }
}
