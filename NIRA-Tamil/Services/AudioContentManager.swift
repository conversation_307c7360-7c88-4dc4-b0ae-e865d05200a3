import Foundation
import SwiftUI
import AVFoundation

@MainActor
class AudioContentManager: NSObject, ObservableObject {
    static let shared = AudioContentManager()

    // Legacy properties for backward compatibility
    @Published var isProcessingLesson = false
    @Published var processingProgress: Double = 0.0
    @Published var processingStatus: String = ""
    @Published var availableAudioContent: [String: [String]] = [:]

    // New Tamil content properties
    @Published var isPlaying = false
    @Published var currentAudioFile: String?
    @Published var playbackProgress: Double = 0.0
    @Published var duration: Double = 0.0
    @Published var errorMessage: String?

    private let audioPlayerService = AudioPlayerService.shared
    private let supabaseClient = NIRASupabaseClient.shared
    private let contentLoader = TamilContentLoader()
    private let speechSynthesizer = AVSpeechSynthesizer()

    // Audio player for Tamil content
    private var audioPlayer: AVAudioPlayer?
    private var playbackTimer: Timer?
    private let audioSession = AVAudioSession.sharedInstance()

    override init() {
        super.init()
        setupAudioSession()
        loadAvailableAudioContent()
    }
    
    // MARK: - Audio Session Setup

    private func setupAudioSession() {
        do {
            try audioSession.setCategory(.playback, mode: .default, options: [.allowAirPlay, .allowBluetooth])
            try audioSession.setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
            errorMessage = "Audio setup failed: \(error.localizedDescription)"
        }
    }

    // MARK: - Tamil Content Audio Methods

    func playAudio(filename: String, level: CEFRLevel = .a1) {
        guard let audioURL = contentLoader.getAudioURL(for: filename, level: level) else {
            errorMessage = "Audio file not found: \(filename)"
            return
        }

        playAudio(url: audioURL, filename: filename)
    }

    // MARK: - Conversation Audio Methods

    /// Play individual conversation line audio
    func playConversationLineAudio(line: TamilSupabaseConversationLine, slowSpeed: Bool = false) {
        let audioUrl = slowSpeed ? line.audioSlowUrl : line.audioUrl

        guard let urlString = audioUrl,
              let url = URL(string: urlString) else {
            print("❌ No audio URL available for conversation line")
            errorMessage = "Audio not available for this line"
            return
        }

        let filename = "conv_line_\(line.lineNumber)_\(line.speakerName.lowercased())"
        print("🎵 Playing conversation line audio: \(filename)")
        playAudio(url: url, filename: filename)
    }

    /// Play full conversation audio
    func playFullConversationAudio(conversation: TamilSupabaseConversation) {
        guard let audioUrl = conversation.audioFullUrl,
              let url = URL(string: audioUrl) else {
            print("❌ No full conversation audio URL available")
            errorMessage = "Full conversation audio not available"
            return
        }

        let filename = "conv_full_\(conversation.conversationId)"
        print("🎵 Playing full conversation audio: \(filename)")
        playAudio(url: url, filename: filename)
    }

    func playAudio(url: URL, filename: String) {
        stopCurrentAudio()

        // Check if it's a remote URL
        if url.scheme == "http" || url.scheme == "https" {
            // For remote URLs, download first then play
            Task {
                await playRemoteAudio(url: url, filename: filename)
            }
        } else {
            // For local URLs, play directly
            playLocalAudio(url: url, filename: filename)
        }
    }

    private func playLocalAudio(url: URL, filename: String) {
        do {
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.delegate = self
            audioPlayer?.prepareToPlay()

            currentAudioFile = filename
            duration = audioPlayer?.duration ?? 0.0

            if audioPlayer?.play() == true {
                isPlaying = true
                startPlaybackTimer()
            } else {
                errorMessage = "Failed to start audio playback"
            }
        } catch {
            print("Failed to play audio: \(error)")
            errorMessage = "Playback failed: \(error.localizedDescription)"
        }
    }

    private func playRemoteAudio(url: URL, filename: String) async {
        await MainActor.run {
            currentAudioFile = filename
            errorMessage = nil
        }

        do {
            print("🌐 Downloading audio from: \(url)")
            let (data, _) = try await URLSession.shared.data(from: url)

            await MainActor.run {
                do {
                    audioPlayer = try AVAudioPlayer(data: data)
                    audioPlayer?.delegate = self
                    audioPlayer?.prepareToPlay()

                    duration = audioPlayer?.duration ?? 0.0

                    if audioPlayer?.play() == true {
                        isPlaying = true
                        startPlaybackTimer()
                        print("✅ Successfully playing remote audio: \(filename)")
                    } else {
                        errorMessage = "Failed to start audio playback"
                    }
                } catch {
                    print("❌ Failed to create audio player from data: \(error)")
                    errorMessage = "Playback failed: \(error.localizedDescription)"
                }
            }
        } catch {
            await MainActor.run {
                print("❌ Failed to download audio: \(error)")
                errorMessage = "Failed to download audio: \(error.localizedDescription)"
            }
        }
    }

    func pauseAudio() {
        audioPlayer?.pause()
        isPlaying = false
        stopPlaybackTimer()
    }

    func resumeAudio() {
        if audioPlayer?.play() == true {
            isPlaying = true
            startPlaybackTimer()
        }
    }

    func stopAudio() {
        stopCurrentAudio()
    }

    private func stopCurrentAudio() {
        audioPlayer?.stop()
        audioPlayer = nil
        isPlaying = false
        currentAudioFile = nil
        playbackProgress = 0.0
        duration = 0.0
        stopPlaybackTimer()
    }

    // MARK: - Vocabulary Audio

    func playVocabularyWord(_ vocabulary: TamilVocabulary, level: CEFRLevel) {
        playAudio(filename: vocabulary.audioWordUrl, level: level)
    }

    func playVocabularySentence(_ vocabulary: TamilVocabulary, level: CEFRLevel) {
        playAudio(filename: vocabulary.audioSentenceUrl, level: level)
    }

    // MARK: - Conversation Audio

    func playConversationLine(_ dialogue: TamilDialogue, level: CEFRLevel) {
        playAudio(filename: dialogue.audioUrl, level: level)
    }

    // MARK: - Legacy Methods (for backward compatibility)

    func playAudioWithFallback(text: String, audioURL: String?, context: String = "") async {
        // Try to play existing audio first
        if let audioURL = audioURL, !audioURL.isEmpty {
            print("🔊 Attempting to play audio: \(audioURL)")

            // Pass the URL directly to AudioPlayerService - it handles both remote and local URLs correctly
            audioPlayerService.playAudio(from: audioURL)
            return
        }

        // Only generate audio if no URL is provided
        print("🎵 No audio URL provided for '\(text)' - skipping audio generation")
        // Note: We're not generating audio automatically to avoid API costs
        // Audio should be pre-generated using the batch scripts
    }
    
    func generateAndPlayAudio(text: String, context: String = "") async {
        await MainActor.run {
            processingStatus = "Generating audio for: \(text.prefix(30))..."

            // Use Google Text-to-Speech via AVSpeechSynthesizer
            let utterance = AVSpeechUtterance(string: text)
            utterance.voice = AVSpeechSynthesisVoice(language: "ta-IN") // Tamil (India) - Default female
            utterance.rate = 0.5 // Slower rate for learning
            utterance.pitchMultiplier = 1.0

            speechSynthesizer.speak(utterance)
            processingStatus = "Audio generated and playing"

            print("🔊 Playing Tamil audio with Google TTS: \(text)")
        }
    }

    /// Generate and play audio with voice selection based on speaker
    func generateAndPlayAudioWithVoice(text: String, speakerName: String, slowSpeed: Bool = false, context: String = "") async {
        await MainActor.run {
            processingStatus = "Generating audio for: \(speakerName) - \(text.prefix(30))..."

            let utterance = AVSpeechUtterance(string: text)

            // Use specific Google TTS voices for male/female speakers
            let isMale = isMaleSpeaker(speakerName)
            let voiceIdentifier = isMale ? "ta-IN-Chirp3-HD-Iapetus" : "ta-IN-Chirp3-HD-Erinome"

            print("🎵 Attempting to use Google TTS voice: \(voiceIdentifier) for \(speakerName) (isMale: \(isMale))")

            // Try to get the specific Google TTS voice
            if let googleVoice = AVSpeechSynthesisVoice(identifier: voiceIdentifier) {
                utterance.voice = googleVoice
                print("✅ Successfully using Google TTS voice: \(googleVoice.name) (\(googleVoice.identifier)) for \(speakerName)")
            } else {
                // Fallback to language-based voice selection
                print("⚠️ Google TTS voice \(voiceIdentifier) not available, trying language-based selection")

                let tamilVoices = AVSpeechSynthesisVoice.speechVoices().filter { $0.language.hasPrefix("ta") }
                print("🎵 Available Tamil voices: \(tamilVoices.map { "\($0.name) (\($0.identifier))" })")

                if let selectedVoice = selectTamilVoice(for: speakerName, from: tamilVoices) {
                    utterance.voice = selectedVoice
                    print("🎵 Using fallback voice: \(selectedVoice.name) (\(selectedVoice.identifier)) for speaker: \(speakerName)")
                } else {
                    // Try to get any Tamil voice
                    if let tamilVoice = AVSpeechSynthesisVoice(language: "ta-IN") {
                        utterance.voice = tamilVoice
                        print("🎵 Using default Tamil voice: \(tamilVoice.name) (\(tamilVoice.identifier)) for speaker: \(speakerName)")
                    } else {
                        // Last resort: inform user that Tamil voices aren't available
                        print("❌ No Tamil voices available on this device/simulator. Tamil TTS requires device with Tamil language support.")
                        print("💡 On real device: Go to Settings > General > Language & Region > Add Tamil")
                        print("💡 For now, skipping audio playback to avoid English voice speaking Tamil text")
                        return // Don't play audio with wrong voice
                    }
                }
            }

            utterance.rate = slowSpeed ? 0.3 : 0.5 // Slower rate for learning
            utterance.pitchMultiplier = isMale ? 0.7 : 1.0 // Lower pitch for male speakers

            speechSynthesizer.speak(utterance)
            processingStatus = "Audio generated and playing for \(speakerName)"

            print("🔊 Playing Tamil audio with voice for \(speakerName): \(text)")
        }
    }

    /// Select appropriate Tamil voice based on speaker gender
    private func selectTamilVoice(for speakerName: String, from voices: [AVSpeechSynthesisVoice]) -> AVSpeechSynthesisVoice? {
        let isMale = isMaleSpeaker(speakerName)
        print("🎵 Selecting voice for \(speakerName) (isMale: \(isMale)) from \(voices.count) voices")

        // Look for gender-appropriate voices
        for voice in voices {
            let voiceName = voice.name.lowercased()
            let identifier = voice.identifier.lowercased()
            print("🎵 Checking voice: \(voice.name) (\(identifier))")

            if isMale {
                // Look for male indicators in voice name or identifier
                if voiceName.contains("male") || voiceName.contains("man") ||
                   identifier.contains("male") || identifier.contains("m") {
                    print("✅ Selected MALE voice: \(voice.name) for \(speakerName)")
                    return voice
                }
            } else {
                // Look for female indicators in voice name or identifier
                if voiceName.contains("female") || voiceName.contains("woman") ||
                   identifier.contains("female") || identifier.contains("f") {
                    print("✅ Selected FEMALE voice: \(voice.name) for \(speakerName)")
                    return voice
                }
            }
        }

        // If no gender-specific voice found, return the first available Tamil voice
        let fallbackVoice = voices.first
        print("⚠️ No gender-specific voice found, using fallback: \(fallbackVoice?.name ?? "none")")
        return fallbackVoice
    }

    /// Check if speaker is male based on name
    private func isMaleSpeaker(_ speakerName: String) -> Bool {
        let maleNames = [
            "raj", "ravi", "kumar", "suresh", "ramesh", "ganesh", "arun", "karthik",
            "vijay", "ajay", "sanjay", "deepak", "rohit", "amit", "ankit", "rahul",
            "arjun", "krishna", "shiva", "vishnu", "murugan", "selvam", "mani"
        ]

        let lowerName = speakerName.lowercased()
        let isMale = maleNames.contains { lowerName.contains($0) }
        print("🎭 Speaker '\(speakerName)' -> isMale: \(isMale)")
        return isMale
    }
    
    func processLessonAudio(_ lesson: SupabaseLesson) async {
        isProcessingLesson = true
        processingProgress = 0.0
        processingStatus = "Processing lesson audio..."
        
        defer {
            Task { @MainActor in
                isProcessingLesson = false
                processingProgress = 0.0
                processingStatus = ""
            }
        }
        
        // Note: Google TTS generates audio on-demand, no batch generation needed
        await MainActor.run {
            processingStatus = "Audio ready for playback with Google TTS"
            processingProgress = 0.8

            // Google TTS doesn't require uploading - audio is generated on-demand
            processingProgress = 1.0
            processingStatus = "Lesson audio processing complete"

            // Update available audio content
            loadAvailableAudioContent()
        }
    }
    
    func getAudioURL(for text: String, context: String = "") -> String? {
        // Check if we have a cached or uploaded audio file for this text
        let key = generateAudioKey(text: text, context: context)
        return availableAudioContent[context]?.first { $0.contains(key) }
    }
    
    func hasAudio(for text: String, context: String = "") -> Bool {
        return getAudioURL(for: text, context: context) != nil
    }
    
    // MARK: - Private Methods
    
    private func loadAvailableAudioContent() {
        // Load available audio content from cache and Supabase
        Task {
            // This would typically query Supabase for available audio files
            // For now, we'll use a simple cache-based approach
            await updateAvailableAudioFromCache()
        }
    }
    
    private func updateAvailableAudioFromCache() async {
        let cacheDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            .appendingPathComponent("AudioCache")
        
        do {
            let audioFiles = try FileManager.default.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            let audioFilenames = audioFiles.map { $0.lastPathComponent }
            
            await MainActor.run {
                availableAudioContent["cache"] = audioFilenames
            }
        } catch {
            print("❌ Failed to load cached audio content: \(error)")
        }
    }
    
    private func uploadGeneratedAudio(audioURL: URL, text: String, context: String) async {
        // This would upload the generated audio to Supabase Storage
        // For now, we'll just log the action
        print("📤 Would upload audio to Supabase: \(audioURL.lastPathComponent) for '\(text)'")
        
        // In a full implementation, this would:
        // 1. Upload the audio file to Supabase Storage
        // 2. Update the lesson content with the new audio URL
        // 3. Cache the mapping locally
    }
    
    private func generateAudioKey(text: String, context: String) -> String {
        let cleanText = text.lowercased()
            .replacingOccurrences(of: " ", with: "_")
            .replacingOccurrences(of: "[^a-z0-9_]", with: "", options: .regularExpression)
        return "\(context)_\(cleanText)".prefix(50).description
    }

    // MARK: - Playback Timer

    private func startPlaybackTimer() {
        playbackTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
            Task { @MainActor in
                self.updateProgress()
            }
        }
    }

    private func stopPlaybackTimer() {
        playbackTimer?.invalidate()
        playbackTimer = nil
    }

    private func updateProgress() {
        guard let player = audioPlayer else { return }
        playbackProgress = player.duration > 0 ? player.currentTime / player.duration : 0.0
    }
}

// MARK: - AVAudioPlayerDelegate

extension AudioContentManager: AVAudioPlayerDelegate {
    nonisolated func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        Task { @MainActor in
            stopCurrentAudio()
        }
    }

    nonisolated func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        Task { @MainActor in
            if let error = error {
                errorMessage = "Audio decode error: \(error.localizedDescription)"
            }
            stopCurrentAudio()
        }
    }
}

// MARK: - Enhanced Audio Button with Fallback

struct EnhancedAudioButton: View {
    let text: String
    let audioURL: String?
    let context: String
    let size: CGFloat
    let color: Color
    
    @StateObject private var audioManager = AudioContentManager.shared
    @StateObject private var audioService = AudioPlayerService.shared
    
    init(text: String, audioURL: String?, context: String = "", size: CGFloat = 32, color: Color = .blue) {
        self.text = text
        self.audioURL = audioURL
        self.context = context
        self.size = size
        self.color = color
    }
    
    var body: some View {
        Button(action: {
            Task {
                await audioManager.playAudioWithFallback(text: text, audioURL: audioURL, context: context)
            }
        }) {
            Group {
                if isCurrentlyLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: color))
                } else if isCurrentlyPlaying {
                    Image(systemName: "stop.fill")
                        .font(.system(size: size * 0.6, weight: .medium))
                        .foregroundColor(color)
                } else {
                    VStack(spacing: 2) {
                        Image(systemName: hasAudioAvailable ? "speaker.wave.2.fill" : "speaker.slash.fill")
                            .font(.system(size: size * 0.5, weight: .medium))
                            .foregroundColor(hasAudioAvailable ? color : .gray)

                        if !hasAudioAvailable {
                            Text("N/A")
                                .font(.system(size: size * 0.2, weight: .bold))
                                .foregroundColor(.gray)
                        }
                    }
                }
            }
            .frame(width: size, height: size)
            .background(
                Circle()
                    .fill(hasAudioAvailable ? color.opacity(0.1) : Color.gray.opacity(0.1))
            )
            .scaleEffect(isCurrentlyPlaying ? 1.1 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isCurrentlyPlaying)
        }
        .disabled(text.isEmpty || isCurrentlyLoading)
    }
    
    private var hasAudioAvailable: Bool {
        return audioURL != nil && !audioURL!.isEmpty
    }
    
    private var isCurrentlyPlaying: Bool {
        return audioService.isPlaying && (
            audioService.currentAudioURL == audioURL ||
            audioService.currentAudioURL?.contains(text.prefix(10)) == true
        )
    }
    
    private var isCurrentlyLoading: Bool {
        return audioService.isLoading || audioManager.isProcessingLesson
    }
}

// MARK: - Lesson Audio Processing View

struct LessonAudioProcessingView: View {
    let lesson: SupabaseLesson
    @StateObject private var audioManager = AudioContentManager.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Header
                VStack(spacing: 8) {
                    Image(systemName: "waveform.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)
                    
                    Text("Audio Processing")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Generating missing audio for lesson content")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                // Progress
                if audioManager.isProcessingLesson {
                    VStack(spacing: 16) {
                        ProgressView(value: audioManager.processingProgress)
                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                            .scaleEffect(1.2)
                        
                        Text(audioManager.processingStatus)
                            .font(.body)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.center)
                        
                        Text("\(Int(audioManager.processingProgress * 100))% Complete")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
                } else {
                    VStack(spacing: 16) {
                        Text("Ready to process lesson audio")
                            .font(.body)
                            .foregroundColor(.secondary)
                        
                        Button("Start Processing") {
                            Task {
                                await audioManager.processLessonAudio(lesson)
                            }
                        }
                        .buttonStyle(.borderedProminent)
                        .controlSize(.large)
                    }
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("Audio Generation")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}
