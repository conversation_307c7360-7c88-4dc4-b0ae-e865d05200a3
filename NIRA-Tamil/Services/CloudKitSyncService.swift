//
//  CloudKitSyncService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import Foundation
import CloudKit
import Combine
import SwiftUI

@MainActor
class CloudKitSyncService: ObservableObject {
    static let shared = CloudKitSyncService()
    
    @Published var syncStatus: SyncStatus = .idle
    @Published var lastSyncDate: Date?
    @Published var syncProgress: Double = 0.0
    @Published var isOnline = true
    
    private let container: CKContainer
    private let privateDatabase: CKDatabase
    private let publicDatabase: CKDatabase
    
    private var cancellables = Set<AnyCancellable>()
    private let syncQueue = DispatchQueue(label: "com.nira.tamil.sync", qos: .utility)
    
    enum SyncStatus: Equatable {
        case idle
        case syncing
        case success
        case error(String)
        
        var displayText: String {
            switch self {
            case .idle: return "Ready to sync"
            case .syncing: return "Syncing..."
            case .success: return "Sync completed"
            case .error(let message): return "Error: \(message)"
            }
        }
    }
    
    // MARK: - CloudKit Record Types
    
    struct RecordType {
        static let writingProgress = "WritingProgress"
        static let writingSession = "WritingSession"
        static let characterMastery = "CharacterMastery"
        static let userPreferences = "UserPreferences"
        static let practiceHistory = "PracticeHistory"
    }
    
    struct RecordKey {
        // WritingProgress
        static let userId = "userId"
        static let characterId = "characterId"
        static let accuracy = "accuracy"
        static let practiceCount = "practiceCount"
        static let lastPracticed = "lastPracticed"
        static let masteryLevel = "masteryLevel"
        static let deviceType = "deviceType"
        
        // WritingSession
        static let sessionId = "sessionId"
        static let startTime = "startTime"
        static let endTime = "endTime"
        static let charactersCount = "charactersCount"
        static let averageAccuracy = "averageAccuracy"
        static let sessionType = "sessionType"
        
        // UserPreferences
        static let writingMode = "writingMode"
        static let guidanceEnabled = "guidanceEnabled"
        static let soundEnabled = "soundEnabled"
        static let hapticEnabled = "hapticEnabled"
        static let preferredDifficulty = "preferredDifficulty"
    }
    
    private init() {
        container = CKContainer(identifier: "iCloud.com.nira.tamil")
        privateDatabase = container.privateCloudDatabase
        publicDatabase = container.publicCloudDatabase
        
        setupCloudKitSubscriptions()
        startNetworkMonitoring()
    }
    
    // MARK: - Sync Operations
    
    /// Sync all writing progress data across devices
    func syncAllData() async {
        syncStatus = .syncing
        syncProgress = 0.0
        
        do {
            // Sync writing progress
            syncProgress = 0.2
            try await syncWritingProgress()
            
            // Sync writing sessions
            syncProgress = 0.4
            try await syncWritingSessions()
            
            // Sync character mastery
            syncProgress = 0.6
            try await syncCharacterMastery()
            
            // Sync user preferences
            syncProgress = 0.8
            try await syncUserPreferences()
            
            // Sync practice history
            syncProgress = 1.0
            try await syncPracticeHistory()
            
            lastSyncDate = Date()
            syncStatus = .success
            
            print("✅ CloudKit sync completed successfully")
            
        } catch {
            syncStatus = .error(error.localizedDescription)
            print("❌ CloudKit sync failed: \(error)")
        }
    }
    
    /// Sync writing progress for a specific character
    func syncCharacterProgress(_ characterId: UUID, progress: WritingProgressData) async throws {
        let record = CKRecord(recordType: RecordType.writingProgress)
        
        record[RecordKey.userId] = getCurrentUserId()
        record[RecordKey.characterId] = characterId.uuidString
        record[RecordKey.accuracy] = progress.accuracy
        record[RecordKey.practiceCount] = progress.practiceCount
        record[RecordKey.lastPracticed] = progress.lastPracticed
        record[RecordKey.masteryLevel] = progress.masteryLevel.rawValue
        record[RecordKey.deviceType] = getCurrentDeviceType()
        
        try await privateDatabase.save(record)
    }
    
    /// Sync a writing session
    func syncWritingSession(_ session: WritingSessionData) async throws {
        let record = CKRecord(recordType: RecordType.writingSession)
        
        record[RecordKey.userId] = getCurrentUserId()
        record[RecordKey.sessionId] = session.id.uuidString
        record[RecordKey.startTime] = session.startTime
        record[RecordKey.endTime] = session.endTime
        record[RecordKey.charactersCount] = session.charactersCount
        record[RecordKey.averageAccuracy] = session.averageAccuracy
        record[RecordKey.sessionType] = session.sessionType.rawValue
        record[RecordKey.deviceType] = getCurrentDeviceType()
        
        try await privateDatabase.save(record)
    }
    
    /// Sync user preferences
    func syncUserPreferences(_ preferences: UserWritingPreferences) async throws {
        let record = CKRecord(recordType: RecordType.userPreferences)
        
        record[RecordKey.userId] = getCurrentUserId()
        record[RecordKey.writingMode] = preferences.writingMode.rawValue
        record[RecordKey.guidanceEnabled] = preferences.guidanceEnabled ? 1 : 0
        record[RecordKey.soundEnabled] = preferences.soundEnabled ? 1 : 0
        record[RecordKey.hapticEnabled] = preferences.hapticEnabled ? 1 : 0
        record[RecordKey.preferredDifficulty] = preferences.preferredDifficulty
        record[RecordKey.deviceType] = getCurrentDeviceType()
        
        try await privateDatabase.save(record)
    }
    
    // MARK: - Fetch Operations
    
    /// Fetch writing progress for all characters
    func fetchWritingProgress() async throws -> [WritingProgressData] {
        let predicate = NSPredicate(format: "userId == %@", getCurrentUserId())
        let query = CKQuery(recordType: RecordType.writingProgress, predicate: predicate)
        
        let (records, _) = try await privateDatabase.records(matching: query)
        
        var progressData: [WritingProgressData] = []
        
        for (_, result) in records {
            switch result {
            case .success(let record):
                if let progress = parseWritingProgressRecord(record) {
                    progressData.append(progress)
                }
            case .failure(let error):
                print("Failed to fetch progress record: \(error)")
            }
        }
        
        return progressData
    }
    
    /// Fetch writing sessions
    func fetchWritingSessions(limit: Int = 50) async throws -> [WritingSessionData] {
        let predicate = NSPredicate(format: "userId == %@", getCurrentUserId())
        let query = CKQuery(recordType: RecordType.writingSession, predicate: predicate)
        query.sortDescriptors = [NSSortDescriptor(key: RecordKey.startTime, ascending: false)]
        
        let (records, _) = try await privateDatabase.records(matching: query)
        
        var sessions: [WritingSessionData] = []
        
        for (_, result) in records {
            switch result {
            case .success(let record):
                if let session = parseWritingSessionRecord(record) {
                    sessions.append(session)
                }
            case .failure(let error):
                print("Failed to fetch session record: \(error)")
            }
        }
        
        return Array(sessions.prefix(limit))
    }
    
    /// Fetch user preferences
    func fetchUserPreferences() async throws -> UserWritingPreferences? {
        let predicate = NSPredicate(format: "userId == %@", getCurrentUserId())
        let query = CKQuery(recordType: RecordType.userPreferences, predicate: predicate)
        
        let (records, _) = try await privateDatabase.records(matching: query)
        
        for (_, result) in records {
            switch result {
            case .success(let record):
                return parseUserPreferencesRecord(record)
            case .failure(let error):
                print("Failed to fetch preferences record: \(error)")
            }
        }
        
        return nil
    }
    
    // MARK: - Private Sync Methods
    
    private func syncWritingProgress() async throws {
        // Fetch local progress data
        let localProgress = getLocalWritingProgress()
        
        // Fetch remote progress data
        let remoteProgress = try await fetchWritingProgress()
        
        // Merge and resolve conflicts
        let mergedProgress = mergeWritingProgress(local: localProgress, remote: remoteProgress)
        
        // Save merged data locally
        saveLocalWritingProgress(mergedProgress)
        
        // Upload any new/updated records
        for progress in mergedProgress {
            if progress.needsSync {
                try await syncCharacterProgress(progress.characterId, progress: progress)
            }
        }
    }
    
    private func syncWritingSessions() async throws {
        let localSessions = getLocalWritingSessions()
        let remoteSessions = try await fetchWritingSessions()
        
        let mergedSessions = mergeWritingSessions(local: localSessions, remote: remoteSessions)
        saveLocalWritingSessions(mergedSessions)
        
        for session in mergedSessions {
            if session.needsSync {
                try await syncWritingSession(session)
            }
        }
    }
    
    private func syncCharacterMastery() async throws {
        // Similar implementation for character mastery data
        print("Syncing character mastery...")
    }
    
    private func syncUserPreferences() async throws {
        let localPreferences = getLocalUserPreferences()
        let remotePreferences = try await fetchUserPreferences()
        
        let mergedPreferences = mergeUserPreferences(local: localPreferences, remote: remotePreferences)
        saveLocalUserPreferences(mergedPreferences)
        
        if mergedPreferences.needsSync {
            try await syncUserPreferences(mergedPreferences)
        }
    }
    
    private func syncPracticeHistory() async throws {
        // Implementation for practice history sync
        print("Syncing practice history...")
    }
    
    // MARK: - Record Parsing
    
    private func parseWritingProgressRecord(_ record: CKRecord) -> WritingProgressData? {
        guard let userIdString = record[RecordKey.userId] as? String,
              let characterIdString = record[RecordKey.characterId] as? String,
              let characterId = UUID(uuidString: characterIdString),
              let accuracy = record[RecordKey.accuracy] as? Double,
              let practiceCount = record[RecordKey.practiceCount] as? Int,
              let lastPracticed = record[RecordKey.lastPracticed] as? Date,
              let masteryLevelString = record[RecordKey.masteryLevel] as? String,
              let masteryLevel = MasteryLevel(rawValue: masteryLevelString) else {
            return nil
        }
        
        return WritingProgressData(
            characterId: characterId,
            accuracy: accuracy,
            practiceCount: practiceCount,
            lastPracticed: lastPracticed,
            masteryLevel: masteryLevel,
            needsSync: false
        )
    }
    
    private func parseWritingSessionRecord(_ record: CKRecord) -> WritingSessionData? {
        guard let userIdString = record[RecordKey.userId] as? String,
              let sessionIdString = record[RecordKey.sessionId] as? String,
              let sessionId = UUID(uuidString: sessionIdString),
              let startTime = record[RecordKey.startTime] as? Date,
              let endTime = record[RecordKey.endTime] as? Date,
              let charactersCount = record[RecordKey.charactersCount] as? Int,
              let averageAccuracy = record[RecordKey.averageAccuracy] as? Double,
              let sessionTypeString = record[RecordKey.sessionType] as? String,
              let sessionType = SessionType(rawValue: sessionTypeString) else {
            return nil
        }
        
        return WritingSessionData(
            id: sessionId,
            startTime: startTime,
            endTime: endTime,
            charactersCount: charactersCount,
            averageAccuracy: averageAccuracy,
            sessionType: sessionType,
            needsSync: false
        )
    }
    
    private func parseUserPreferencesRecord(_ record: CKRecord) -> UserWritingPreferences? {
        guard let userIdString = record[RecordKey.userId] as? String,
              let writingModeString = record[RecordKey.writingMode] as? String,
              let writingMode = WritingMode(rawValue: writingModeString),
              let guidanceEnabled = record[RecordKey.guidanceEnabled] as? Int,
              let soundEnabled = record[RecordKey.soundEnabled] as? Int,
              let hapticEnabled = record[RecordKey.hapticEnabled] as? Int,
              let preferredDifficulty = record[RecordKey.preferredDifficulty] as? Int else {
            return nil
        }
        
        return UserWritingPreferences(
            writingMode: writingMode,
            guidanceEnabled: guidanceEnabled == 1,
            soundEnabled: soundEnabled == 1,
            hapticEnabled: hapticEnabled == 1,
            preferredDifficulty: preferredDifficulty,
            needsSync: false
        )
    }
    
    // MARK: - Data Merging
    
    private func mergeWritingProgress(local: [WritingProgressData], remote: [WritingProgressData]) -> [WritingProgressData] {
        var merged: [UUID: WritingProgressData] = [:]
        
        // Add local data
        for progress in local {
            merged[progress.characterId] = progress
        }
        
        // Merge with remote data (remote takes precedence if more recent)
        for remoteProgress in remote {
            if let localProgress = merged[remoteProgress.characterId] {
                // Use the more recent data
                if remoteProgress.lastPracticed > localProgress.lastPracticed {
                    merged[remoteProgress.characterId] = remoteProgress
                }
            } else {
                merged[remoteProgress.characterId] = remoteProgress
            }
        }
        
        return Array(merged.values)
    }
    
    private func mergeWritingSessions(local: [WritingSessionData], remote: [WritingSessionData]) -> [WritingSessionData] {
        var merged: [UUID: WritingSessionData] = [:]
        
        // Add local sessions
        for session in local {
            merged[session.id] = session
        }
        
        // Add remote sessions (no conflicts expected for sessions)
        for session in remote {
            merged[session.id] = session
        }
        
        return Array(merged.values)
    }
    
    private func mergeUserPreferences(local: UserWritingPreferences?, remote: UserWritingPreferences?) -> UserWritingPreferences {
        // Remote preferences take precedence if they exist
        return remote ?? local ?? UserWritingPreferences.default
    }
    
    // MARK: - CloudKit Subscriptions
    
    private func setupCloudKitSubscriptions() {
        // Set up subscriptions for real-time sync
        Task {
            await setupWritingProgressSubscription()
            await setupWritingSessionSubscription()
            await setupUserPreferencesSubscription()
        }
    }
    
    private func setupWritingProgressSubscription() async {
        let predicate = NSPredicate(format: "userId == %@", getCurrentUserId())
        let subscription = CKQuerySubscription(
            recordType: RecordType.writingProgress,
            predicate: predicate,
            options: [.firesOnRecordCreation, .firesOnRecordUpdate]
        )
        
        let notificationInfo = CKSubscription.NotificationInfo()
        notificationInfo.shouldSendContentAvailable = true
        subscription.notificationInfo = notificationInfo
        
        do {
            try await privateDatabase.save(subscription)
            print("✅ Writing progress subscription created")
        } catch {
            print("❌ Failed to create writing progress subscription: \(error)")
        }
    }
    
    private func setupWritingSessionSubscription() async {
        let predicate = NSPredicate(format: "userId == %@", getCurrentUserId())
        let subscription = CKQuerySubscription(
            recordType: RecordType.writingSession,
            predicate: predicate,
            options: [.firesOnRecordCreation]
        )
        
        let notificationInfo = CKSubscription.NotificationInfo()
        notificationInfo.shouldSendContentAvailable = true
        subscription.notificationInfo = notificationInfo
        
        do {
            try await privateDatabase.save(subscription)
            print("✅ Writing session subscription created")
        } catch {
            print("❌ Failed to create writing session subscription: \(error)")
        }
    }
    
    private func setupUserPreferencesSubscription() async {
        let predicate = NSPredicate(format: "userId == %@", getCurrentUserId())
        let subscription = CKQuerySubscription(
            recordType: RecordType.userPreferences,
            predicate: predicate,
            options: [.firesOnRecordUpdate]
        )
        
        let notificationInfo = CKSubscription.NotificationInfo()
        notificationInfo.shouldSendContentAvailable = true
        subscription.notificationInfo = notificationInfo
        
        do {
            try await privateDatabase.save(subscription)
            print("✅ User preferences subscription created")
        } catch {
            print("❌ Failed to create user preferences subscription: \(error)")
        }
    }
    
    // MARK: - Network Monitoring
    
    private func startNetworkMonitoring() {
        // Monitor network connectivity for sync
        // Implementation would use Network framework
    }
    
    // MARK: - Helper Methods
    
    private func getCurrentUserId() -> String {
        // Return current user ID from authentication
        return "current-user-id"
    }
    
    private func getCurrentDeviceType() -> String {
        #if os(iOS)
        return UIDevice.current.userInterfaceIdiom == .pad ? "iPad" : "iPhone"
        #elseif os(watchOS)
        return "Apple Watch"
        #elseif os(macOS)
        return "Mac"
        #else
        return "Unknown"
        #endif
    }
    
    // MARK: - Local Data Access (Mock implementations)
    
    private func getLocalWritingProgress() -> [WritingProgressData] {
        // Implementation would fetch from Core Data or UserDefaults
        return []
    }
    
    private func saveLocalWritingProgress(_ progress: [WritingProgressData]) {
        // Implementation would save to Core Data or UserDefaults
    }
    
    private func getLocalWritingSessions() -> [WritingSessionData] {
        return []
    }
    
    private func saveLocalWritingSessions(_ sessions: [WritingSessionData]) {
        // Implementation would save to local storage
    }
    
    private func getLocalUserPreferences() -> UserWritingPreferences? {
        return nil
    }
    
    private func saveLocalUserPreferences(_ preferences: UserWritingPreferences) {
        // Implementation would save to UserDefaults
    }
}

// MARK: - Sync Data Models

struct WritingProgressData: Codable, Identifiable {
    let id = UUID()
    let characterId: UUID
    var accuracy: Double
    var practiceCount: Int
    var lastPracticed: Date
    var masteryLevel: MasteryLevel
    var needsSync: Bool

    enum MasteryLevel: String, Codable, CaseIterable {
        case beginner = "beginner"
        case intermediate = "intermediate"
        case advanced = "advanced"
        case mastered = "mastered"

        var displayName: String {
            switch self {
            case .beginner: return "Beginner"
            case .intermediate: return "Intermediate"
            case .advanced: return "Advanced"
            case .mastered: return "Mastered"
            }
        }

        var color: Color {
            switch self {
            case .beginner: return .red
            case .intermediate: return .orange
            case .advanced: return .blue
            case .mastered: return .green
            }
        }
    }
}

struct WritingSessionData: Codable, Identifiable {
    let id: UUID
    let startTime: Date
    let endTime: Date
    let charactersCount: Int
    let averageAccuracy: Double
    let sessionType: SessionType
    var needsSync: Bool

    enum SessionType: String, Codable, CaseIterable {
        case practice = "practice"
        case lesson = "lesson"
        case assessment = "assessment"
        case freeform = "freeform"

        var displayName: String {
            switch self {
            case .practice: return "Practice"
            case .lesson: return "Lesson"
            case .assessment: return "Assessment"
            case .freeform: return "Freeform"
            }
        }

        var icon: String {
            switch self {
            case .practice: return "pencil"
            case .lesson: return "book"
            case .assessment: return "checkmark.circle"
            case .freeform: return "scribble"
            }
        }
    }

    var duration: TimeInterval {
        endTime.timeIntervalSince(startTime)
    }

    var formattedDuration: String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

struct UserWritingPreferences: Codable {
    var writingMode: WritingMode
    var guidanceEnabled: Bool
    var soundEnabled: Bool
    var hapticEnabled: Bool
    var preferredDifficulty: Int
    var needsSync: Bool

    static let `default` = UserWritingPreferences(
        writingMode: .guided,
        guidanceEnabled: true,
        soundEnabled: true,
        hapticEnabled: true,
        preferredDifficulty: 3,
        needsSync: false
    )
}

// MARK: - Cross-Device Sync Manager

@MainActor
class CrossDeviceSyncManager: ObservableObject {
    static let shared = CrossDeviceSyncManager()

    @Published var syncStatus: CloudKitSyncService.SyncStatus = .idle
    @Published var lastSyncDate: Date?
    @Published var autoSyncEnabled = true
    @Published var syncConflicts: [SyncConflict] = []

    private let cloudKitService = CloudKitSyncService.shared
    #if os(watchOS)
    private let watchConnectivity = WatchConnectivityService.shared
    #endif

    private var syncTimer: Timer?

    struct SyncConflict: Identifiable {
        let id = UUID()
        let type: ConflictType
        let localData: Any
        let remoteData: Any
        let timestamp: Date

        enum ConflictType {
            case writingProgress
            case userPreferences
            case sessionData
        }
    }

    private init() {
        setupAutoSync()
        observeCloudKitChanges()
    }

    // MARK: - Public Sync Methods

    /// Perform a full sync across all devices
    func performFullSync() async {
        syncStatus = .syncing

        do {
            // Sync with CloudKit
            await cloudKitService.syncAllData()

            // Sync with Apple Watch if available
            await syncWithAppleWatch()

            // Update local state
            lastSyncDate = Date()
            syncStatus = .success

            print("✅ Cross-device sync completed")

        } catch {
            syncStatus = .error(error.localizedDescription)
            print("❌ Cross-device sync failed: \(error)")
        }
    }

    /// Sync specific writing progress
    func syncWritingProgress(_ characterId: UUID, progress: WritingProgressData) async {
        do {
            try await cloudKitService.syncCharacterProgress(characterId, progress: progress)

            // Also sync to Apple Watch
            await syncProgressToWatch(characterId: characterId, progress: progress)

        } catch {
            print("❌ Failed to sync writing progress: \(error)")
        }
    }

    /// Sync user preferences across devices
    func syncUserPreferences(_ preferences: UserWritingPreferences) async {
        do {
            try await cloudKitService.syncUserPreferences(preferences)

            // Sync to Apple Watch
            await syncPreferencesToWatch(preferences)

        } catch {
            print("❌ Failed to sync user preferences: \(error)")
        }
    }

    /// Resolve sync conflicts
    func resolveSyncConflict(_ conflict: SyncConflict, useLocal: Bool) async {
        // Implementation for conflict resolution
        switch conflict.type {
        case .writingProgress:
            await resolveWritingProgressConflict(conflict, useLocal: useLocal)
        case .userPreferences:
            await resolveUserPreferencesConflict(conflict, useLocal: useLocal)
        case .sessionData:
            await resolveSessionDataConflict(conflict, useLocal: useLocal)
        }

        // Remove resolved conflict
        syncConflicts.removeAll { $0.id == conflict.id }
    }

    // MARK: - Apple Watch Sync

    #if os(watchOS)
    private func syncWithAppleWatch() async {
        guard watchConnectivity.isConnected else { return }

        // Prepare sync data for Apple Watch
        let syncData: [String: Any] = [
            "writing_progress": await getWritingProgressForWatch(),
            "user_preferences": await getUserPreferencesForWatch(),
            "recent_characters": await getRecentCharactersForWatch()
        ]

        watchConnectivity.syncWritingProgress(syncData)
    }

    private func syncProgressToWatch(characterId: UUID, progress: WritingProgressData) async {
        let watchData: [String: Any] = [
            "character_id": characterId.uuidString,
            "accuracy": progress.accuracy,
            "practice_count": progress.practiceCount,
            "mastery_level": progress.masteryLevel.rawValue
        ]

        watchConnectivity.syncWritingProgress(["character_progress": watchData])
    }

    private func syncPreferencesToWatch(_ preferences: UserWritingPreferences) async {
        let watchData: [String: Any] = [
            "writing_mode": preferences.writingMode.rawValue,
            "guidance_enabled": preferences.guidanceEnabled,
            "haptic_enabled": preferences.hapticEnabled
        ]

        watchConnectivity.syncWritingProgress(["user_preferences": watchData])
    }
    #endif

    // MARK: - Auto Sync

    private func setupAutoSync() {
        guard autoSyncEnabled else { return }

        // Sync every 5 minutes when app is active
        syncTimer = Timer.scheduledTimer(withTimeInterval: 300, repeats: true) { _ in
            Task {
                await self.performIncrementalSync()
            }
        }
    }

    private func performIncrementalSync() async {
        // Perform lightweight sync of recent changes
        do {
            let recentProgress = getRecentWritingProgress()
            for progress in recentProgress {
                if progress.needsSync {
                    try await cloudKitService.syncCharacterProgress(progress.characterId, progress: progress)
                }
            }

            let recentSessions = getRecentWritingSessions()
            for session in recentSessions {
                if session.needsSync {
                    try await cloudKitService.syncWritingSession(session)
                }
            }

        } catch {
            print("❌ Incremental sync failed: \(error)")
        }
    }

    // MARK: - CloudKit Change Observation

    private func observeCloudKitChanges() {
        cloudKitService.$syncStatus
            .receive(on: DispatchQueue.main)
            .assign(to: &$syncStatus)

        cloudKitService.$lastSyncDate
            .receive(on: DispatchQueue.main)
            .assign(to: &$lastSyncDate)
    }

    // MARK: - Conflict Resolution

    private func resolveWritingProgressConflict(_ conflict: SyncConflict, useLocal: Bool) async {
        // Implementation for resolving writing progress conflicts
        print("Resolving writing progress conflict, using \(useLocal ? "local" : "remote") data")
    }

    private func resolveUserPreferencesConflict(_ conflict: SyncConflict, useLocal: Bool) async {
        // Implementation for resolving user preferences conflicts
        print("Resolving user preferences conflict, using \(useLocal ? "local" : "remote") data")
    }

    private func resolveSessionDataConflict(_ conflict: SyncConflict, useLocal: Bool) async {
        // Implementation for resolving session data conflicts
        print("Resolving session data conflict, using \(useLocal ? "local" : "remote") data")
    }

    // MARK: - Data Preparation for Watch

    private func getWritingProgressForWatch() async -> [[String: Any]] {
        // Get recent writing progress data formatted for Apple Watch
        let recentProgress = getRecentWritingProgress()
        return recentProgress.map { progress in
            [
                "character_id": progress.characterId.uuidString,
                "accuracy": progress.accuracy,
                "practice_count": progress.practiceCount,
                "mastery_level": progress.masteryLevel.rawValue
            ]
        }
    }

    private func getUserPreferencesForWatch() async -> [String: Any] {
        let preferences = getLocalUserPreferences()
        return [
            "writing_mode": preferences?.writingMode.rawValue ?? "guided",
            "guidance_enabled": preferences?.guidanceEnabled ?? true,
            "haptic_enabled": preferences?.hapticEnabled ?? true
        ]
    }

    private func getRecentCharactersForWatch() async -> [[String: Any]] {
        // Get recently practiced characters for Apple Watch
        let recentCharacters = getRecentlyPracticedCharacters()
        return recentCharacters.map { character in
            [
                "id": character.id.uuidString,
                "character": character.character,
                "romanization": character.romanization,
                "difficulty": character.difficultyLevel
            ]
        }
    }

    // MARK: - Helper Methods (Mock implementations)

    private func getRecentWritingProgress() -> [WritingProgressData] {
        // Implementation would fetch recent progress from local storage
        return []
    }

    private func getRecentWritingSessions() -> [WritingSessionData] {
        // Implementation would fetch recent sessions from local storage
        return []
    }

    private func getLocalUserPreferences() -> UserWritingPreferences? {
        // Implementation would fetch from UserDefaults
        return nil
    }

    private func getRecentlyPracticedCharacters() -> [TamilCharacter] {
        // Implementation would fetch from local storage
        return []
    }

    // MARK: - Public Interface

    func enableAutoSync(_ enabled: Bool) {
        autoSyncEnabled = enabled

        if enabled {
            setupAutoSync()
        } else {
            syncTimer?.invalidate()
            syncTimer = nil
        }
    }

    func forceSyncNow() {
        Task {
            await performFullSync()
        }
    }

    func getSyncStatusText() -> String {
        switch syncStatus {
        case .idle:
            if let lastSync = lastSyncDate {
                let formatter = RelativeDateTimeFormatter()
                return "Last synced \(formatter.localizedString(for: lastSync, relativeTo: Date()))"
            } else {
                return "Not synced yet"
            }
        case .syncing:
            return "Syncing across devices..."
        case .success:
            return "All devices synced"
        case .error(let message):
            return "Sync error: \(message)"
        }
    }
}
