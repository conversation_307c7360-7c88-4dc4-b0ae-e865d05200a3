//
//  ConversationWritingService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import Foundation
import Combine

@MainActor
class ConversationWritingService: ObservableObject {
    static let shared = ConversationWritingService()
    
    @Published var conversationWritingExercises: [ConversationWritingExercise] = []
    @Published var isGeneratingContent = false
    @Published var currentConversation: ConversationData?
    
    private let scriptService = TamilScriptService.shared
    private let contentService = TamilContentService.shared
    private let supabase = SupabaseService.shared.client
    
    private init() {}
    
    // MARK: - Conversation Writing Models
    
    struct ConversationWritingExercise: Identifiable, Codable {
        let id: UUID
        let conversationId: String
        let lessonId: String
        let title: String
        let description: String
        let conversationContext: ConversationContext
        let keyPhrases: [KeyPhrase]
        let writingTasks: [ConversationWritingTask]
        let culturalNotes: [String]
        let difficultyLevel: Int
        let estimatedTime: Int // minutes
        let learningObjectives: [String]
        let successCriteria: ConversationSuccessCriteria
        
        struct ConversationContext: Codable {
            let situation: String
            let participants: [String]
            let setting: String
            let formalityLevel: FormalityLevel
            let culturalBackground: String
            
            enum FormalityLevel: String, Codable, CaseIterable {
                case informal = "informal"
                case formal = "formal"
                case respectful = "respectful"
                case intimate = "intimate"
                
                var displayName: String {
                    switch self {
                    case .informal: return "Informal"
                    case .formal: return "Formal"
                    case .respectful: return "Respectful"
                    case .intimate: return "Intimate"
                    }
                }
            }
        }
        
        struct KeyPhrase: Identifiable, Codable {
            let id: UUID
            let tamil: String
            let romanization: String
            let english: String
            let usage: String
            let formalityLevel: ConversationContext.FormalityLevel
            let targetCharacters: [String]
            let writingTips: [String]
            let culturalSignificance: String?
            let audioUrl: String?
        }
        
        struct ConversationWritingTask: Identifiable, Codable {
            let id: UUID
            let taskNumber: Int
            let taskType: TaskType
            let instruction: String
            let targetPhrase: KeyPhrase
            let context: String
            let writingMode: WritingMode
            let expectedDuration: Int // seconds
            let hints: [String]
            
            enum TaskType: String, Codable, CaseIterable {
                case phraseWriting = "phrase_writing"
                case responseWriting = "response_writing"
                case dialogueCompletion = "dialogue_completion"
                case contextualWriting = "contextual_writing"
                case freeConversation = "free_conversation"
                
                var displayName: String {
                    switch self {
                    case .phraseWriting: return "Phrase Writing"
                    case .responseWriting: return "Response Writing"
                    case .dialogueCompletion: return "Dialogue Completion"
                    case .contextualWriting: return "Contextual Writing"
                    case .freeConversation: return "Free Conversation"
                    }
                }
                
                var icon: String {
                    switch self {
                    case .phraseWriting: return "text.quote"
                    case .responseWriting: return "bubble.left.and.bubble.right"
                    case .dialogueCompletion: return "text.bubble"
                    case .contextualWriting: return "doc.text"
                    case .freeConversation: return "pencil.and.outline"
                    }
                }
            }
        }
        
        struct ConversationSuccessCriteria: Codable {
            let minimumAccuracy: Double
            let requiredPhrases: Int
            let contextualAppropriatenesss: Double
            let timeLimit: Int? // seconds
            let allowedAttempts: Int
        }
    }
    
    struct ConversationData: Codable {
        let id: String
        let title: String
        let participants: [String]
        let dialogue: [DialogueLine]
        let context: String
        let level: CEFRLevel
        
        struct DialogueLine: Identifiable, Codable {
            let id: UUID
            let speaker: String
            let tamil: String
            let romanization: String
            let english: String
            let audioUrl: String?
            let keyPhrases: [String]
        }
    }
    
    // MARK: - Exercise Generation
    
    /// Generate conversation writing exercises for a lesson
    func generateConversationWritingExercises(for lessonId: String) async {
        isGeneratingContent = true
        defer { isGeneratingContent = false }
        
        do {
            // Get conversations for the lesson
            let conversations = try await getConversationsForLesson(lessonId)
            
            var exercises: [ConversationWritingExercise] = []
            
            for conversation in conversations {
                let exercise = await createConversationWritingExercise(
                    conversation: conversation,
                    lessonId: lessonId
                )
                exercises.append(exercise)
            }
            
            conversationWritingExercises.append(contentsOf: exercises)
            
            // Save to database
            await saveConversationExercises(exercises)
            
            print("✅ Generated \(exercises.count) conversation writing exercises")
            
        } catch {
            print("❌ Error generating conversation exercises: \(error)")
        }
    }
    
    /// Create a comprehensive conversation writing exercise
    private func createConversationWritingExercise(
        conversation: ConversationData,
        lessonId: String
    ) async -> ConversationWritingExercise {
        
        // Extract key phrases from conversation
        let keyPhrases = await extractKeyPhrases(from: conversation)
        
        // Create conversation context
        let context = ConversationWritingExercise.ConversationContext(
            situation: conversation.context,
            participants: conversation.participants,
            setting: determineSetting(from: conversation),
            formalityLevel: determineFormalityLevel(from: conversation),
            culturalBackground: generateCulturalBackground(for: conversation)
        )
        
        // Create writing tasks
        let writingTasks = createConversationWritingTasks(
            keyPhrases: keyPhrases,
            conversation: conversation
        )
        
        // Generate cultural notes
        let culturalNotes = generateConversationCulturalNotes(conversation: conversation)
        
        // Calculate difficulty and time
        let difficultyLevel = calculateConversationDifficulty(conversation: conversation)
        let estimatedTime = calculateConversationTime(tasks: writingTasks)
        
        // Define learning objectives
        let learningObjectives = generateLearningObjectives(conversation: conversation)
        
        // Set success criteria
        let successCriteria = ConversationWritingExercise.ConversationSuccessCriteria(
            minimumAccuracy: 75.0,
            requiredPhrases: keyPhrases.count,
            contextualAppropriatenesss: 80.0,
            timeLimit: estimatedTime * 60,
            allowedAttempts: 3
        )
        
        return ConversationWritingExercise(
            id: UUID(),
            conversationId: conversation.id,
            lessonId: lessonId,
            title: "Writing Practice: \(conversation.title)",
            description: "Practice writing key phrases and responses from the conversation",
            conversationContext: context,
            keyPhrases: keyPhrases,
            writingTasks: writingTasks,
            culturalNotes: culturalNotes,
            difficultyLevel: difficultyLevel,
            estimatedTime: estimatedTime,
            learningObjectives: learningObjectives,
            successCriteria: successCriteria
        )
    }
    
    // MARK: - Key Phrase Extraction
    
    private func extractKeyPhrases(from conversation: ConversationData) async -> [ConversationWritingExercise.KeyPhrase] {
        var keyPhrases: [ConversationWritingExercise.KeyPhrase] = []
        
        for line in conversation.dialogue {
            // Extract important phrases from each dialogue line
            let phrases = identifyKeyPhrasesInLine(line)
            
            for phrase in phrases {
                let keyPhrase = ConversationWritingExercise.KeyPhrase(
                    id: UUID(),
                    tamil: phrase,
                    romanization: generateRomanization(for: phrase),
                    english: generateEnglishTranslation(for: phrase),
                    usage: generateUsageNote(for: phrase, in: conversation),
                    formalityLevel: determineFormalityLevel(from: conversation),
                    targetCharacters: extractCharacters(from: phrase),
                    writingTips: generateWritingTips(for: phrase),
                    culturalSignificance: generateCulturalSignificance(for: phrase),
                    audioUrl: nil // Would be generated separately
                )
                
                keyPhrases.append(keyPhrase)
            }
        }
        
        // Remove duplicates and limit to most important phrases
        return Array(Set(keyPhrases.map { $0.tamil }))
            .compactMap { tamil in keyPhrases.first { $0.tamil == tamil } }
            .prefix(8)
            .map { $0 }
    }
    
    private func identifyKeyPhrasesInLine(_ line: ConversationData.DialogueLine) -> [String] {
        // Extract key phrases from dialogue line
        // This would use NLP techniques in practice
        
        let commonGreetings = ["வணக்கம்", "நன்றி", "மன்னிக்கவும்"]
        let commonQuestions = ["எப்படி இருக்கிறீர்கள்?", "என்ன செய்கிறீர்கள்?"]
        let commonResponses = ["நல்லா இருக்கேன்", "சரி", "ஓகே"]
        
        var phrases: [String] = []
        
        // Check for common patterns
        for greeting in commonGreetings {
            if line.tamil.contains(greeting) {
                phrases.append(greeting)
            }
        }
        
        for question in commonQuestions {
            if line.tamil.contains(question) {
                phrases.append(question)
            }
        }
        
        for response in commonResponses {
            if line.tamil.contains(response) {
                phrases.append(response)
            }
        }
        
        // If no common phrases found, use the whole line if it's short
        if phrases.isEmpty && line.tamil.count <= 20 {
            phrases.append(line.tamil)
        }
        
        return phrases
    }
    
    // MARK: - Writing Task Creation
    
    private func createConversationWritingTasks(
        keyPhrases: [ConversationWritingExercise.KeyPhrase],
        conversation: ConversationData
    ) -> [ConversationWritingExercise.ConversationWritingTask] {
        
        var tasks: [ConversationWritingExercise.ConversationWritingTask] = []
        var taskNumber = 1
        
        // Task 1: Practice key phrases
        for phrase in keyPhrases.prefix(3) {
            tasks.append(ConversationWritingExercise.ConversationWritingTask(
                id: UUID(),
                taskNumber: taskNumber,
                taskType: .phraseWriting,
                instruction: "Practice writing the phrase '\(phrase.tamil)'",
                targetPhrase: phrase,
                context: "This phrase is used \(phrase.usage.lowercased())",
                writingMode: .guided,
                expectedDuration: 60,
                hints: phrase.writingTips
            ))
            taskNumber += 1
        }
        
        // Task 2: Write appropriate responses
        if keyPhrases.count > 1 {
            let responsePhrase = keyPhrases[1]
            tasks.append(ConversationWritingExercise.ConversationWritingTask(
                id: UUID(),
                taskNumber: taskNumber,
                taskType: .responseWriting,
                instruction: "Write an appropriate response",
                targetPhrase: responsePhrase,
                context: "Respond to: \(keyPhrases[0].tamil)",
                writingMode: .freeform,
                expectedDuration: 90,
                hints: [
                    "Consider the formality level",
                    "Use appropriate Tamil expressions",
                    "Maintain cultural politeness"
                ]
            ))
            taskNumber += 1
        }
        
        // Task 3: Complete dialogue
        if keyPhrases.count > 2 {
            tasks.append(ConversationWritingExercise.ConversationWritingTask(
                id: UUID(),
                taskNumber: taskNumber,
                taskType: .dialogueCompletion,
                instruction: "Complete the conversation",
                targetPhrase: keyPhrases[2],
                context: "Fill in the missing part of the dialogue",
                writingMode: .freeform,
                expectedDuration: 120,
                hints: [
                    "Keep the conversation natural",
                    "Use phrases you've learned",
                    "Maintain the conversation flow"
                ]
            ))
        }
        
        return tasks
    }
    
    // MARK: - Helper Methods
    
    private func getConversationsForLesson(_ lessonId: String) async throws -> [ConversationData] {
        // Mock conversation data - in practice, this would fetch from database
        switch lessonId {
        case "A1_BASIC_GREETINGS":
            return [
                ConversationData(
                    id: "greeting_conversation_1",
                    title: "Meeting Someone New",
                    participants: ["Person A", "Person B"],
                    dialogue: [
                        ConversationData.DialogueLine(
                            id: UUID(),
                            speaker: "Person A",
                            tamil: "வணக்கம்! நான் ராம்.",
                            romanization: "Vanakkam! Naan Ram.",
                            english: "Hello! I am Ram.",
                            audioUrl: nil,
                            keyPhrases: ["வணக்கம்", "நான்"]
                        ),
                        ConversationData.DialogueLine(
                            id: UUID(),
                            speaker: "Person B",
                            tamil: "வணக்கம் ராம்! நான் சீதா.",
                            romanization: "Vanakkam Ram! Naan Seetha.",
                            english: "Hello Ram! I am Seetha.",
                            audioUrl: nil,
                            keyPhrases: ["வணக்கம்", "நான்"]
                        )
                    ],
                    context: "Two people meeting for the first time",
                    level: .a1
                )
            ]
        default:
            return []
        }
    }
    
    private func determineSetting(from conversation: ConversationData) -> String {
        // Analyze conversation to determine setting
        if conversation.context.contains("meeting") {
            return "Social gathering"
        } else if conversation.context.contains("shop") {
            return "Shopping"
        } else if conversation.context.contains("home") {
            return "Home"
        } else {
            return "General"
        }
    }
    
    private func determineFormalityLevel(from conversation: ConversationData) -> ConversationWritingExercise.ConversationContext.FormalityLevel {
        // Analyze conversation for formality markers
        let formalMarkers = ["நீங்கள்", "இருக்கிறீர்கள்", "வருகிறீர்கள்"]
        let informalMarkers = ["நீ", "இருக்க", "வர"]
        
        let text = conversation.dialogue.map { $0.tamil }.joined(separator: " ")
        
        let formalCount = formalMarkers.filter { text.contains($0) }.count
        let informalCount = informalMarkers.filter { text.contains($0) }.count
        
        if formalCount > informalCount {
            return .formal
        } else if informalCount > 0 {
            return .informal
        } else {
            return .respectful
        }
    }
    
    private func generateRomanization(for phrase: String) -> String {
        // Simple romanization mapping - would use proper transliteration in practice
        let romanizationMap: [String: String] = [
            "வணக்கம்": "vanakkam",
            "நான்": "naan",
            "நன்றி": "nandri",
            "மன்னிக்கவும்": "mannikkavum"
        ]
        
        return romanizationMap[phrase] ?? phrase
    }
    
    private func generateEnglishTranslation(for phrase: String) -> String {
        let translationMap: [String: String] = [
            "வணக்கம்": "Hello/Greetings",
            "நான்": "I",
            "நன்றி": "Thank you",
            "மன்னிக்கவும்": "Excuse me/Sorry"
        ]
        
        return translationMap[phrase] ?? phrase
    }
    
    private func generateUsageNote(for phrase: String, in conversation: ConversationData) -> String {
        switch phrase {
        case "வணக்கம்":
            return "as a universal greeting throughout the day"
        case "நான்":
            return "to introduce yourself"
        case "நன்றி":
            return "to express gratitude"
        default:
            return "in everyday conversation"
        }
    }
    
    private func extractCharacters(from phrase: String) -> [String] {
        return phrase.map { String($0) }
    }
    
    private func generateWritingTips(for phrase: String) -> [String] {
        switch phrase {
        case "வணக்கம்":
            return [
                "Start with வ - remember the curve",
                "ண் has a distinctive shape",
                "End with ம் - practice the final form"
            ]
        case "நான்":
            return [
                "நா is a common combination",
                "Final ன் is different from ண்"
            ]
        default:
            return [
                "Write slowly and carefully",
                "Pay attention to character spacing",
                "Practice each character separately first"
            ]
        }
    }
    
    private func generateCulturalSignificance(for phrase: String) -> String? {
        switch phrase {
        case "வணக்கம்":
            return "வணக்கம் is more than just 'hello' - it's a respectful acknowledgment of the other person"
        case "நன்றி":
            return "Expressing gratitude is deeply valued in Tamil culture"
        default:
            return nil
        }
    }
    
    private func generateCulturalBackground(for conversation: ConversationData) -> String {
        return "Tamil conversations emphasize respect, politeness, and appropriate social hierarchy"
    }
    
    private func generateConversationCulturalNotes(conversation: ConversationData) -> [String] {
        return [
            "Tamil greetings show respect and acknowledgment",
            "The choice of pronouns reflects social relationships",
            "Politeness is built into the language structure"
        ]
    }
    
    private func calculateConversationDifficulty(conversation: ConversationData) -> Int {
        let baseLevel = conversation.level == .a1 ? 1 : conversation.level == .a2 ? 2 : 3
        let dialogueLength = conversation.dialogue.count
        return min(5, baseLevel + (dialogueLength > 4 ? 1 : 0))
    }
    
    private func calculateConversationTime(tasks: [ConversationWritingExercise.ConversationWritingTask]) -> Int {
        let totalSeconds = tasks.map { $0.expectedDuration }.reduce(0, +)
        return (totalSeconds / 60) + 5 // Add buffer time
    }
    
    private func generateLearningObjectives(conversation: ConversationData) -> [String] {
        return [
            "Practice writing conversational Tamil phrases",
            "Learn appropriate responses in social contexts",
            "Understand cultural nuances in Tamil communication",
            "Develop fluency in common dialogue patterns"
        ]
    }
    
    private func saveConversationExercises(_ exercises: [ConversationWritingExercise]) async {
        // Save to database - implementation would convert to appropriate format
        for exercise in exercises {
            print("Saving conversation exercise: \(exercise.title)")
        }
    }
    
    // MARK: - Public Interface
    
    /// Get conversation writing exercises for a lesson
    func getConversationExercisesForLesson(_ lessonId: String) -> [ConversationWritingExercise] {
        return conversationWritingExercises.filter { $0.lessonId == lessonId }
    }
    
    /// Get exercises by conversation ID
    func getExercisesByConversation(_ conversationId: String) -> [ConversationWritingExercise] {
        return conversationWritingExercises.filter { $0.conversationId == conversationId }
    }
    
    /// Load conversation data
    func loadConversation(_ conversationId: String) async {
        // Load and set current conversation
        // Implementation would fetch from database
    }
}
