//
//  EnhancedPracticeExerciseGenerator.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 25/06/2025.
//

import Foundation

/// Enhanced Practice Exercise Generator with proper Tamil content, romanization, and pronunciation
@MainActor
class EnhancedPracticeExerciseGenerator: ObservableObject {
    static let shared = EnhancedPracticeExerciseGenerator()
    
    @Published var isGenerating = false
    @Published var generationProgress = 0.0
    @Published var statusMessage = ""
    
    private init() {}
    
    /// Generate high-quality practice exercises from lesson content
    func generateQualityPracticeExercises(
        from vocabulary: [TamilSupabaseVocabulary],
        conversations: [TamilSupabaseConversation],
        grammar: [TamilSupabaseGrammarTopic],
        lessonId: String
    ) -> [TamilSupabasePracticeExercise] {
        
        print("🎯 Generating quality practice exercises for lesson: \(lessonId)")
        
        var exercises: [TamilSupabasePracticeExercise] = []
        
        // Generate vocabulary-based exercises (4 exercises)
        exercises.append(contentsOf: generateVocabularyExercises(vocabulary: vocabulary, lessonId: lessonId))
        
        // Generate conversation-based exercises (3 exercises)
        exercises.append(contentsOf: generateConversationExercises(conversations: conversations, vocabulary: vocabulary, lessonId: lessonId))
        
        // Generate grammar-based exercises (3 exercises)
        exercises.append(contentsOf: generateGrammarExercises(grammar: grammar, vocabulary: vocabulary, lessonId: lessonId))
        
        print("✅ Generated \(exercises.count) quality practice exercises")
        return exercises
    }
    
    // MARK: - Vocabulary Exercises
    
    private func generateVocabularyExercises(vocabulary: [TamilSupabaseVocabulary], lessonId: String) -> [TamilSupabasePracticeExercise] {
        var exercises: [TamilSupabasePracticeExercise] = []
        
        // Exercise 1: Tamil to English Translation
        if let exercise = createTamilToEnglishExercise(vocabulary: vocabulary, lessonId: lessonId, exerciseNumber: 1) {
            exercises.append(exercise)
        }
        
        // Exercise 2: English to Tamil Translation
        if let exercise = createEnglishToTamilExercise(vocabulary: vocabulary, lessonId: lessonId, exerciseNumber: 2) {
            exercises.append(exercise)
        }
        
        // Exercise 3: Fill in the Blank with Tamil
        if let exercise = createFillBlankTamilExercise(vocabulary: vocabulary, lessonId: lessonId, exerciseNumber: 3) {
            exercises.append(exercise)
        }
        
        // Exercise 4: Pronunciation Recognition
        if let exercise = createPronunciationExercise(vocabulary: vocabulary, lessonId: lessonId, exerciseNumber: 4) {
            exercises.append(exercise)
        }
        
        return exercises
    }
    
    private func createTamilToEnglishExercise(vocabulary: [TamilSupabaseVocabulary], lessonId: String, exerciseNumber: Int) -> TamilSupabasePracticeExercise? {
        guard vocabulary.count >= 4 else { return nil }
        
        // Select a random vocabulary item
        let targetVocab = vocabulary.randomElement()!
        let otherVocab = vocabulary.filter { $0.id != targetVocab.id }.shuffled().prefix(3)
        
        // Create proper question
        let question = "What is the English meaning of '\(targetVocab.tamilTranslation)'?"
        let questionTamil = "'\(targetVocab.tamilTranslation)' என்பதன் ஆங்கில அர்த்தம் என்ன?"
        
        // Create answer options
        var options = [targetVocab.englishWord]
        options.append(contentsOf: otherVocab.map { $0.englishWord })
        options.shuffle()
        
        let correctIndex = options.firstIndex(of: targetVocab.englishWord) ?? 0
        
        // Create exercise with proper data
        var exercise = TamilSupabasePracticeExercise(
            id: UUID().uuidString,
            lessonId: lessonId,
            exerciseId: "L1E\(exerciseNumber)",
            exerciseType: "multiple_choice",
            titleEnglish: "Tamil to English",
            titleTamil: "தமிழிலிருந்து ஆங்கிலம்",
            instructionsEnglish: "Select the correct English meaning of the Tamil word.",
            instructionsTamil: "தமிழ் சொல்லின் சரியான ஆங்கில அர்த்தத்தைத் தேர்ந்தெடு.",
            difficultyLevel: 1,
            pointsValue: 10,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )
        
        // Add detailed question data with romanization and pronunciation
        exercise.questionData = ExerciseQuestionData(
            question: question,
            questionTamil: questionTamil,
            options: options,
            optionsTamil: options, // English options remain the same
            optionsRomanization: nil, // Not needed for English options
            optionsPronunciation: nil, // Not needed for English options
            optionsAudioUrls: nil, // Not needed for English options
            correctAnswer: correctIndex,
            correctAnswers: nil,
            explanation: "'\(targetVocab.tamilTranslation)' means '\(targetVocab.englishWord)' in English.",
            explanationTamil: "'\(targetVocab.tamilTranslation)' என்பது ஆங்கிலத்தில் '\(targetVocab.englishWord)' என்று பொருள்.",
            audioQuestionUrl: targetVocab.audioWordUrl,
            matchPairs: nil,
            fillBlanks: nil
        )
        
        return exercise
    }
    
    private func createEnglishToTamilExercise(vocabulary: [TamilSupabaseVocabulary], lessonId: String, exerciseNumber: Int) -> TamilSupabasePracticeExercise? {
        guard vocabulary.count >= 4 else { return nil }
        
        // Select a random vocabulary item
        let targetVocab = vocabulary.randomElement()!
        let otherVocab = vocabulary.filter { $0.id != targetVocab.id }.shuffled().prefix(3)
        
        // Create proper question
        let question = "How do you say '\(targetVocab.englishWord)' in Tamil?"
        let questionTamil = "'\(targetVocab.englishWord)' என்பதை தமிழில் எப்படி சொல்வது?"
        
        // Create answer options with romanization
        var options = [targetVocab.tamilTranslation]
        var optionsRomanization = [targetVocab.romanization]
        
        for vocab in otherVocab {
            options.append(vocab.tamilTranslation)
            optionsRomanization.append(vocab.romanization)
        }
        
        // Shuffle while maintaining correspondence
        let combined = zip(options, optionsRomanization).shuffled()
        options = combined.map { $0.0 }
        optionsRomanization = combined.map { $0.1 }
        
        let correctIndex = options.firstIndex(of: targetVocab.tamilTranslation) ?? 0
        
        // Create exercise
        var exercise = TamilSupabasePracticeExercise(
            id: UUID().uuidString,
            lessonId: lessonId,
            exerciseId: "L1E\(exerciseNumber)",
            exerciseType: "multiple_choice",
            titleEnglish: "English to Tamil",
            titleTamil: "ஆங்கிலத்திலிருந்து தமிழ்",
            instructionsEnglish: "Select the correct Tamil translation of the English word.",
            instructionsTamil: "ஆங்கில சொல்லின் சரியான தமிழ் மொழிபெயர்ப்பைத் தேர்ந்தெடு.",
            difficultyLevel: 1,
            pointsValue: 10,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )
        
        // Add detailed question data with romanization
        exercise.questionData = ExerciseQuestionData(
            question: question,
            questionTamil: questionTamil,
            options: options,
            optionsTamil: options, // Tamil options
            optionsRomanization: optionsRomanization, // Basic learners need romanization!
            optionsPronunciation: nil, // Could add pronunciation guides later
            optionsAudioUrls: nil, // Could add audio URLs later
            correctAnswer: correctIndex,
            correctAnswers: nil,
            explanation: "'\(targetVocab.englishWord)' is '\(targetVocab.tamilTranslation)' (\(targetVocab.romanization)) in Tamil.",
            explanationTamil: "'\(targetVocab.englishWord)' என்பது தமிழில் '\(targetVocab.tamilTranslation)' (\(targetVocab.romanization)).",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )
        
        return exercise
    }
    
    private func createFillBlankTamilExercise(vocabulary: [TamilSupabaseVocabulary], lessonId: String, exerciseNumber: Int) -> TamilSupabasePracticeExercise? {
        guard let targetVocab = vocabulary.first(where: { $0.exampleSentenceTamil != nil && !$0.exampleSentenceTamil!.isEmpty }) else { return nil }
        
        let sentence = targetVocab.exampleSentenceTamil!
        let sentenceWithBlank = sentence.replacingOccurrences(of: targetVocab.tamilTranslation, with: "______")
        
        // Create options
        let otherVocab = vocabulary.filter { $0.id != targetVocab.id }.shuffled().prefix(3)
        var options = [targetVocab.tamilTranslation]
        options.append(contentsOf: otherVocab.map { $0.tamilTranslation })
        options.shuffle()
        
        let correctIndex = options.firstIndex(of: targetVocab.tamilTranslation) ?? 0
        
        // Create exercise
        var exercise = TamilSupabasePracticeExercise(
            id: UUID().uuidString,
            lessonId: lessonId,
            exerciseId: "L1E\(exerciseNumber)",
            exerciseType: "fill_blank",
            titleEnglish: "Complete the Sentence",
            titleTamil: "வாக்கியத்தை நிறைவு செய்",
            instructionsEnglish: "Fill in the blank with the correct Tamil word.",
            instructionsTamil: "வெற்று இடத்தை சரியான தமிழ் சொல்லால் நிரப்பவும்.",
            difficultyLevel: 2,
            pointsValue: 15,
            timeLimitSeconds: 45,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )
        
        // Add detailed question data
        exercise.questionData = ExerciseQuestionData(
            question: sentenceWithBlank,
            questionTamil: "வெற்று இடத்தை நிரப்பவும்: \(sentenceWithBlank)",
            options: options,
            optionsTamil: options,
            optionsRomanization: nil, // Could add romanization for Tamil options
            optionsPronunciation: nil, // Could add pronunciation guides
            optionsAudioUrls: nil, // Could add audio URLs
            correctAnswer: correctIndex,
            correctAnswers: nil,
            explanation: "The correct word is '\(targetVocab.tamilTranslation)' (\(targetVocab.romanization)) meaning '\(targetVocab.englishWord)'.",
            explanationTamil: "சரியான சொல் '\(targetVocab.tamilTranslation)' (\(targetVocab.romanization)) என்பது '\(targetVocab.englishWord)' என்று பொருள்.",
            audioQuestionUrl: targetVocab.audioSentenceUrl,
            matchPairs: nil,
            fillBlanks: [targetVocab.tamilTranslation]
        )
        
        return exercise
    }
    
    private func createPronunciationExercise(vocabulary: [TamilSupabaseVocabulary], lessonId: String, exerciseNumber: Int) -> TamilSupabasePracticeExercise? {
        guard vocabulary.count >= 4 else { return nil }
        
        let targetVocab = vocabulary.randomElement()!
        let otherVocab = vocabulary.filter { $0.id != targetVocab.id }.shuffled().prefix(3)
        
        // Create romanization-based question
        let question = "Which Tamil word is pronounced as '\(targetVocab.romanization)'?"
        let questionTamil = "'\(targetVocab.romanization)' என்று உச்சரிக்கப்படும் தமிழ் சொல் எது?"
        
        // Create options
        var options = [targetVocab.tamilTranslation]
        options.append(contentsOf: otherVocab.map { $0.tamilTranslation })
        options.shuffle()
        
        let correctIndex = options.firstIndex(of: targetVocab.tamilTranslation) ?? 0
        
        // Create exercise
        var exercise = TamilSupabasePracticeExercise(
            id: UUID().uuidString,
            lessonId: lessonId,
            exerciseId: "L1E\(exerciseNumber)",
            exerciseType: "multiple_choice",
            titleEnglish: "Pronunciation Recognition",
            titleTamil: "உச்சரிப்பு அடையாளம்",
            instructionsEnglish: "Listen to the pronunciation and select the correct Tamil word.",
            instructionsTamil: "உச்சரிப்பைக் கேட்டு சரியான தமிழ் சொல்லைத் தேர்ந்தெடு.",
            difficultyLevel: 2,
            pointsValue: 15,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )
        
        // Add detailed question data
        exercise.questionData = ExerciseQuestionData(
            question: question,
            questionTamil: questionTamil,
            options: options,
            optionsTamil: options,
            optionsRomanization: nil, // Could add romanization for options
            optionsPronunciation: nil, // Could add pronunciation guides
            optionsAudioUrls: nil, // Could add audio URLs
            correctAnswer: correctIndex,
            correctAnswers: nil,
            explanation: "'\(targetVocab.romanization)' is the pronunciation of '\(targetVocab.tamilTranslation)' meaning '\(targetVocab.englishWord)'.",
            explanationTamil: "'\(targetVocab.romanization)' என்பது '\(targetVocab.tamilTranslation)' என்ற சொல்லின் உச்சரிப்பு, இது '\(targetVocab.englishWord)' என்று பொருள்.",
            audioQuestionUrl: targetVocab.audioWordUrl,
            matchPairs: nil,
            fillBlanks: nil
        )
        
        return exercise
    }

    // MARK: - Conversation Exercises

    private func generateConversationExercises(conversations: [TamilSupabaseConversation], vocabulary: [TamilSupabaseVocabulary], lessonId: String) -> [TamilSupabasePracticeExercise] {
        var exercises: [TamilSupabasePracticeExercise] = []

        // Exercise 5: Context Understanding
        if let exercise = createContextExercise(conversations: conversations, lessonId: lessonId, exerciseNumber: 5) {
            exercises.append(exercise)
        }

        // Exercise 6: Appropriate Response
        if let exercise = createResponseExercise(conversations: conversations, vocabulary: vocabulary, lessonId: lessonId, exerciseNumber: 6) {
            exercises.append(exercise)
        }

        // Exercise 7: Conversation Completion
        if let exercise = createConversationCompletionExercise(conversations: conversations, vocabulary: vocabulary, lessonId: lessonId, exerciseNumber: 7) {
            exercises.append(exercise)
        }

        return exercises
    }

    private func createContextExercise(conversations: [TamilSupabaseConversation], lessonId: String, exerciseNumber: Int) -> TamilSupabasePracticeExercise? {
        guard let conversation = conversations.randomElement() else { return nil }

        let question = "What is the context of this conversation?"
        let questionTamil = "இந்த உரையாடலின் சூழல் என்ன?"

        // Create context-based options
        let correctContext = conversation.contextDescription ?? "Meeting and greeting"
        let options = [
            correctContext,
            "Asking for directions",
            "Ordering food",
            "Shopping at market"
        ].shuffled()

        let optionsTamil = [
            "சந்திப்பு மற்றும் வாழ்த்து",
            "வழி கேட்டல்",
            "உணவு ஆர்டர் செய்தல்",
            "சந்தையில் வாங்குதல்"
        ].shuffled()

        let correctIndex = options.firstIndex(of: correctContext) ?? 0

        var exercise = TamilSupabasePracticeExercise(
            id: UUID().uuidString,
            lessonId: lessonId,
            exerciseId: "L1E\(exerciseNumber)",
            exerciseType: "multiple_choice",
            titleEnglish: "Conversation Context",
            titleTamil: "உரையாடல் சூழல்",
            instructionsEnglish: "Listen to the conversation and identify the context.",
            instructionsTamil: "உரையாடலைக் கேட்டு சூழலை அடையாளம் காணவும்.",
            difficultyLevel: 2,
            pointsValue: 15,
            timeLimitSeconds: 60,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        exercise.questionData = ExerciseQuestionData(
            question: question,
            questionTamil: questionTamil,
            options: options,
            optionsTamil: optionsTamil,
            optionsRomanization: nil, // Could add romanization for Tamil options
            optionsPronunciation: nil, // Could add pronunciation guides
            optionsAudioUrls: nil, // Could add audio URLs
            correctAnswer: correctIndex,
            correctAnswers: nil,
            explanation: "This conversation is about \(correctContext.lowercased()).",
            explanationTamil: "இந்த உரையாடல் \(optionsTamil[correctIndex]) பற்றியது.",
            audioQuestionUrl: conversation.audioFullUrl,
            matchPairs: nil,
            fillBlanks: nil
        )

        return exercise
    }

    private func createResponseExercise(conversations: [TamilSupabaseConversation], vocabulary: [TamilSupabaseVocabulary], lessonId: String, exerciseNumber: Int) -> TamilSupabasePracticeExercise? {
        guard let conversation = conversations.randomElement() else { return nil }

        // Use greeting vocabulary for appropriate responses
        let greetingVocab = vocabulary.filter { $0.englishWord.lowercased().contains("hello") || $0.englishWord.lowercased().contains("good") }
        guard !greetingVocab.isEmpty else { return nil }

        let question = "What is the appropriate response to 'வணக்கம்' (Hello)?"
        let questionTamil = "'வணக்கம்' என்பதற்கு சரியான பதில் என்ன?"

        let options = [
            "வணக்கம்",
            "நன்றி",
            "மன்னிக்கவும்",
            "போய் வாருங்கள்"
        ]

        let optionsRomanization = [
            "Vanakkam",
            "Nandri",
            "Mannikkavum",
            "Poy varungal"
        ]

        let correctIndex = 0 // வணக்கம் is the correct response

        var exercise = TamilSupabasePracticeExercise(
            id: UUID().uuidString,
            lessonId: lessonId,
            exerciseId: "L1E\(exerciseNumber)",
            exerciseType: "multiple_choice",
            titleEnglish: "Appropriate Response",
            titleTamil: "சரியான பதில்",
            instructionsEnglish: "Choose the most appropriate response in Tamil.",
            instructionsTamil: "தமிழில் மிகவும் பொருத்தமான பதிலைத் தேர்ந்தெடு.",
            difficultyLevel: 2,
            pointsValue: 15,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        exercise.questionData = ExerciseQuestionData(
            question: question,
            questionTamil: questionTamil,
            options: options,
            optionsTamil: options, // Tamil options
            optionsRomanization: optionsRomanization, // Basic learners need romanization!
            optionsPronunciation: nil, // Could add pronunciation guides
            optionsAudioUrls: nil, // Could add audio URLs
            correctAnswer: correctIndex,
            correctAnswers: nil,
            explanation: "When someone says 'வணக்கம்' (Hello), the appropriate response is also 'வணக்கம்' (Hello).",
            explanationTamil: "யாராவது 'வணக்கம்' என்று சொன்னால், சரியான பதில் 'வணக்கம்' தான்.",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )

        return exercise
    }

    private func createConversationCompletionExercise(conversations: [TamilSupabaseConversation], vocabulary: [TamilSupabaseVocabulary], lessonId: String, exerciseNumber: Int) -> TamilSupabasePracticeExercise? {
        guard let conversation = conversations.randomElement() else { return nil }

        let question = "Complete the conversation: A: வணக்கம்! B: ______"
        let questionTamil = "உரையாடலை நிறைவு செய்யவும்: அ: வணக்கம்! ஆ: ______"

        let options = [
            "வணக்கம்! எப்படி இருக்கிறீர்கள்?",
            "நன்றி, போகிறேன்",
            "தெரியாது",
            "பார்க்கிறேன்"
        ]

        let optionsRomanization = [
            "Vanakkam! Eppadi irukkireergal?",
            "Nandri, pogiren",
            "Theriyathu",
            "Parkiren"
        ]

        let correctIndex = 0

        var exercise = TamilSupabasePracticeExercise(
            id: UUID().uuidString,
            lessonId: lessonId,
            exerciseId: "L1E\(exerciseNumber)",
            exerciseType: "multiple_choice",
            titleEnglish: "Complete the Conversation",
            titleTamil: "உரையாடலை நிறைவு செய்",
            instructionsEnglish: "Choose the best way to continue this conversation.",
            instructionsTamil: "இந்த உரையாடலைத் தொடர சிறந்த வழியைத் தேர்ந்தெடு.",
            difficultyLevel: 3,
            pointsValue: 20,
            timeLimitSeconds: 45,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        exercise.questionData = ExerciseQuestionData(
            question: question,
            questionTamil: questionTamil,
            options: options,
            optionsTamil: options, // Tamil options
            optionsRomanization: optionsRomanization, // Basic learners need romanization!
            optionsPronunciation: nil, // Could add pronunciation guides
            optionsAudioUrls: nil, // Could add audio URLs
            correctAnswer: correctIndex,
            correctAnswers: nil,
            explanation: "After greeting with 'வணக்கம்', it's polite to ask 'எப்படி இருக்கிறீர்கள்?' (How are you?).",
            explanationTamil: "'வணக்கம்' என்று வாழ்த்திய பிறகு, 'எப்படி இருக்கிறீர்கள்?' என்று கேட்பது நல்லது.",
            audioQuestionUrl: conversation.audioFullUrl,
            matchPairs: nil,
            fillBlanks: nil
        )

        return exercise
    }

    // MARK: - Grammar Exercises

    private func generateGrammarExercises(grammar: [TamilSupabaseGrammarTopic], vocabulary: [TamilSupabaseVocabulary], lessonId: String) -> [TamilSupabasePracticeExercise] {
        var exercises: [TamilSupabasePracticeExercise] = []

        // Exercise 8: Grammar Rule Recognition
        if let exercise = createGrammarRuleExercise(grammar: grammar, lessonId: lessonId, exerciseNumber: 8) {
            exercises.append(exercise)
        }

        // Exercise 9: Grammar Application
        if let exercise = createGrammarApplicationExercise(grammar: grammar, vocabulary: vocabulary, lessonId: lessonId, exerciseNumber: 9) {
            exercises.append(exercise)
        }

        // Exercise 10: True/False Grammar
        if let exercise = createGrammarTrueFalseExercise(grammar: grammar, lessonId: lessonId, exerciseNumber: 10) {
            exercises.append(exercise)
        }

        return exercises
    }

    private func createGrammarRuleExercise(grammar: [TamilSupabaseGrammarTopic], lessonId: String, exerciseNumber: Int) -> TamilSupabasePracticeExercise? {
        guard let grammarTopic = grammar.randomElement() else { return nil }

        let question = "What is the grammar rule for '\(grammarTopic.titleEnglish)'?"
        let questionTamil = "'\(grammarTopic.titleTamil)' க்கான இலக்கண விதி என்ன?"

        // Create options with the correct rule and some distractors
        let correctRule = grammarTopic.ruleEnglish
        let options = [
            correctRule,
            "Add 'ஆன்' suffix to make past tense",
            "Use 'இல்' for negative sentences",
            "Place verb at the beginning of sentence"
        ].shuffled()

        let optionsTamil = [
            grammarTopic.ruleTamil,
            "கடந்த காலத்திற்கு 'ஆன்' பின்னொட்டு சேர்க்கவும்",
            "எதிர்மறை வாக்கியங்களுக்கு 'இல்' பயன்படுத்தவும்",
            "வாக்கியத்தின் தொடக்கத்தில் வினையை வைக்கவும்"
        ].shuffled()

        let correctIndex = options.firstIndex(of: correctRule) ?? 0

        var exercise = TamilSupabasePracticeExercise(
            id: UUID().uuidString,
            lessonId: lessonId,
            exerciseId: "L1E\(exerciseNumber)",
            exerciseType: "multiple_choice",
            titleEnglish: "Grammar Rules",
            titleTamil: "இலக்கண விதிகள்",
            instructionsEnglish: "Select the correct grammar rule.",
            instructionsTamil: "சரியான இலக்கண விதியைத் தேர்ந்தெடு.",
            difficultyLevel: 3,
            pointsValue: 20,
            timeLimitSeconds: 60,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        exercise.questionData = ExerciseQuestionData(
            question: question,
            questionTamil: questionTamil,
            options: options,
            optionsTamil: optionsTamil,
            optionsRomanization: nil, // Could add romanization for Tamil options
            optionsPronunciation: nil, // Could add pronunciation guides
            optionsAudioUrls: nil, // Could add audio URLs
            correctAnswer: correctIndex,
            correctAnswers: nil,
            explanation: "The rule for '\(grammarTopic.titleEnglish)' is: \(correctRule)",
            explanationTamil: "'\(grammarTopic.titleTamil)' க்கான விதி: \(grammarTopic.ruleTamil)",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )

        return exercise
    }

    private func createGrammarApplicationExercise(grammar: [TamilSupabaseGrammarTopic], vocabulary: [TamilSupabaseVocabulary], lessonId: String, exerciseNumber: Int) -> TamilSupabasePracticeExercise? {
        guard let grammarTopic = grammar.randomElement() else { return nil }

        let question = "Which sentence correctly applies the '\(grammarTopic.titleEnglish)' rule?"
        let questionTamil = "'\(grammarTopic.titleTamil)' விதியை சரியாகப் பயன்படுத்தும் வாக்கியம் எது?"

        // Create example sentences
        let correctSentence = "நான் தமிழ் பேசுகிறேன்" // I speak Tamil (present tense)
        let options = [
            correctSentence,
            "நான் தமிழ் பேசினேன்", // I spoke Tamil (past tense)
            "நான் தமிழ் பேசுவேன்", // I will speak Tamil (future tense)
            "தமிழ் நான் பேசுகிறேன்" // Tamil I speak (wrong word order)
        ]

        let optionsRomanization = [
            "Naan Tamil pesugireen",
            "Naan Tamil pesinen",
            "Naan Tamil pesuven",
            "Tamil naan pesugireen"
        ]

        let correctIndex = 0

        var exercise = TamilSupabasePracticeExercise(
            id: UUID().uuidString,
            lessonId: lessonId,
            exerciseId: "L1E\(exerciseNumber)",
            exerciseType: "multiple_choice",
            titleEnglish: "Apply Grammar Rule",
            titleTamil: "இலக்கண விதியைப் பயன்படுத்து",
            instructionsEnglish: "Choose the sentence that correctly follows the grammar rule.",
            instructionsTamil: "இலக்கண விதியை சரியாகப் பின்பற்றும் வாக்கியத்தைத் தேர்ந்தெடு.",
            difficultyLevel: 3,
            pointsValue: 20,
            timeLimitSeconds: 45,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        exercise.questionData = ExerciseQuestionData(
            question: question,
            questionTamil: questionTamil,
            options: options,
            optionsTamil: options, // Tamil options
            optionsRomanization: optionsRomanization, // Basic learners need romanization!
            optionsPronunciation: nil, // Could add pronunciation guides
            optionsAudioUrls: nil, // Could add audio URLs
            correctAnswer: correctIndex,
            correctAnswers: nil,
            explanation: "'\(correctSentence)' correctly follows the present tense pattern in Tamil.",
            explanationTamil: "'\(correctSentence)' தமிழில் நிகழ்கால வடிவத்தை சரியாகப் பின்பற்றுகிறது.",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )

        return exercise
    }

    private func createGrammarTrueFalseExercise(grammar: [TamilSupabaseGrammarTopic], lessonId: String, exerciseNumber: Int) -> TamilSupabasePracticeExercise? {
        guard let grammarTopic = grammar.randomElement() else { return nil }

        let question = "True or False: In Tamil, verbs typically come at the end of the sentence."
        let questionTamil = "உண்மை அல்லது பொய்: தமிழில், வினைச்சொற்கள் பொதுவாக வாக்கியத்தின் இறுதியில் வரும்."

        let options = ["True", "False"]
        let optionsTamil = ["உண்மை", "பொய்"]

        let correctIndex = 0 // True - Tamil is SOV (Subject-Object-Verb)

        var exercise = TamilSupabasePracticeExercise(
            id: UUID().uuidString,
            lessonId: lessonId,
            exerciseId: "L1E\(exerciseNumber)",
            exerciseType: "true_false",
            titleEnglish: "Grammar True/False",
            titleTamil: "இலக்கணம் உண்மை/பொய்",
            instructionsEnglish: "Determine if the grammar statement is true or false.",
            instructionsTamil: "இலக்கண கூற்று உண்மையா பொய்யா என்பதைத் தீர்மானிக்கவும்.",
            difficultyLevel: 2,
            pointsValue: 15,
            timeLimitSeconds: 30,
            createdAt: ISO8601DateFormatter().string(from: Date())
        )

        exercise.questionData = ExerciseQuestionData(
            question: question,
            questionTamil: questionTamil,
            options: options,
            optionsTamil: optionsTamil,
            optionsRomanization: nil, // Could add romanization for Tamil options
            optionsPronunciation: nil, // Could add pronunciation guides
            optionsAudioUrls: nil, // Could add audio URLs
            correctAnswer: correctIndex,
            correctAnswers: nil,
            explanation: "True. Tamil follows SOV (Subject-Object-Verb) word order, so verbs typically come at the end.",
            explanationTamil: "உண்மை. தமிழ் SOV (எழுவாய்-செயப்படுபொருள்-வினை) வரிசையைப் பின்பற்றுகிறது, எனவே வினைச்சொற்கள் பொதுவாக இறுதியில் வரும்.",
            audioQuestionUrl: nil,
            matchPairs: nil,
            fillBlanks: nil
        )

        return exercise
    }
}
