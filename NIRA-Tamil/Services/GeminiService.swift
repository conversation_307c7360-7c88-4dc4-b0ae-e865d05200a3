import Foundation
import Combine

// MARK: - Gemini AI Service for Dynamic Content Generation

@MainActor
class GeminiService: ObservableObject {
    static let shared = GeminiService()
    
    private let baseURL = APIConfig.geminiBaseURL
    private let apiKey: String
    private let useMockData: Bool
    
    @Published var isGenerating = false
    @Published var lastError: Error?
    @Published var lastGenerated: Date?
    
    private init() {
        // Use API key from configuration file
        self.apiKey = APIKeys.geminiAPIKey
        // Use mock data if API keys are not configured
        self.useMockData = APIConfig.useMockData
    }
    
    // MARK: - Lesson Generation
    
    func generateLesson(
        language: Language,
        difficulty: Difficulty,
        category: LessonCategory,
        culturalContext: String? = nil,
        userLevel: String = "beginner"
    ) async throws -> GeneratedLesson {
        await MainActor.run {
            isGenerating = true
        }
        
        defer {
            Task { @MainActor in
                isGenerating = false
            }
        }
        
        if useMockData {
            // Return mock data for development
            try await Task.sleep(nanoseconds: 1_000_000_000) // Simulate API delay
            return createMockLesson(language: language, difficulty: difficulty, category: category)
        }
        
        let prompt = createLessonPrompt(
            language: language,
            difficulty: difficulty,
            category: category,
            culturalContext: culturalContext,
            userLevel: userLevel
        )
        
        let response = try await makeGeminiRequest(prompt: prompt)
        return try parseLessonResponse(response)
    }
    
    // MARK: - Exercise Generation
    
    func generateExercises(
        for lesson: Lesson,
        count: Int = 5,
        types: [ExerciseType] = ExerciseType.allCases
    ) async throws -> [GeneratedExercise] {
        await MainActor.run {
            isGenerating = true
        }
        
        defer {
            Task { @MainActor in
                isGenerating = false
            }
        }
        
        let prompt = createExercisePrompt(for: lesson, count: count, types: types)
        
        do {
            let response = try await makeGeminiRequest(prompt: prompt)
            let exercises = try parseExerciseResponse(response)
            
            await MainActor.run {
                lastGenerated = Date()
            }
            
            return exercises
            
        } catch {
            await MainActor.run {
                lastError = error
            }
            
            // Return mock exercises for development
            return createMockExercises(for: lesson, count: count)
        }
    }
    
    // MARK: - Cultural Context Generation
    
    func generateCulturalContext(
        language: Language,
        scenario: String,
        difficulty: Difficulty
    ) async throws -> GeneratedCulturalContext {
        await MainActor.run {
            isGenerating = true
        }
        
        defer {
            Task { @MainActor in
                isGenerating = false
            }
        }
        
        if useMockData {
            try await Task.sleep(nanoseconds: 500_000_000)
            return createMockCulturalContext(language: language, scenario: scenario)
        }
        
        let prompt = createCulturalPrompt(
            language: language,
            scenario: scenario,
            difficulty: difficulty
        )
        
        let response = try await makeGeminiRequest(prompt: prompt)
        return try parseCulturalResponse(response)
    }
    
    // MARK: - Personalized Content
    
    func generatePersonalizedLesson(
        for user: User,
        weakAreas: [LessonCategory],
        interests: [String]
    ) async throws -> GeneratedLesson {
        await MainActor.run {
            isGenerating = true
        }
        
        defer {
            Task { @MainActor in
                isGenerating = false
            }
        }
        
        if useMockData {
            try await Task.sleep(nanoseconds: 1_000_000_000)
            return createMockPersonalizedLesson(for: user, weakAreas: weakAreas)
        }
        
        let prompt = createPersonalizedPrompt(
            user: user,
            weakAreas: weakAreas,
            interests: interests
        )
        
        let response = try await makeGeminiRequest(prompt: prompt)
        return try parseLessonResponse(response)
    }
    
    // MARK: - Pronunciation Feedback
    
    func generatePronunciationFeedback(
        targetText: String,
        userAudio: Data,
        language: Language
    ) async throws -> PronunciationFeedback {
        await MainActor.run {
            isGenerating = true
        }
        
        defer {
            Task { @MainActor in
                isGenerating = false
            }
        }
        
        // Always use mock data for pronunciation feedback for now
        try await Task.sleep(nanoseconds: 500_000_000)
        return PronunciationFeedback(
            accuracy: Double.random(in: 0.7...0.98),
            feedback: "Good pronunciation! Focus on the '\(targetText.prefix(1))' sound.",
            improvedAreas: ["Accent", "Rhythm"],
            suggestions: ["Practice the target sound", "Slow down slightly"]
        )
    }
    
    // MARK: - Mock Data Methods
    
    private func createMockLesson(language: Language, difficulty: Difficulty, category: LessonCategory) -> GeneratedLesson {
        return GeneratedLesson(
            title: "Mock \(language.displayName) \(category.displayName)",
            description: "A sample lesson for \(language.displayName) \(category.displayName) at \(difficulty.displayName) level",
            estimatedDuration: 15,
            vocabulary: [
                GeneratedVocabulary(
                    word: "hello",
                    translation: language == .french ? "bonjour" : "hola",
                    pronunciation: language == .french ? "bon-ZHOOR" : "OH-lah",
                    partOfSpeech: "interjection",
                    example: "Hello, nice to meet you!",
                    exampleTranslation: language == .french ? "Bonjour, ravi de vous rencontrer!" : "¡Hola, gusto en conocerte!"
                )
            ],
            grammarPoints: [
                GeneratedGrammarPoint(
                    rule: "Basic Greetings",
                    explanation: "Learn how to greet people politely",
                    examples: ["Hello", "Good morning", "How are you?"],
                    tips: "Use appropriate greetings for time of day"
                )
            ],
            culturalContext: GeneratedCulturalContextSimple(
                scenario: "Meeting someone new",
                setting: "Formal introduction",
                culturalNotes: "Different ways to greet people in different situations",
                doAndDonts: ["Do: Make eye contact", "Don't: Be too casual in formal settings"]
            ),
            dialogues: [
                GeneratedDialogue(
                    speaker: "Person 1",
                    text: "Hello, nice to meet you!",
                    translation: language == .french ? "Bonjour, ravi de vous rencontrer!" : "¡Hola, gusto en conocerte!",
                    culturalNote: "Standard polite greeting"
                )
            ],
            exercises: []
        )
    }
    
    private func createMockExercises(for lesson: Lesson, count: Int) -> [GeneratedExercise] {
        // Create simple mock exercises - this will be replaced by AI generation
        return []
    }
    
    private func createMockCulturalContext(language: Language, scenario: String) -> GeneratedCulturalContext {
        return GeneratedCulturalContext(
            scenario: scenario,
            setting: "A mock cultural scenario for \(language.displayName)",
            participants: ["Local speaker", "Language learner"],
            socialNorms: ["Be polite", "Make eye contact", "Smile"],
            etiquette: ["Say please and thank you", "Wait your turn to speak"],
            commonPhrases: [
                GeneratedPhrase(
                    phrase: "Hello",
                    translation: language == .french ? "Bonjour" : "Hola",
                    usage: "Use when greeting someone"
                )
            ],
            backgroundInfo: "Cultural context for \(language.displayName) speakers",
            tips: ["Be polite", "Make eye contact", "Smile"],
            doAndDonts: ["Do: Be respectful", "Don't: Be too casual"],
            regionalVariations: ["Different regions may have slight variations"],
            historicalContext: "Historical background for the language",
            modernUsage: "How this applies in modern context"
        )
    }
    
    private func createMockPersonalizedLesson(for user: User, weakAreas: [LessonCategory]) -> GeneratedLesson {
        let weakArea = weakAreas.first ?? .vocabulary
        return GeneratedLesson(
            title: "Personalized \(weakArea.displayName) Lesson for \(user.id)",
            description: "A customized lesson focusing on your weak areas",
            estimatedDuration: 20,
            vocabulary: [
                GeneratedVocabulary(
                    word: "practice",
                    translation: "practice",
                    pronunciation: "PRAK-tis",
                    partOfSpeech: "verb",
                    example: "Practice makes perfect",
                    exampleTranslation: "La pratique rend parfait"
                )
            ],
            grammarPoints: [
                GeneratedGrammarPoint(
                    rule: "Practice Makes Perfect",
                    explanation: "Regular practice is key to improvement",
                    examples: ["I practice every day", "Practice your pronunciation"],
                    tips: "Set aside regular practice time"
                )
            ],
            culturalContext: GeneratedCulturalContextSimple(
                scenario: "Study session",
                setting: "Learning environment",
                culturalNotes: "Different cultures have different approaches to learning",
                doAndDonts: ["Do: Be patient with yourself", "Don't: Give up easily"]
            ),
            dialogues: [
                GeneratedDialogue(
                    speaker: "Student",
                    text: "I want to improve my skills",
                    translation: "Je veux améliorer mes compétences",
                    culturalNote: "Expressing motivation to learn"
                )
            ],
            exercises: []
        )
    }
}

// MARK: - Private Methods

extension GeminiService {
    func createLessonPrompt(
        language: Language,
        difficulty: Difficulty,
        category: LessonCategory,
        culturalContext: String?,
        userLevel: String
    ) -> String {
        var prompt = """
        Create a comprehensive language lesson in JSON format for:
        - Language: \(language.displayName)
        - Difficulty: \(difficulty.displayName)
        - Category: \(category.displayName)
        - User Level: \(userLevel)
        """
        
        if let context = culturalContext {
            prompt += "\n- Cultural Context: \(context)"
        }
        
        prompt += """
        
        Return a JSON object with the following structure:
        {
            "title": "Lesson title",
            "description": "Detailed lesson description",
            "estimatedDuration": 15,
            "objectives": ["Learning objective 1", "Learning objective 2"],
            "vocabulary": [
                {
                    "word": "target word",
                    "translation": "translation",
                    "pronunciation": "phonetic pronunciation",
                    "exampleSentence": "example usage",
                    "culturalNote": "cultural context if applicable"
                }
            ],
            "grammarPoints": [
                {
                    "concept": "Grammar concept",
                    "explanation": "Clear explanation",
                    "examples": ["Example 1", "Example 2"]
                }
            ],
            "culturalInsights": [
                {
                    "topic": "Cultural topic",
                    "explanation": "Cultural explanation",
                    "doAndDonts": ["Do this", "Don't do this"]
                }
            ],
            "practiceScenarios": [
                {
                    "scenario": "Real-world scenario",
                    "dialogue": ["Speaker 1: Text", "Speaker 2: Text"],
                    "tips": ["Tip 1", "Tip 2"]
                }
            ]
        }
        
        Make the content engaging, culturally authentic, and appropriate for the specified difficulty level.
        """
        
        return prompt
    }
    
    func createExercisePrompt(for lesson: Lesson, count: Int, types: [ExerciseType]) -> String {
        let typeNames = types.map { $0.displayName }.joined(separator: ", ")
        
        return """
        Create \(count) diverse language exercises in JSON format for this lesson:
        - Title: \(lesson.title)
        - Language: \(lesson.language.displayName)
        - Difficulty: \(lesson.difficulty.displayName)
        - Category: \(lesson.category.displayName)
        - Exercise Types: \(typeNames)
        
        Return a JSON array with this structure:
        [
            {
                "type": "multiple_choice",
                "question": "Exercise question",
                "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
                "correctAnswer": 0,
                "explanation": "Why this answer is correct",
                "points": 10,
                "timeLimit": 30,
                "hints": ["Hint 1", "Hint 2"],
                "culturalNote": "Cultural context if applicable"
            }
        ]
        
        Mix different exercise types and ensure they're engaging and educational.
        """
    }
    
    func createCulturalPrompt(language: Language, scenario: String, difficulty: Difficulty) -> String {
        return """
        Create a detailed cultural context guide in JSON format for:
        - Language: \(language.displayName)
        - Scenario: \(scenario)
        - Difficulty: \(difficulty.displayName)
        
        Return a JSON object with:
        {
            "scenario": "\(scenario)",
            "setting": "Detailed setting description",
            "participants": ["Role 1", "Role 2"],
            "socialNorms": ["Norm 1", "Norm 2"],
            "etiquette": ["Etiquette rule 1", "Etiquette rule 2"],
            "commonPhrases": [
                {
                    "phrase": "Common phrase",
                    "translation": "Translation",
                    "usage": "When to use this phrase"
                }
            ],
            "backgroundInfo": "Cultural background information",
            "tips": ["Tip 1", "Tip 2"],
            "doAndDonts": ["Do: Action", "Don't: Action"],
            "regionalVariations": ["Variation 1", "Variation 2"],
            "historicalContext": "Historical background if relevant",
            "modernUsage": "How this applies in modern context"
        }
        
        Make it culturally authentic and respectful.
        """
    }
    
    func createPersonalizedPrompt(user: User, weakAreas: [LessonCategory], interests: [String]) -> String {
        let weakAreaNames = weakAreas.map { $0.displayName }.joined(separator: ", ")
        let interestList = interests.joined(separator: ", ")
        let languages = user.preferredLanguages.map { $0.displayName }.joined(separator: ", ")
        
        return """
        Create a personalized language lesson for this user profile:
        - Preferred Languages: \(languages)
        - Current Streak: \(user.currentStreak) days
        - Total Lessons Completed: \(user.totalLessonsCompleted)
        - Weak Areas: \(weakAreaNames)
        - Interests: \(interestList)
        
        Design a lesson that:
        1. Addresses the weak areas
        2. Incorporates the user's interests
        3. Matches their learning level
        4. Is engaging and motivating
        
        Use the same JSON structure as the lesson generation prompt.
        """
    }
    
    func makeGeminiRequest(prompt: String) async throws -> String {
        guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
            throw GeminiError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let requestBody = GeminiRequest(
            contents: [
                GeminiContent(
                    parts: [GeminiPart(text: prompt)]
                )
            ]
        )
        
        request.httpBody = try JSONEncoder().encode(requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw GeminiError.invalidResponse
        }
        
        let geminiResponse = try JSONDecoder().decode(GeminiResponse.self, from: data)
        
        guard let text = geminiResponse.candidates.first?.content.parts.first?.text else {
            throw GeminiError.noContent
        }
        
        return text
    }
    
    func parseLessonResponse(_ response: String) throws -> GeneratedLesson {
        // Extract JSON from response (Gemini sometimes includes markdown formatting)
        let jsonString = extractJSON(from: response)
        
        guard let data = jsonString.data(using: .utf8) else {
            throw GeminiError.invalidJSON
        }
        
        return try JSONDecoder().decode(GeneratedLesson.self, from: data)
    }
    
    func parseExerciseResponse(_ response: String) throws -> [GeneratedExercise] {
        let jsonString = extractJSON(from: response)
        
        guard let data = jsonString.data(using: .utf8) else {
            throw GeminiError.invalidJSON
        }
        
        return try JSONDecoder().decode([GeneratedExercise].self, from: data)
    }
    
    func parseCulturalResponse(_ response: String) throws -> GeneratedCulturalContext {
        let jsonString = extractJSON(from: response)
        
        guard let data = jsonString.data(using: .utf8) else {
            throw GeminiError.invalidJSON
        }
        
        return try JSONDecoder().decode(GeneratedCulturalContext.self, from: data)
    }
    
    func extractJSON(from response: String) -> String {
        // Remove markdown formatting if present
        let cleaned = response
            .replacingOccurrences(of: "```json", with: "")
            .replacingOccurrences(of: "```", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)
        
        return cleaned
    }
}

// MARK: - Data Models

struct GeminiRequest: Codable {
    let contents: [GeminiContent]
}

struct GeminiContent: Codable {
    let parts: [GeminiPart]
}

struct GeminiPart: Codable {
    let text: String
}

struct GeminiResponse: Codable {
    let candidates: [GeminiCandidate]
}

struct GeminiCandidate: Codable {
    let content: GeminiContent
}

struct GeneratedLesson: Codable {
    let title: String
    let description: String
    let estimatedDuration: Int
    let vocabulary: [GeneratedVocabulary]
    let grammarPoints: [GeneratedGrammarPoint]
    let culturalContext: GeneratedCulturalContextSimple
    let dialogues: [GeneratedDialogue]
    let exercises: [GeneratedExercise]
}

struct GeneratedVocabulary: Codable {
    let word: String
    let translation: String
    let pronunciation: String
    let partOfSpeech: String
    let example: String
    let exampleTranslation: String
}

struct GeneratedGrammarPoint: Codable {
    let rule: String
    let explanation: String
    let examples: [String]
    let tips: String
}

struct GeneratedCulturalContextSimple: Codable {
    let scenario: String
    let setting: String
    let culturalNotes: String
    let doAndDonts: [String]
}

struct GeneratedDialogue: Codable {
    let speaker: String
    let text: String
    let translation: String
    let culturalNote: String?
}

struct GeneratedCulturalInsight: Codable {
    let topic: String
    let explanation: String
    let doAndDonts: [String]
}

struct GeneratedScenario: Codable {
    let scenario: String
    let dialogue: [String]
    let tips: [String]
}

struct GeneratedExercise: Codable {
    let type: String
    let question: String
    let options: [String]?
    let correctAnswer: String?
    let explanation: String?
    let points: Int
    let hints: [String]?
    let pairs: [GeneratedExercisePair]?
    let targetPhrase: String?
    let phonetic: String?
    let audioHint: String?
    
    enum CodingKeys: String, CodingKey {
        case type, question, options, explanation, points, hints, pairs
        case correctAnswer = "correctAnswer"
        case targetPhrase, phonetic, audioHint
    }
    
    // Memberwise initializer for direct creation
    init(type: String, question: String, options: [String]? = nil, correctAnswer: String? = nil, explanation: String? = nil, points: Int = 100, hints: [String]? = nil, pairs: [GeneratedExercisePair]? = nil, targetPhrase: String? = nil, phonetic: String? = nil, audioHint: String? = nil) {
        self.type = type
        self.question = question
        self.options = options
        self.correctAnswer = correctAnswer
        self.explanation = explanation
        self.points = points
        self.hints = hints
        self.pairs = pairs
        self.targetPhrase = targetPhrase
        self.phonetic = phonetic
        self.audioHint = audioHint
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        type = try container.decode(String.self, forKey: .type)
        question = try container.decode(String.self, forKey: .question)
        options = try container.decodeIfPresent([String].self, forKey: .options)
        explanation = try container.decodeIfPresent(String.self, forKey: .explanation)
        points = try container.decode(Int.self, forKey: .points)
        hints = try container.decodeIfPresent([String].self, forKey: .hints)
        pairs = try container.decodeIfPresent([GeneratedExercisePair].self, forKey: .pairs)
        targetPhrase = try container.decodeIfPresent(String.self, forKey: .targetPhrase)
        phonetic = try container.decodeIfPresent(String.self, forKey: .phonetic)
        audioHint = try container.decodeIfPresent(String.self, forKey: .audioHint)
        
        // Handle correctAnswer which can be String, Int, or missing for some exercise types
        if let intAnswer = try? container.decode(Int.self, forKey: .correctAnswer) {
            correctAnswer = String(intAnswer)
        } else if let stringAnswer = try? container.decode(String.self, forKey: .correctAnswer) {
            correctAnswer = stringAnswer
        } else {
            // Some exercise types (matching, pronunciation) don't have correctAnswer
            correctAnswer = nil
        }
    }
}

struct GeneratedExercisePair: Codable {
    let left: String
    let right: String
}

struct GeneratedCulturalContext: Codable {
    let scenario: String
    let setting: String
    let participants: [String]
    let socialNorms: [String]
    let etiquette: [String]
    let commonPhrases: [GeneratedPhrase]
    let backgroundInfo: String
    let tips: [String]
    let doAndDonts: [String]
    let regionalVariations: [String]
    let historicalContext: String?
    let modernUsage: String?
}

struct GeneratedPhrase: Codable {
    let phrase: String
    let translation: String
    let usage: String
}

struct PronunciationFeedback: Codable {
    let accuracy: Double
    let feedback: String
    let improvedAreas: [String]
    let suggestions: [String]
}

enum GeminiError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case noContent
    case invalidJSON
    case rateLimitExceeded
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL: return "Invalid API URL"
        case .invalidResponse: return "Invalid response from Gemini API"
        case .noContent: return "No content generated"
        case .invalidJSON: return "Invalid JSON response"
        case .rateLimitExceeded: return "API rate limit exceeded"
        case .networkError(let error): return "Network error: \(error.localizedDescription)"
        }
    }
}

// MARK: - Supporting Types (remove duplicate GeneratedExercise)
