//
//  LessonWritingIntegrationService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import Foundation
import Combine

@MainActor
class LessonWritingIntegrationService: ObservableObject {
    static let shared = LessonWritingIntegrationService()
    
    @Published var lessonWritingContent: [String: [TamilWritingContent]] = [:]
    @Published var vocabularyWritingExercises: [VocabularyWritingExercise] = []
    @Published var isGeneratingContent = false
    
    private let scriptService = TamilScriptService.shared
    private let contentService = TamilContentService.shared
    private let supabase = SupabaseService.shared.client
    
    private init() {}
    
    // MARK: - Vocabulary Writing Exercise Models
    
    struct VocabularyWritingExercise: Identifiable, Codable {
        let id: UUID
        let lessonId: String
        let vocabularyWord: VocabularyItem
        let targetCharacters: [TamilCharacter]
        let writingSteps: [WritingStep]
        let difficulty: WritingDifficulty
        let estimatedTime: Int // minutes
        let culturalContext: String?
        let practiceHints: [String]
        let successCriteria: WritingSuccessCriteria
        
        struct WritingStep: Identifiable, Codable {
            let id: UUID
            let stepNumber: Int
            let stepType: StepType
            let content: String
            let targetCharacter: TamilCharacter?
            let instruction: String
            let expectedDuration: Int // seconds
            
            enum StepType: String, Codable, CaseIterable {
                case characterPractice = "character_practice"
                case wordBuilding = "word_building"
                case contextualWriting = "contextual_writing"
                case freeWriting = "free_writing"
            }
        }
        
        enum WritingDifficulty: String, Codable, CaseIterable {
            case beginner = "beginner"
            case intermediate = "intermediate"
            case advanced = "advanced"
            
            var displayName: String {
                switch self {
                case .beginner: return "Beginner"
                case .intermediate: return "Intermediate"
                case .advanced: return "Advanced"
                }
            }
        }
        
        struct WritingSuccessCriteria: Codable {
            let minimumAccuracy: Double
            let requiredCharacters: [String]
            let timeLimit: Int? // seconds
            let allowedAttempts: Int
        }
    }
    
    struct VocabularyItem: Codable {
        let tamil: String
        let romanization: String
        let english: String
        let audioUrl: String?
        let category: String
        let difficulty: Int
    }
    
    // MARK: - Content Generation
    
    /// Generate writing exercises for a specific lesson's vocabulary
    func generateWritingExercisesForLesson(_ lessonId: String) async {
        isGeneratingContent = true
        defer { isGeneratingContent = false }
        
        do {
            // Get lesson vocabulary
            let vocabulary = try await getLessonVocabulary(lessonId: lessonId)
            
            // Generate writing exercises for each vocabulary item
            var exercises: [VocabularyWritingExercise] = []
            
            for vocabItem in vocabulary {
                let exercise = await createWritingExercise(
                    for: vocabItem,
                    lessonId: lessonId
                )
                exercises.append(exercise)
            }
            
            vocabularyWritingExercises = exercises
            
            // Save to database
            await saveWritingExercises(exercises)
            
            print("✅ Generated \(exercises.count) writing exercises for lesson \(lessonId)")
            
        } catch {
            print("❌ Error generating writing exercises: \(error)")
        }
    }
    
    /// Create a comprehensive writing exercise for a vocabulary item
    private func createWritingExercise(
        for vocabulary: VocabularyItem,
        lessonId: String
    ) async -> VocabularyWritingExercise {
        
        // Analyze the Tamil word to identify characters
        let targetCharacters = await analyzeWordCharacters(vocabulary.tamil)
        
        // Determine difficulty based on character complexity
        let difficulty = determineDifficulty(for: targetCharacters)
        
        // Create writing steps
        let writingSteps = createWritingSteps(
            for: vocabulary,
            targetCharacters: targetCharacters,
            difficulty: difficulty
        )
        
        // Estimate time based on complexity
        let estimatedTime = calculateEstimatedTime(
            characters: targetCharacters,
            difficulty: difficulty
        )
        
        // Generate cultural context
        let culturalContext = generateCulturalContext(for: vocabulary)
        
        // Create practice hints
        let practiceHints = generatePracticeHints(
            for: vocabulary,
            characters: targetCharacters
        )
        
        // Define success criteria
        let successCriteria = VocabularyWritingExercise.WritingSuccessCriteria(
            minimumAccuracy: difficulty == .beginner ? 70.0 : difficulty == .intermediate ? 80.0 : 85.0,
            requiredCharacters: targetCharacters.map { $0.character },
            timeLimit: estimatedTime * 60, // Convert to seconds
            allowedAttempts: 3
        )
        
        return VocabularyWritingExercise(
            id: UUID(),
            lessonId: lessonId,
            vocabularyWord: vocabulary,
            targetCharacters: targetCharacters,
            writingSteps: writingSteps,
            difficulty: difficulty,
            estimatedTime: estimatedTime,
            culturalContext: culturalContext,
            practiceHints: practiceHints,
            successCriteria: successCriteria
        )
    }
    
    // MARK: - Character Analysis
    
    private func analyzeWordCharacters(_ tamilWord: String) async -> [TamilCharacter] {
        var characters: [TamilCharacter] = []
        
        for char in tamilWord {
            let charString = String(char)
            
            // Find character in database
            if let tamilChar = scriptService.allCharacters.first(where: { $0.character == charString }) {
                characters.append(tamilChar)
            } else {
                // Handle compound characters or create placeholder
                print("⚠️ Character not found in database: \(charString)")
            }
        }
        
        return characters
    }
    
    private func determineDifficulty(for characters: [TamilCharacter]) -> VocabularyWritingExercise.WritingDifficulty {
        let averageDifficulty = characters.map { $0.difficultyLevel }.reduce(0, +) / characters.count
        let maxComplexity = characters.map { $0.writingComplexity }.max()
        
        if averageDifficulty <= 2 && maxComplexity != .complex {
            return .beginner
        } else if averageDifficulty <= 3 {
            return .intermediate
        } else {
            return .advanced
        }
    }
    
    // MARK: - Writing Steps Creation
    
    private func createWritingSteps(
        for vocabulary: VocabularyItem,
        targetCharacters: [TamilCharacter],
        difficulty: VocabularyWritingExercise.WritingDifficulty
    ) -> [VocabularyWritingExercise.WritingStep] {
        
        var steps: [VocabularyWritingExercise.WritingStep] = []
        var stepNumber = 1
        
        // Step 1: Individual character practice
        for character in targetCharacters.prefix(3) { // Limit to first 3 unique characters
            steps.append(VocabularyWritingExercise.WritingStep(
                id: UUID(),
                stepNumber: stepNumber,
                stepType: .characterPractice,
                content: character.character,
                targetCharacter: character,
                instruction: "Practice writing the character '\(character.character)' (\(character.romanization))",
                expectedDuration: character.writingComplexity == .simple ? 30 : 60
            ))
            stepNumber += 1
        }
        
        // Step 2: Word building (if word has multiple characters)
        if targetCharacters.count > 1 {
            steps.append(VocabularyWritingExercise.WritingStep(
                id: UUID(),
                stepNumber: stepNumber,
                stepType: .wordBuilding,
                content: vocabulary.tamil,
                targetCharacter: nil,
                instruction: "Write the complete word '\(vocabulary.tamil)' (\(vocabulary.romanization))",
                expectedDuration: targetCharacters.count * 20
            ))
            stepNumber += 1
        }
        
        // Step 3: Contextual writing (for intermediate/advanced)
        if difficulty != .beginner {
            let contextSentence = generateContextSentence(for: vocabulary)
            steps.append(VocabularyWritingExercise.WritingStep(
                id: UUID(),
                stepNumber: stepNumber,
                stepType: .contextualWriting,
                content: contextSentence,
                targetCharacter: nil,
                instruction: "Write the word in context: \(contextSentence)",
                expectedDuration: 90
            ))
            stepNumber += 1
        }
        
        // Step 4: Free writing (for advanced)
        if difficulty == .advanced {
            steps.append(VocabularyWritingExercise.WritingStep(
                id: UUID(),
                stepNumber: stepNumber,
                stepType: .freeWriting,
                content: "",
                targetCharacter: nil,
                instruction: "Create your own sentence using '\(vocabulary.tamil)'",
                expectedDuration: 120
            ))
        }
        
        return steps
    }
    
    // MARK: - Content Generation Helpers
    
    private func generateContextSentence(for vocabulary: VocabularyItem) -> String {
        // Generate simple context sentences based on vocabulary category
        switch vocabulary.category.lowercased() {
        case "family":
            return "என் \(vocabulary.tamil) நல்லவர்" // My [family member] is good
        case "food":
            return "\(vocabulary.tamil) சுவையாக இருக்கிறது" // [food] is tasty
        case "greetings":
            return "\(vocabulary.tamil) சொல்கிறேன்" // I say [greeting]
        case "numbers":
            return "\(vocabulary.tamil) என்பது எண்" // [number] is a number
        default:
            return "\(vocabulary.tamil) முக்கியமானது" // [word] is important
        }
    }
    
    private func generateCulturalContext(for vocabulary: VocabularyItem) -> String {
        switch vocabulary.category.lowercased() {
        case "family":
            return "Family relationships are deeply respected in Tamil culture. The word '\(vocabulary.tamil)' carries cultural significance and shows the importance of family bonds."
        case "food":
            return "Tamil cuisine is rich and diverse. '\(vocabulary.tamil)' is an important part of Tamil food culture and traditions."
        case "greetings":
            return "Tamil greetings like '\(vocabulary.tamil)' reflect the culture's emphasis on respect and politeness in social interactions."
        default:
            return "The word '\(vocabulary.tamil)' is commonly used in everyday Tamil conversation and reflects important cultural values."
        }
    }
    
    private func generatePracticeHints(
        for vocabulary: VocabularyItem,
        characters: [TamilCharacter]
    ) -> [String] {
        var hints: [String] = []
        
        // Character-specific hints
        for character in characters.prefix(3) {
            if character.writingComplexity == .complex {
                hints.append("Take your time with '\(character.character)' - it has \(character.strokeCount) strokes")
            }
            
            if character.characterType == .combined {
                hints.append("'\(character.character)' is a combination character - practice the base form first")
            }
        }
        
        // Word-level hints
        if vocabulary.tamil.count > 3 {
            hints.append("Break down '\(vocabulary.tamil)' into smaller parts for easier practice")
        }
        
        hints.append("Listen to the pronunciation while writing to connect sound and script")
        hints.append("Practice writing '\(vocabulary.tamil)' slowly first, then increase speed")
        
        return hints
    }
    
    private func calculateEstimatedTime(
        characters: [TamilCharacter],
        difficulty: VocabularyWritingExercise.WritingDifficulty
    ) -> Int {
        let baseTime = characters.count * 2 // 2 minutes per character
        let complexityMultiplier = difficulty == .beginner ? 1.0 : difficulty == .intermediate ? 1.5 : 2.0
        
        return Int(Double(baseTime) * complexityMultiplier)
    }
    
    // MARK: - Database Operations
    
    private func getLessonVocabulary(lessonId: String) async throws -> [VocabularyItem] {
        // This would integrate with existing lesson content
        // For now, return sample vocabulary based on lesson
        
        switch lessonId {
        case "A1_BASIC_GREETINGS":
            return [
                VocabularyItem(tamil: "வணக்கம்", romanization: "vanakkam", english: "hello", audioUrl: nil, category: "greetings", difficulty: 1),
                VocabularyItem(tamil: "நன்றி", romanization: "nandri", english: "thank you", audioUrl: nil, category: "greetings", difficulty: 1),
                VocabularyItem(tamil: "மன்னிக்கவும்", romanization: "mannikkavum", english: "excuse me", audioUrl: nil, category: "greetings", difficulty: 2)
            ]
        case "A1_FAMILY_MEMBERS":
            return [
                VocabularyItem(tamil: "அம்மா", romanization: "amma", english: "mother", audioUrl: nil, category: "family", difficulty: 1),
                VocabularyItem(tamil: "அப்பா", romanization: "appa", english: "father", audioUrl: nil, category: "family", difficulty: 1),
                VocabularyItem(tamil: "அண்ணா", romanization: "anna", english: "elder brother", audioUrl: nil, category: "family", difficulty: 1)
            ]
        default:
            return []
        }
    }
    
    private func saveWritingExercises(_ exercises: [VocabularyWritingExercise]) async {
        // Save exercises to database
        for exercise in exercises {
            do {
                // Convert to database format and save
                let writingContent = TamilWritingContent(
                    id: exercise.id,
                    contentType: .word,
                    cefrLevel: .a1, // This would be determined by lesson level
                    writingMode: .guided,
                    practiceText: exercise.vocabularyWord.tamil,
                    practiceTextRomanized: exercise.vocabularyWord.romanization,
                    practiceTextEnglish: exercise.vocabularyWord.english,
                    targetCharacters: exercise.targetCharacters.map { $0.id },
                    lessonId: exercise.lessonId,
                    vocabularyId: nil,
                    difficultyScore: exercise.difficulty == .beginner ? 3 : exercise.difficulty == .intermediate ? 6 : 9,
                    estimatedTimeMinutes: exercise.estimatedTime,
                    successCriteria: SuccessCriteria(
                        accuracyThreshold: exercise.successCriteria.minimumAccuracy,
                        completionRequired: true,
                        timeLimit: exercise.successCriteria.timeLimit,
                        minimumStrokes: nil,
                        maximumAttempts: exercise.successCriteria.allowedAttempts
                    ),
                    hints: exercise.practiceHints.enumerated().map { index, hint in
                        WritingHint(
                            id: UUID(),
                            hintType: .formation,
                            content: hint,
                            triggerCondition: nil,
                            priority: index + 1
                        )
                    },
                    culturalContext: exercise.culturalContext,
                    learningObjectives: ["Practice writing \(exercise.vocabularyWord.tamil)", "Learn vocabulary through writing"],
                    prerequisiteContentIds: [],
                    isActive: true,
                    createdAt: Date(),
                    updatedAt: Date()
                )
                
                // Save to Supabase
                let _: TamilWritingContent = try await supabase
                    .from("tamil_writing_content")
                    .insert(writingContent)
                    .execute()
                    .value
                
            } catch {
                print("❌ Error saving writing exercise: \(error)")
            }
        }
    }
    
    // MARK: - Integration with Existing Lessons
    
    /// Get writing exercises for a specific lesson
    func getWritingExercisesForLesson(_ lessonId: String) -> [VocabularyWritingExercise] {
        return vocabularyWritingExercises.filter { $0.lessonId == lessonId }
    }
    
    /// Get writing content for lesson integration
    func getWritingContentForLesson(_ lessonId: String) async -> [TamilWritingContent] {
        do {
            let content: [TamilWritingContent] = try await supabase
                .from("tamil_writing_content")
                .select("*")
                .eq("lesson_id", value: lessonId)
                .eq("is_active", value: true)
                .order("difficulty_score")
                .execute()
                .value
            
            return content
        } catch {
            print("❌ Error fetching writing content for lesson: \(error)")
            return []
        }
    }
    
    /// Generate writing exercises for all A1 lessons
    func generateWritingContentForAllLessons() async {
        let a1Lessons = ["A1_BASIC_GREETINGS", "A1_FAMILY_MEMBERS", "A1_NUMBERS", "A1_COLORS", "A1_FOOD"]
        
        for lessonId in a1Lessons {
            await generateWritingExercisesForLesson(lessonId)
        }
    }
}
