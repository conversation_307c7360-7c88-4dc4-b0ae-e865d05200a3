import Foundation
// import Supabase // Disabled for compilation
import Combine

// MARK: - Simulation Models

struct SimulationPersona: Codable, Identifiable {
    let id: UUID
    let name: String
    let displayName: String
    let description: String
    let targetAudience: String
    let difficultyRange: String
    let colorTheme: String
    let iconName: String
    let learningObjectives: [String]?
    let typicalScenarios: [String]?
    let vocabularyFocus: [String]?
    let personalityTraits: [String: SupabaseAnyCodable]?
    let teachingStyle: String?
    let isActive: Bool?
    let sortOrder: Int?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id, name, description, isActive, createdAt, updatedAt
        case displayName = "display_name"
        case targetAudience = "target_audience"
        case difficultyRange = "difficulty_range"
        case colorTheme = "color_theme"
        case iconName = "icon_name"
        case learningObjectives = "learning_objectives"
        case typicalScenarios = "typical_scenarios"
        case vocabularyFocus = "vocabulary_focus"
        case personalityTraits = "personality_traits"
        case teachingStyle = "teaching_style"
        case sortOrder = "sort_order"
    }
}

struct Simulation: Codable, Identifiable {
    let id: UUID
    let personaId: UUID
    let languageId: UUID
    let title: String
    let description: String
    let difficultyLevel: String
    let estimatedDuration: Int
    let scenarioType: String
    let learningObjectives: [String]
    let vocabularyFocus: [SimulationVocabularyItem]?
    let conversationStarters: [String]?
    let successCriteria: [String: SupabaseAnyCodable]?
    let culturalNotes: String?
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date

    // Enhanced realistic simulation fields
    let realLifeContext: [String: SupabaseAnyCodable]?
    let realisticDialogue: [[String: SupabaseAnyCodable]]?
    let conversationBranches: [[String: SupabaseAnyCodable]]?
    let aiAgentIntegration: [String: SupabaseAnyCodable]?
    let audioIntegration: [String: SupabaseAnyCodable]?
    let culturalDeepDive: [String: SupabaseAnyCodable]?
    let prerequisiteLessons: [String]?
    let followUpLessons: [String]?
    let practiceSuggestions: [String]?
    let gamificationElements: [String: SupabaseAnyCodable]?
    let realLifeReadinessScore: Double?

    enum CodingKeys: String, CodingKey {
        case id, title, description, isActive, createdAt, updatedAt
        case personaId = "persona_id"
        case languageId = "language_id"
        case difficultyLevel = "difficulty_level"
        case estimatedDuration = "estimated_duration"
        case scenarioType = "scenario_type"
        case learningObjectives = "learning_objectives"
        case vocabularyFocus = "vocabulary_focus"
        case conversationStarters = "conversation_starters"
        case successCriteria = "success_criteria"
        case culturalNotes = "cultural_notes"

        // Enhanced fields
        case realLifeContext = "real_life_context"
        case realisticDialogue = "realistic_dialogue"
        case conversationBranches = "conversation_branches"
        case aiAgentIntegration = "ai_agent_integration"
        case audioIntegration = "audio_integration"
        case culturalDeepDive = "cultural_deep_dive"
        case prerequisiteLessons = "prerequisite_lessons"
        case followUpLessons = "follow_up_lessons"
        case practiceSuggestions = "practice_suggestions"
        case gamificationElements = "gamification_elements"
        case realLifeReadinessScore = "real_life_readiness_score"
    }
}

struct SimulationVocabularyItem: Codable {
    let word: String
    let translation: String
    let pronunciation: String?
    let context: String?
}

struct SimulationDialogue: Codable, Identifiable {
    let id: UUID
    let simulationId: UUID
    let sequenceOrder: Int
    let speakerRole: String
    let speakerName: String?
    let dialogueText: String
    let dialogueTranslation: String?
    let audioUrl: String?
    let responseOptions: [ResponseOption]
    let culturalContext: String?
    let vocabularyHighlights: [String]
    let grammarNotes: String?
    let difficultyLevel: String
    let isCriticalPath: Bool
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, createdAt
        case simulationId = "simulation_id"
        case sequenceOrder = "sequence_order"
        case speakerRole = "speaker_role"
        case speakerName = "speaker_name"
        case dialogueText = "dialogue_text"
        case dialogueTranslation = "dialogue_translation"
        case audioUrl = "audio_url"
        case responseOptions = "response_options"
        case culturalContext = "cultural_context"
        case vocabularyHighlights = "vocabulary_highlights"
        case grammarNotes = "grammar_notes"
        case difficultyLevel = "difficulty_level"
        case isCriticalPath = "is_critical_path"
    }
}

struct ResponseOption: Codable {
    let id: String
    let text: String
    let translation: String?
    let isCorrect: Bool
    let culturalAppropriate: Bool
    let nextSequence: Int?
    let feedback: String?
}

struct UserSimulationProgress: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let simulationId: UUID
    let personaId: UUID
    let completionStatus: CompletionStatus
    let performanceScore: Double
    let culturalCompetencyScore: Double
    let vocabularyScore: Double
    let grammarScore: Double
    let fluencyScore: Double
    let timeSpent: Int
    let attemptsCount: Int
    let dialogueChoices: [String: SupabaseAnyCodable]
    let feedbackReceived: String?
    let mistakesMade: [SimulationMistake]
    let strengthsIdentified: [SimulationStrength]
    let areasForImprovement: [ImprovementArea]
    let startedAt: Date?
    let completedAt: Date?
    let lastAccessed: Date
    let createdAt: Date
    let updatedAt: Date

    enum CompletionStatus: String, Codable, CaseIterable {
        case notStarted = "not_started"
        case inProgress = "in_progress"
        case completed = "completed"
        case mastered = "mastered"
    }

    enum CodingKeys: String, CodingKey {
        case id, createdAt, updatedAt
        case userId = "user_id"
        case simulationId = "simulation_id"
        case personaId = "persona_id"
        case completionStatus = "completion_status"
        case performanceScore = "performance_score"
        case culturalCompetencyScore = "cultural_competency_score"
        case vocabularyScore = "vocabulary_score"
        case grammarScore = "grammar_score"
        case fluencyScore = "fluency_score"
        case timeSpent = "time_spent"
        case attemptsCount = "attempts_count"
        case dialogueChoices = "dialogue_choices"
        case feedbackReceived = "feedback_received"
        case mistakesMade = "mistakes_made"
        case strengthsIdentified = "strengths_identified"
        case areasForImprovement = "areas_for_improvement"
        case startedAt = "started_at"
        case completedAt = "completed_at"
        case lastAccessed = "last_accessed"
    }
}

struct SimulationMistake: Codable {
    let type: String
    let description: String
    let suggestion: String
    let severity: String
}

struct SimulationStrength: Codable {
    let area: String
    let description: String
    let examples: [String]
}

struct ImprovementArea: Codable {
    let skill: String
    let description: String
    let recommendations: [String]
    let priority: String
}

// MARK: - Phase 4 & 5 Models

struct DialogueBranch: Codable, Identifiable {
    let id: UUID
    let dialogueId: UUID
    let branchCondition: [String: SupabaseAnyCodable]
    let branchWeight: Double
    let nextDialogueId: UUID?
    let branchType: String
    let culturalImpact: Double
    let difficultyModifier: Double
    let nextDialogue: SimulationDialogue?
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, createdAt
        case dialogueId = "dialogue_id"
        case branchCondition = "branch_condition"
        case branchWeight = "branch_weight"
        case nextDialogueId = "next_dialogue_id"
        case branchType = "branch_type"
        case culturalImpact = "cultural_impact"
        case difficultyModifier = "difficulty_modifier"
        case nextDialogue = "next_dialogue"
    }
}

struct AIVariation: Codable, Identifiable {
    let id: UUID
    let simulationId: UUID
    let variationType: String
    let originalContentId: UUID
    let aiGeneratedContent: [String: SupabaseAnyCodable]
    let variationPrompt: String
    let qualityScore: Double
    let usageCount: Int
    let userFeedback: [String: SupabaseAnyCodable]
    let isApproved: Bool
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, createdAt, updatedAt
        case simulationId = "simulation_id"
        case variationType = "variation_type"
        case originalContentId = "original_content_id"
        case aiGeneratedContent = "ai_generated_content"
        case variationPrompt = "variation_prompt"
        case qualityScore = "quality_score"
        case usageCount = "usage_count"
        case userFeedback = "user_feedback"
        case isApproved = "is_approved"
    }
}

struct SimulationShare: Codable, Identifiable {
    let id: UUID
    let simulationId: UUID
    let sharedByUserId: UUID
    let sharedWithUserId: UUID?
    let shareType: String
    let shareMessage: String?
    let isPublic: Bool
    let viewCount: Int
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, createdAt
        case simulationId = "simulation_id"
        case sharedByUserId = "shared_by_user_id"
        case sharedWithUserId = "shared_with_user_id"
        case shareType = "share_type"
        case shareMessage = "share_message"
        case isPublic = "is_public"
        case viewCount = "view_count"
    }
}

struct SimulationGroup: Codable, Identifiable {
    let id: UUID
    let name: String
    let description: String?
    let createdByUserId: UUID
    let isPublic: Bool
    let memberCount: Int
    let simulationCount: Int
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, name, description, createdAt, updatedAt
        case createdByUserId = "created_by_user_id"
        case isPublic = "is_public"
        case memberCount = "member_count"
        case simulationCount = "simulation_count"
    }
}

struct VoiceInteraction: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let simulationId: UUID
    let dialogueId: UUID
    let audioUrl: String?
    let transcribedText: String?
    let pronunciationScore: Double?
    let fluencyScore: Double?
    let accuracyScore: Double?
    let aiFeedback: String?
    let processingStatus: String
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, createdAt
        case userId = "user_id"
        case simulationId = "simulation_id"
        case dialogueId = "dialogue_id"
        case audioUrl = "audio_url"
        case transcribedText = "transcribed_text"
        case pronunciationScore = "pronunciation_score"
        case fluencyScore = "fluency_score"
        case accuracyScore = "accuracy_score"
        case aiFeedback = "ai_feedback"
        case processingStatus = "processing_status"
    }
}

struct LearningInsight: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let personaId: UUID
    let languageId: UUID
    let insightType: String
    let insightData: [String: SupabaseAnyCodable]
    let confidenceScore: Double
    let aiGenerated: Bool
    let isActionable: Bool
    let createdAt: Date
    let expiresAt: Date?

    enum CodingKeys: String, CodingKey {
        case id, createdAt
        case userId = "user_id"
        case personaId = "persona_id"
        case languageId = "language_id"
        case insightType = "insight_type"
        case insightData = "insight_data"
        case confidenceScore = "confidence_score"
        case aiGenerated = "ai_generated"
        case isActionable = "is_actionable"
        case expiresAt = "expires_at"
    }
}

struct PersonaPreference: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let personaId: UUID
    let preferenceScore: Double
    let completionRate: Double
    let averagePerformance: Double
    let totalTimeSpent: Int
    let simulationsCompleted: Int
    let simulationsMastered: Int
    let lastAccessed: Date?
    let recommendedNext: [String: SupabaseAnyCodable]
    let learningGoals: [String]
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, createdAt, updatedAt
        case userId = "user_id"
        case personaId = "persona_id"
        case preferenceScore = "preference_score"
        case completionRate = "completion_rate"
        case averagePerformance = "average_performance"
        case totalTimeSpent = "total_time_spent"
        case simulationsCompleted = "simulations_completed"
        case simulationsMastered = "simulations_mastered"
        case lastAccessed = "last_accessed"
        case recommendedNext = "recommended_next"
        case learningGoals = "learning_goals"
    }
}

// MARK: - Simulation Service

@MainActor
class SimulationService: ObservableObject {
    static let shared = SimulationService()

    private let supabaseClient: NIRASupabaseClient
    private let analyticsService: LearningAnalyticsService
    private let adaptiveCurriculumService: AdaptiveCurriculumService
    private let geminiService: GeminiService

    @Published var personas: [SimulationPersona] = []
    @Published var currentSimulations: [Simulation] = []
    @Published var userProgress: [UserSimulationProgress] = []
    @Published var personaPreferences: [PersonaPreference] = []
    @Published var isLoading = false
    @Published var errorMessage: String?

    private var cancellables = Set<AnyCancellable>()

    init(
        supabaseClient: NIRASupabaseClient? = nil,
        analyticsService: LearningAnalyticsService? = nil,
        adaptiveCurriculumService: AdaptiveCurriculumService? = nil,
        geminiService: GeminiService? = nil
    ) {
        self.supabaseClient = supabaseClient ?? NIRASupabaseClient.shared
        self.analyticsService = analyticsService ?? LearningAnalyticsService.shared
        self.adaptiveCurriculumService = adaptiveCurriculumService ?? AdaptiveCurriculumService()
        self.geminiService = geminiService ?? GeminiService.shared

        print("🔍 SimulationService: Initializing...")
        print("🔍 SimulationService: Supabase client connected: \(self.supabaseClient.isConnected)")
        loadPersonas()
    }

    // MARK: - Persona Management

    func loadPersonas() {
        Task {
            isLoading = true
            defer { isLoading = false }

            print("🔍 Loading simulation personas...")
            // Mock implementation for compilation
            let response: [SimulationPersona] = []
            // try await supabaseClient.client
            //     .from("simulation_personas")
            //     .select()
            //     .eq("is_active", value: true)
            //     .order("sort_order")
            //     .execute()
            //     .value

            personas = response
            print("✅ Loaded \(personas.count) simulation personas")

            // Track analytics
            analyticsService.trackInteraction(
                userId: UUID(), // Placeholder - would get current user ID
                interactionType: .lessonStart,
                contentType: .dialogue,
                contentId: "simulation_personas_loaded",
                isCorrect: nil,
                responseTime: nil,
                metadata: ["count": SupabaseAnyCodable(personas.count)]
            )
        }
    }

    func getPersona(by id: UUID) -> SimulationPersona? {
        return personas.first { $0.id == id }
    }

    func getPersonasByDifficulty(_ difficulty: String) -> [SimulationPersona] {
        return personas.filter { $0.difficultyRange.contains(difficulty) }
    }

    // MARK: - Simulation Management

    func loadSimulations(for personaId: UUID, languageId: UUID) {
        Task {
            isLoading = true
            defer { isLoading = false }

            // Mock implementation for compilation
            let response: [Simulation] = []
            // try await supabaseClient.client
            //     .from("simulations")
            //     .select()
            //     .eq("persona_id", value: personaId)
            //     .eq("language_id", value: languageId)
            //     .eq("is_active", value: true)
            //     .order("created_at", ascending: false)
            //     .execute()
            //     .value

            currentSimulations = response
            print("✅ Loaded \(response.count) simulations for persona \(personaId)")

            // Track analytics
            analyticsService.trackInteraction(
                userId: UUID(), // Placeholder - would get current user ID
                interactionType: .lessonStart,
                contentType: .dialogue,
                contentId: "simulations_loaded",
                isCorrect: nil,
                responseTime: nil,
                metadata: [
                    "persona_id": SupabaseAnyCodable(personaId.uuidString),
                    "language_id": SupabaseAnyCodable(languageId.uuidString),
                    "count": SupabaseAnyCodable(response.count)
                ]
            )
        }
    }

    func loadAllSimulations(for languageId: UUID) {
        Task {
            isLoading = true
            defer { isLoading = false }

            // Mock implementation for compilation
            let response: [Simulation] = []
            // try await supabaseClient.client
            //     .from("simulations")
            //     .select()
            //     .eq("language_id", value: languageId)
            //     .eq("is_active", value: true)
            //     .order("created_at", ascending: false)
            //     .execute()
            //     .value

            currentSimulations = response
            print("✅ Loaded \(response.count) total simulations for language \(languageId)")

            // Track analytics
            analyticsService.trackInteraction(
                userId: UUID(), // Placeholder - would get current user ID
                interactionType: .lessonStart,
                contentType: .dialogue,
                contentId: "all_simulations_loaded",
                isCorrect: nil,
                responseTime: nil,
                metadata: [
                    "language_id": SupabaseAnyCodable(languageId.uuidString),
                    "count": SupabaseAnyCodable(response.count)
                ]
            )
        }
    }



    func getSimulation(by id: UUID) -> Simulation? {
        return currentSimulations.first { $0.id == id }
    }

    func getSimulationsByDifficulty(_ difficulty: String) -> [Simulation] {
        return currentSimulations.filter { $0.difficultyLevel == difficulty }
    }

    func getRecommendedSimulations(for userId: UUID, languageId: UUID) async -> [Simulation] {
        // Get user's current skill level and preferences
        let userPreferences = await getUserPersonaPreferences(userId: userId)
        let _ = try? await adaptiveCurriculumService.getNextRecommendation(for: userId)

        // Combine persona preferences with adaptive curriculum recommendations
        var recommendedSimulations: [Simulation] = []

        for _ in userPreferences.prefix(2) { // Top 2 preferred personas
            // Mock implementation for compilation
            let simulations: [Simulation] = []
            // try await supabaseClient.client
            //     .from("simulations")
            //     .select()
            //     .eq("persona_id", value: preference.personaId)
            //     .eq("language_id", value: languageId)
            //     .eq("is_active", value: true)
            //     .order("created_at", ascending: false)
            //     .limit(5)
            //     .execute()
            //     .value

            recommendedSimulations.append(contentsOf: simulations)
        }

        // Track analytics
        analyticsService.trackInteraction(
            userId: userId,
            interactionType: .lessonStart,
            contentType: .dialogue,
            contentId: "simulation_recommendations_generated",
            isCorrect: nil,
            responseTime: nil,
            metadata: [
                "user_id": SupabaseAnyCodable(userId.uuidString),
                "language_id": SupabaseAnyCodable(languageId.uuidString),
                "count": SupabaseAnyCodable(recommendedSimulations.count)
            ]
        )

        return Array(recommendedSimulations.prefix(10)) // Limit to 10 recommendations
    }

    // MARK: - Dialogue Management

    func loadDialogues(for simulationId: UUID) async -> [SimulationDialogue] {
        // Mock implementation for compilation
        let response: [SimulationDialogue] = []
        // try await supabaseClient.client
        //     .from("simulation_dialogues")
        //     .select()
        //     .eq("simulation_id", value: simulationId)
        //     .order("sequence_order")
        //     .execute()
        //     .value

        return response
    }

    func getNextDialogue(currentSequence: Int, choice: String?, simulationId: UUID) async -> SimulationDialogue? {
        let dialogues = await loadDialogues(for: simulationId)

        // Simple linear progression for now
        // In a full implementation, this would handle branching based on choices
        let nextSequence = currentSequence + 1
        return dialogues.first { $0.sequenceOrder == nextSequence }
    }

    // MARK: - Progress Tracking

    func startSimulation(_ simulation: Simulation, userId: UUID) async {
        let progress = UserSimulationProgress(
            id: UUID(),
            userId: userId,
            simulationId: simulation.id,
            personaId: simulation.personaId,
            completionStatus: .inProgress,
            performanceScore: 0.0,
            culturalCompetencyScore: 0.0,
            vocabularyScore: 0.0,
            grammarScore: 0.0,
            fluencyScore: 0.0,
            timeSpent: 0,
            attemptsCount: 1,
            dialogueChoices: [:],
            feedbackReceived: nil,
            mistakesMade: [],
            strengthsIdentified: [],
            areasForImprovement: [],
            startedAt: Date(),
            completedAt: nil,
            lastAccessed: Date(),
            createdAt: Date(),
            updatedAt: Date()
        )

        // Mock implementation for compilation
        // try await supabaseClient.client
        //     .from("user_simulation_progress")
        //     .insert(progress)
        //     .execute()

        // Update local state
        if let index = userProgress.firstIndex(where: { $0.simulationId == simulation.id && $0.userId == userId }) {
            userProgress[index] = progress
        } else {
            userProgress.append(progress)
        }

        // Track analytics
        analyticsService.trackInteraction(
            userId: userId,
            interactionType: .lessonStart,
            contentType: .dialogue,
            contentId: "simulation_started",
            isCorrect: nil,
            responseTime: nil,
            metadata: [
                "simulation_id": SupabaseAnyCodable(simulation.id.uuidString),
                "persona_id": SupabaseAnyCodable(simulation.personaId.uuidString),
                "difficulty": SupabaseAnyCodable(simulation.difficultyLevel)
            ]
        )
    }

    func updateProgress(
        simulationId: UUID,
        userId: UUID,
        dialogueChoice: String,
        timeSpent: Int,
        scores: [String: Double]
    ) async {
        guard let progressIndex = userProgress.firstIndex(where: {
            $0.simulationId == simulationId && $0.userId == userId
        }) else { return }

        var progress = userProgress[progressIndex]

        // Update progress data
        progress = UserSimulationProgress(
            id: progress.id,
            userId: progress.userId,
            simulationId: progress.simulationId,
            personaId: progress.personaId,
            completionStatus: progress.completionStatus,
            performanceScore: scores["performance"] ?? progress.performanceScore,
            culturalCompetencyScore: scores["cultural"] ?? progress.culturalCompetencyScore,
            vocabularyScore: scores["vocabulary"] ?? progress.vocabularyScore,
            grammarScore: scores["grammar"] ?? progress.grammarScore,
            fluencyScore: scores["fluency"] ?? progress.fluencyScore,
            timeSpent: progress.timeSpent + timeSpent,
            attemptsCount: progress.attemptsCount,
            dialogueChoices: progress.dialogueChoices,
            feedbackReceived: progress.feedbackReceived,
            mistakesMade: progress.mistakesMade,
            strengthsIdentified: progress.strengthsIdentified,
            areasForImprovement: progress.areasForImprovement,
            startedAt: progress.startedAt,
            completedAt: progress.completedAt,
            lastAccessed: Date(),
            createdAt: progress.createdAt,
            updatedAt: Date()
        )

        // Mock implementation for compilation
        // try await supabaseClient.client
        //     .from("user_simulation_progress")
        //     .update(progress)
        //     .eq("id", value: progress.id)
        //     .execute()

        userProgress[progressIndex] = progress
    }

    func completeSimulation(simulationId: UUID, userId: UUID, finalScores: [String: Double]) async {
        guard let progressIndex = userProgress.firstIndex(where: {
            $0.simulationId == simulationId && $0.userId == userId
        }) else { return }

        var progress = userProgress[progressIndex]
        let overallScore = finalScores.values.reduce(0, +) / Double(finalScores.count)
        let completionStatus: UserSimulationProgress.CompletionStatus = overallScore >= 85 ? .mastered : .completed

        progress = UserSimulationProgress(
            id: progress.id,
            userId: progress.userId,
            simulationId: progress.simulationId,
            personaId: progress.personaId,
            completionStatus: completionStatus,
            performanceScore: finalScores["performance"] ?? progress.performanceScore,
            culturalCompetencyScore: finalScores["cultural"] ?? progress.culturalCompetencyScore,
            vocabularyScore: finalScores["vocabulary"] ?? progress.vocabularyScore,
            grammarScore: finalScores["grammar"] ?? progress.grammarScore,
            fluencyScore: finalScores["fluency"] ?? progress.fluencyScore,
            timeSpent: progress.timeSpent,
            attemptsCount: progress.attemptsCount,
            dialogueChoices: progress.dialogueChoices,
            feedbackReceived: progress.feedbackReceived,
            mistakesMade: progress.mistakesMade,
            strengthsIdentified: progress.strengthsIdentified,
            areasForImprovement: progress.areasForImprovement,
            startedAt: progress.startedAt,
            completedAt: Date(),
            lastAccessed: Date(),
            createdAt: progress.createdAt,
            updatedAt: Date()
        )

        // Mock implementation for compilation
        // try await supabaseClient.client
        //     .from("user_simulation_progress")
        //     .update(progress)
        //     .eq("id", value: progress.id)
        //     .execute()

        userProgress[progressIndex] = progress

        // Update persona preferences
        await updatePersonaPreferences(userId: userId, personaId: progress.personaId, performance: overallScore)

        // Track analytics
        analyticsService.trackInteraction(
            userId: userId,
            interactionType: .lessonComplete,
            contentType: .dialogue,
            contentId: "simulation_completed",
            isCorrect: overallScore >= 70,
            responseTime: progress.timeSpent,
            metadata: [
                "simulation_id": SupabaseAnyCodable(simulationId.uuidString),
                "completion_status": SupabaseAnyCodable(completionStatus.rawValue),
                "overall_score": SupabaseAnyCodable(overallScore),
                "time_spent": SupabaseAnyCodable(progress.timeSpent)
            ]
        )
    }

    // MARK: - Persona Preferences

    func getUserPersonaPreferences(userId: UUID) async -> [PersonaPreference] {
        // Mock implementation for compilation
        let response: [PersonaPreference] = []
        // try await supabaseClient.client
        //     .from("persona_preferences")
        //     .select()
        //     .eq("user_id", value: userId)
        //     .order("preference_score", ascending: false)
        //     .execute()
        //     .value

        return response
    }

    private func updatePersonaPreferences(userId: UUID, personaId: UUID, performance: Double) async {
        // Get existing preference or create new one
        // Mock implementation for compilation
        let existingPreferences: [PersonaPreference] = []
        // try await supabaseClient.client
        //     .from("persona_preferences")
        //     .select()
        //     .eq("user_id", value: userId)
        //     .eq("persona_id", value: personaId)
        //     .execute()
        //     .value

        if let existing = existingPreferences.first {
            // Update existing preference
            let newAveragePerformance = (existing.averagePerformance * Double(existing.simulationsCompleted) + performance) / Double(existing.simulationsCompleted + 1)
            let newPreferenceScore = calculatePreferenceScore(
                completionRate: existing.completionRate,
                averagePerformance: newAveragePerformance,
                totalTime: existing.totalTimeSpent
            )

            let _ = PersonaPreference(
                id: existing.id,
                userId: existing.userId,
                personaId: existing.personaId,
                preferenceScore: newPreferenceScore,
                completionRate: existing.completionRate,
                averagePerformance: newAveragePerformance,
                totalTimeSpent: existing.totalTimeSpent,
                simulationsCompleted: existing.simulationsCompleted + 1,
                simulationsMastered: existing.simulationsMastered + (performance >= 85 ? 1 : 0),
                lastAccessed: Date(),
                recommendedNext: existing.recommendedNext,
                learningGoals: existing.learningGoals,
                createdAt: existing.createdAt,
                updatedAt: Date()
            )

            // Mock implementation for compilation
            // try await supabaseClient.client
            //     .from("persona_preferences")
            //     .update(updatedPreference)
            //     .eq("id", value: existing.id)
            //     .execute()

        } else {
            // Create new preference
            let _ = PersonaPreference(
                id: UUID(),
                userId: userId,
                personaId: personaId,
                preferenceScore: performance,
                completionRate: 100.0, // First simulation completed
                averagePerformance: performance,
                totalTimeSpent: 0,
                simulationsCompleted: 1,
                simulationsMastered: performance >= 85 ? 1 : 0,
                lastAccessed: Date(),
                recommendedNext: [:],
                learningGoals: [],
                createdAt: Date(),
                updatedAt: Date()
            )

            // Mock implementation for compilation
            // try await supabaseClient.client
            //     .from("persona_preferences")
            //     .insert(newPreference)
            //     .execute()
        }
    }

    private func calculatePreferenceScore(completionRate: Double, averagePerformance: Double, totalTime: Int) -> Double {
        // Weighted calculation: 40% completion rate, 50% performance, 10% engagement (time)
        let engagementScore = min(Double(totalTime) / 3600.0, 100.0) // Max 1 hour = 100 points
        return (completionRate * 0.4) + (averagePerformance * 0.5) + (engagementScore * 0.1)
    }

    // MARK: - AI Integration & Advanced Features

    func generatePersonalizedFeedback(for progress: UserSimulationProgress, simulation: Simulation) async -> String {
        let prompt = """
        Generate personalized feedback for a language learning simulation.

        Simulation: \(simulation.title)
        Persona: \(simulation.personaId)
        Difficulty: \(simulation.difficultyLevel)

        Performance Scores:
        - Overall: \(progress.performanceScore)%
        - Cultural Competency: \(progress.culturalCompetencyScore)%
        - Vocabulary: \(progress.vocabularyScore)%
        - Grammar: \(progress.grammarScore)%
        - Fluency: \(progress.fluencyScore)%

        Time Spent: \(progress.timeSpent) seconds
        Attempts: \(progress.attemptsCount)

        Provide encouraging, specific feedback with actionable improvement suggestions.
        """

        do {
            let feedback = try await geminiService.makeGeminiRequest(prompt: prompt)
            return feedback
        } catch {
            return "Great job completing this simulation! Keep practicing to improve your skills."
        }
    }

    // MARK: - Advanced Branching Dialogues

    func getAdvancedNextDialogue(
        currentSequence: Int,
        userPerformance: Double,
        culturalScore: Double,
        simulationId: UUID
    ) async -> SimulationDialogue? {
        // Get dialogue branches based on performance and cultural competency
        // Mock implementation for compilation
        let branches: [DialogueBranch] = []
        // TODO: Implement Supabase query when ready
        // try await supabaseClient.client
        //     .from("simulation_dialogue_branches")
        //     .select("""
        //         *,
        //         next_dialogue:simulation_dialogues(*)
        //     """)
        //     .eq("dialogue_id", value: currentSequence)
        //     .execute()
        //     .value

        // AI-powered branch selection based on user performance
        let selectedBranch = selectOptimalBranch(
            branches: branches,
            userPerformance: userPerformance,
            culturalScore: culturalScore
        )

        if let nextDialogue = selectedBranch?.nextDialogue {
            return nextDialogue
        } else {
            return await getNextDialogue(currentSequence: currentSequence, choice: nil, simulationId: simulationId)
        }
    }

    private func selectOptimalBranch(
        branches: [DialogueBranch],
        userPerformance: Double,
        culturalScore: Double
    ) -> DialogueBranch? {
        // AI-weighted selection based on user performance and learning objectives
        let weightedBranches = branches.map { branch in
            var weight = branch.branchWeight

            // Adjust weight based on user performance
            if userPerformance < 60 && branch.branchType == "remedial" {
                weight *= 1.5
            } else if userPerformance > 80 && branch.branchType == "advanced" {
                weight *= 1.3
            }

            // Adjust for cultural competency
            if culturalScore < 70 && branch.culturalImpact > 0.5 {
                weight *= 1.2
            }

            return (branch, weight)
        }

        // Select branch with highest adjusted weight
        return weightedBranches.max(by: { $0.1 < $1.1 })?.0
    }

    // MARK: - AI-Generated Variations

    func generateSimulationVariation(
        for simulation: Simulation,
        variationType: String,
        userLevel: String
    ) async -> AIVariation? {
        let prompt = """
        Generate a \(variationType) variation for this language learning simulation:

        Original Simulation: \(simulation.title)
        Description: \(simulation.description)
        Difficulty: \(simulation.difficultyLevel)
        User Level: \(userLevel)

        Create a variation that:
        1. Maintains the core learning objectives
        2. Adjusts difficulty appropriately
        3. Adds cultural authenticity
        4. Provides engaging content

        Return as JSON with: title, description, content, difficulty_adjustment
        """

        do {
            let response = try await geminiService.makeGeminiRequest(prompt: prompt)

            // Parse AI response and create variation
            let variation = AIVariation(
                id: UUID(),
                simulationId: simulation.id,
                variationType: variationType,
                originalContentId: simulation.id,
                aiGeneratedContent: ["response": SupabaseAnyCodable(response)],
                variationPrompt: prompt,
                qualityScore: 0.0,
                usageCount: 0,
                userFeedback: [:],
                isApproved: false,
                createdAt: Date(),
                updatedAt: Date()
            )

            // Store variation in database
            // Mock implementation for compilation
            // try await supabaseClient.client
            //     .from("simulation_ai_variations")
            //     .insert(variation)
            //     .execute()

            return variation

        } catch {
            print("Error generating AI variation: \(error)")
            return nil
        }
    }

    func generateNextSimulationRecommendations(for userId: UUID, personaId: UUID) async -> [String] {
        do {
            let userProgress = await getUserProgress(userId: userId, personaId: personaId)
            let completedSimulations = userProgress.filter { $0.completionStatus == .completed || $0.completionStatus == .mastered }

            let prompt = """
            Based on completed simulations, recommend the next 3 simulations for this user.

            Completed simulations: \(completedSimulations.count)
            Average performance: \(completedSimulations.map { $0.performanceScore }.reduce(0, +) / Double(max(completedSimulations.count, 1)))

            Focus on progressive difficulty and skill building.
            """

            let recommendations = try await geminiService.makeGeminiRequest(prompt: prompt)
            return recommendations.components(separatedBy: "\n").filter { !$0.isEmpty }

        } catch {
            return ["Continue with beginner-level simulations", "Focus on vocabulary building", "Practice cultural scenarios"]
        }
    }

    // MARK: - Helper Methods

    func getUserProgress(userId: UUID, personaId: UUID? = nil) async -> [UserSimulationProgress] {
        // Mock implementation for compilation
        // var query = supabaseClient.client
        //     .from("user_simulation_progress")
        //     .select()
        //     .eq("user_id", value: userId)

        // if let personaId = personaId {
        //     query = query.eq("persona_id", value: personaId)
        // }

        // let response: [UserSimulationProgress] = try await query
        //     .order("last_accessed", ascending: false)
        //     .execute()
        //     .value

        // Mock implementation for compilation
        return []
    }

    func getSimulationStats(userId: UUID) async -> [String: Any] {
        let progress = await getUserProgress(userId: userId)

        let totalSimulations = progress.count
        let completedSimulations = progress.filter { $0.completionStatus == .completed || $0.completionStatus == .mastered }.count
        let masteredSimulations = progress.filter { $0.completionStatus == .mastered }.count
        let totalTimeSpent = progress.reduce(0) { $0 + $1.timeSpent }
        let averageScore = progress.isEmpty ? 0 : progress.map { $0.performanceScore }.reduce(0, +) / Double(progress.count)

        return [
            "total_simulations": totalSimulations,
            "completed_simulations": completedSimulations,
            "mastered_simulations": masteredSimulations,
            "completion_rate": totalSimulations > 0 ? Double(completedSimulations) / Double(totalSimulations) * 100 : 0,
            "mastery_rate": completedSimulations > 0 ? Double(masteredSimulations) / Double(completedSimulations) * 100 : 0,
            "total_time_spent": totalTimeSpent,
            "average_score": averageScore
        ]
    }

    // MARK: - Social Features

    func shareSimulation(_ simulation: Simulation, with userId: UUID?, message: String?, isPublic: Bool = false) async -> Bool {
        guard let currentUserId = getCurrentUserId() else { return false }

        let share = SimulationShare(
            id: UUID(),
            simulationId: simulation.id,
            sharedByUserId: currentUserId,
            sharedWithUserId: userId,
            shareType: isPublic ? "public" : "direct",
            shareMessage: message,
            isPublic: isPublic,
            viewCount: 0,
            createdAt: Date()
        )

        // Mock implementation for compilation
        // try await supabaseClient.client
        //     .from("simulation_shares")
        //     .insert(share)
        //     .execute()

        // Track analytics
        analyticsService.trackInteraction(
            userId: currentUserId,
            interactionType: .exerciseAttempt,
            contentType: .dialogue,
            contentId: "simulation_shared",
            isCorrect: nil,
            responseTime: nil,
            metadata: [
                "simulation_id": SupabaseAnyCodable(simulation.id.uuidString),
                "share_type": SupabaseAnyCodable(share.shareType),
                "is_public": SupabaseAnyCodable(isPublic)
            ]
        )

        return true
    }

    func createSimulationGroup(name: String, description: String?, isPublic: Bool = false) async -> SimulationGroup? {
        guard let currentUserId = getCurrentUserId() else { return nil }

        let group = SimulationGroup(
            id: UUID(),
            name: name,
            description: description,
            createdByUserId: currentUserId,
            isPublic: isPublic,
            memberCount: 1,
            simulationCount: 0,
            createdAt: Date(),
            updatedAt: Date()
        )

        // Mock implementation for compilation
        // try await supabaseClient.client
        //     .from("simulation_groups")
        //     .insert(group)
        //     .execute()

        // Add creator as first member
        // try await supabaseClient.client
        //     .from("simulation_group_members")
        //     .insert([
        //         "group_id": group.id.uuidString,
        //         "user_id": currentUserId.uuidString,
        //         "role": "admin"
        //     ])
        //     .execute()

        return group
    }

    func getPublicSimulationGroups() async -> [SimulationGroup] {
        // Mock implementation for compilation
        let groups: [SimulationGroup] = []
        // try await supabaseClient.client
        //     .from("simulation_groups")
        //     .select()
        //     .eq("is_public", value: true)
        //     .order("member_count", ascending: false)
        //     .limit(20)
        //     .execute()
        //     .value

        return groups
    }

    // MARK: - Voice Interaction Features

    func processVoiceInteraction(
        audioData: Data,
        simulationId: UUID,
        dialogueId: UUID
    ) async -> VoiceInteraction? {
        guard let currentUserId = getCurrentUserId() else { return nil }

        do {
            // Upload audio file
            let audioUrl = try await uploadAudioFile(audioData: audioData)

            // Create voice interaction record
            let interaction = VoiceInteraction(
                id: UUID(),
                userId: currentUserId,
                simulationId: simulationId,
                dialogueId: dialogueId,
                audioUrl: audioUrl,
                transcribedText: nil,
                pronunciationScore: nil,
                fluencyScore: nil,
                accuracyScore: nil,
                aiFeedback: nil,
                processingStatus: "pending",
                createdAt: Date()
            )

            // Mock implementation for compilation
            // try await supabaseClient.client
            //     .from("simulation_voice_interactions")
            //     .insert(interaction)
            //     .execute()

            // Process audio asynchronously
            Task {
                await processAudioAnalysis(interaction: interaction)
            }

            return interaction

        } catch {
            print("Error processing voice interaction: \(error)")
            return nil
        }
    }

    private func uploadAudioFile(audioData: Data) async throws -> String {
        // Implementation would upload to Supabase Storage
        // For now, return a placeholder URL
        return "https://storage.supabase.co/audio/\(UUID().uuidString).m4a"
    }

    private func processAudioAnalysis(interaction: VoiceInteraction) async {
        // This would integrate with speech recognition and pronunciation analysis APIs
        // For now, simulate the process

        do {
            // Simulate transcription and scoring
            let transcription = "Simulated transcription"
            let pronunciationScore = Double.random(in: 60...95)
            let fluencyScore = Double.random(in: 55...90)
            let accuracyScore = Double.random(in: 65...95)

            // Generate AI feedback
            let feedback = await generateVoiceFeedback(
                transcription: transcription,
                scores: [
                    "pronunciation": pronunciationScore,
                    "fluency": fluencyScore,
                    "accuracy": accuracyScore
                ]
            )

            // Update interaction with results
            struct VoiceInteractionUpdate: Codable {
                let transcribedText: String
                let pronunciationScore: Double
                let fluencyScore: Double
                let accuracyScore: Double
                let aiFeedback: String
                let processingStatus: String

                enum CodingKeys: String, CodingKey {
                    case transcribedText = "transcribed_text"
                    case pronunciationScore = "pronunciation_score"
                    case fluencyScore = "fluency_score"
                    case accuracyScore = "accuracy_score"
                    case aiFeedback = "ai_feedback"
                    case processingStatus = "processing_status"
                }
            }

            let _ = VoiceInteractionUpdate(
                transcribedText: transcription,
                pronunciationScore: pronunciationScore,
                fluencyScore: fluencyScore,
                accuracyScore: accuracyScore,
                aiFeedback: feedback,
                processingStatus: "completed"
            )

            // Mock implementation for compilation
            try await Task.sleep(nanoseconds: 100_000_000) // 0.1 second delay

        } catch {
            print("Error processing audio analysis: \(error)")
        }
    }

    private func generateVoiceFeedback(transcription: String, scores: [String: Double]) async -> String {
        let prompt = """
        Provide encouraging feedback for a language learner's voice interaction:

        Transcription: \(transcription)
        Pronunciation Score: \(scores["pronunciation"] ?? 0)%
        Fluency Score: \(scores["fluency"] ?? 0)%
        Accuracy Score: \(scores["accuracy"] ?? 0)%

        Give specific, actionable feedback to help improve pronunciation and fluency.
        """

        do {
            return try await geminiService.makeGeminiRequest(prompt: prompt)
        } catch {
            return "Good effort! Keep practicing to improve your pronunciation and fluency."
        }
    }

    // MARK: - Advanced Analytics & Insights

    func generateLearningInsights(for userId: UUID, languageId: UUID) async -> [LearningInsight] {
        // Get user's simulation progress and patterns
        let progress = await getUserProgress(userId: userId)
        let personaPreferences = await getUserPersonaPreferences(userId: userId)

        var insights: [LearningInsight] = []

        // Generate performance insights
        if let performanceInsight = await generatePerformanceInsight(
            userId: userId,
            languageId: languageId,
            progress: progress
        ) {
            insights.append(performanceInsight)
        }

        // Generate learning pattern insights
        if let patternInsight = await generateLearningPatternInsight(
            userId: userId,
            languageId: languageId,
            preferences: personaPreferences
        ) {
            insights.append(patternInsight)
        }

        // Generate recommendation insights
        if let recommendationInsight = await generateRecommendationInsight(
            userId: userId,
            languageId: languageId,
            progress: progress
        ) {
            insights.append(recommendationInsight)
        }

        // Store insights in database
        // Mock implementation for compilation
        for _ in insights {
            // try await supabaseClient.client
            //     .from("simulation_learning_insights")
            //     .insert(insight)
            //     .execute()
        }

        return insights
    }

    private func generatePerformanceInsight(
        userId: UUID,
        languageId: UUID,
        progress: [UserSimulationProgress]
    ) async -> LearningInsight? {
        let averageScore = progress.isEmpty ? 0 : progress.map { $0.performanceScore }.reduce(0, +) / Double(progress.count)

        let insightData: [String: SupabaseAnyCodable] = [
            "average_score": SupabaseAnyCodable(averageScore),
            "total_simulations": SupabaseAnyCodable(progress.count),
            "trend": SupabaseAnyCodable(averageScore > 75 ? "improving" : "needs_focus")
        ]

        return LearningInsight(
            id: UUID(),
            userId: userId,
            personaId: UUID(), // Would be determined based on most used persona
            languageId: languageId,
            insightType: "performance",
            insightData: insightData,
            confidenceScore: 85.0,
            aiGenerated: true,
            isActionable: true,
            createdAt: Date(),
            expiresAt: Calendar.current.date(byAdding: .day, value: 30, to: Date())
        )
    }

    private func generateLearningPatternInsight(
        userId: UUID,
        languageId: UUID,
        preferences: [PersonaPreference]
    ) async -> LearningInsight? {
        guard let topPersona = preferences.first else { return nil }

        let insightData: [String: SupabaseAnyCodable] = [
            "preferred_persona": SupabaseAnyCodable(topPersona.personaId.uuidString),
            "completion_rate": SupabaseAnyCodable(topPersona.completionRate),
            "learning_style": SupabaseAnyCodable("visual_interactive")
        ]

        return LearningInsight(
            id: UUID(),
            userId: userId,
            personaId: topPersona.personaId,
            languageId: languageId,
            insightType: "pattern",
            insightData: insightData,
            confidenceScore: 78.0,
            aiGenerated: true,
            isActionable: false,
            createdAt: Date(),
            expiresAt: Calendar.current.date(byAdding: .day, value: 14, to: Date())
        )
    }

    private func generateRecommendationInsight(
        userId: UUID,
        languageId: UUID,
        progress: [UserSimulationProgress]
    ) async -> LearningInsight? {
        let weakAreas = progress.filter { $0.performanceScore < 70 }

        let insightData: [String: SupabaseAnyCodable] = [
            "recommended_focus": SupabaseAnyCodable("cultural_competency"),
            "weak_areas_count": SupabaseAnyCodable(weakAreas.count),
            "next_action": SupabaseAnyCodable("practice_cultural_scenarios")
        ]

        return LearningInsight(
            id: UUID(),
            userId: userId,
            personaId: UUID(), // Would be determined based on analysis
            languageId: languageId,
            insightType: "recommendation",
            insightData: insightData,
            confidenceScore: 82.0,
            aiGenerated: true,
            isActionable: true,
            createdAt: Date(),
            expiresAt: Calendar.current.date(byAdding: .day, value: 7, to: Date())
        )
    }

    private func getCurrentUserId() -> UUID? {
        guard let userIdString = UserDefaults.standard.string(forKey: "user_id"),
              let userId = UUID(uuidString: userIdString) else { return nil }
        return userId
    }
}

// MARK: - Extensions

extension SimulationService {
    func getPersonaIcon(_ iconName: String) -> String {
        switch iconName {
        case "airplane": return "✈️"
        case "home": return "🏠"
        case "briefcase": return "💼"
        case "graduation": return "🎓"
        case "family": return "👨‍👩‍👧‍👦"
        case "medical": return "🏥"
        case "job": return "💼"
        case "social": return "🤝"
        default: return "👤"
        }
    }

    func getPersonaColor(_ colorTheme: String) -> String {
        switch colorTheme {
        case "blue": return "#007AFF"
        case "green": return "#34C759"
        case "purple": return "#AF52DE"
        case "orange": return "#FF9500"
        case "red": return "#FF3B30"
        case "pink": return "#FF2D92"
        case "teal": return "#5AC8FA"
        case "indigo": return "#5856D6"
        default: return "#007AFF"
        }
    }
}