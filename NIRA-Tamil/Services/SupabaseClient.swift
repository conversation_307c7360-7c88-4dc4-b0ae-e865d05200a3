import Foundation
import Combine
// import Supabase // Disabled for compilation

// MARK: - Mock Supabase Client for Compilation
// This is a simplified mock version to allow the app to compile without Supabase dependency

// Mock Supabase Models
struct MockSupabaseUser {
    let id: String
    let email: String?
}

struct MockSupabaseSession {
    let user: MockSupabaseUser
}

// Mock NIRASupabaseClient
@MainActor
class NIRASupabaseClient: ObservableObject {
    static let shared = NIRASupabaseClient()

    @Published var session: MockSupabaseSession?
    @Published var isConnected: Bool = false

    let client = MockSupabaseClient()

    private init() {
        // Mock initialization
    }

    func getUserProfile() async throws -> SupabaseUserProfile? {
        // Mock implementation
        return nil
    }

    func getLessonContentComplete(lessonId: UUID) async throws -> (
        vocabulary: [LessonVocabularyItem],
        conversations: [LessonDialogueItem],
        grammar: [LessonGrammarPoint],
        exercises: [LessonExerciseItem]
    ) {
        // Mock implementation - return empty content
        print("🔍 Mock getLessonContentComplete called for lesson: \(lessonId)")
        return (
            vocabulary: [],
            conversations: [],
            grammar: [],
            exercises: []
        )
    }

    // Note: getLessons is implemented in SupabaseModels.swift extension
}

// MARK: - Mock Supabase Models for Compilation

// Note: SupabaseUserProfile is defined in SupabaseModels.swift

// Note: SupabaseLesson is defined in SupabaseModels.swift

enum SupabaseError: Error {
    case notAuthenticated
}

// MARK: - Mock Supabase Client for Compilation
class MockSupabaseClient {
    let auth = MockSupabaseAuth()

    init() {
        // Mock initialization - no actual Supabase connection
        print("✅ Mock Supabase client initialized for compilation")
    }

    func from(_ table: String) -> MockSupabaseQueryBuilder {
        return MockSupabaseQueryBuilder()
    }
}

// MARK: - Mock Supabase Auth
class MockSupabaseAuth {
    var session: MockSupabaseSession? {
        get async throws {
            // Mock implementation - no session
            throw SupabaseError.notAuthenticated
        }
    }
}

// MARK: - Mock Supabase Query Builder
class MockSupabaseQueryBuilder {
    func select(_ columns: String = "*") -> MockSupabaseQueryBuilder {
        return self
    }

    func limit(_ count: Int) -> MockSupabaseQueryBuilder {
        return self
    }

    func eq(_ column: String, value: Any) -> MockSupabaseQueryBuilder {
        return self
    }

    func execute() async throws -> MockSupabaseResponse {
        // Mock implementation - return empty response
        return MockSupabaseResponse()
    }
}

// MARK: - Mock Supabase Response
struct MockSupabaseResponse {
    var value: [Any] {
        return [] // Return empty array for mock
    }
}

// MARK: - Review Log Database Models

struct ReviewLogRow: Codable {
    let id: UUID
    let card_id: UUID
    let user_id: UUID
    let rating: Int
    let state: String
    let due: Date
    let stability: Double
    let difficulty: Double
    let elapsed_days: Int
    let last_elapsed_days: Int
    let scheduled_days: Int
    let review_time: Date
    let response_time_ms: Int?
    let created_at: Date?
    
    private enum CodingKeys: String, CodingKey {
        case id, card_id, user_id, rating, state, due, stability, difficulty
        case elapsed_days, last_elapsed_days, scheduled_days, review_time
        case response_time_ms, created_at
    }
    
    init(from log: ReviewLog, userId: UUID) {
        self.id = UUID(uuidString: log.id) ?? UUID()
        self.card_id = UUID(uuidString: log.cardId) ?? UUID()
        self.user_id = userId
        self.rating = log.rating.rawValue
        self.state = log.state.rawValue
        self.due = log.due
        self.stability = log.stability
        self.difficulty = log.difficulty
        self.elapsed_days = log.elapsedDays
        self.last_elapsed_days = log.lastElapsedDays
        self.scheduled_days = log.scheduledDays
        self.review_time = log.review
        self.response_time_ms = nil
        self.created_at = nil
    }
    
    func toReviewLog() -> ReviewLog {
        return ReviewLog(
            id: id.uuidString,
            cardId: card_id.uuidString,
            rating: ReviewRating(rawValue: rating) ?? .good,
            state: CardState(rawValue: state) ?? .new,
            due: due,
            stability: stability,
            difficulty: difficulty,
            elapsedDays: elapsed_days,
            lastElapsedDays: last_elapsed_days,
            scheduledDays: scheduled_days,
            review: review_time
        )
    }
}

// MARK: - Assessment Database Models

struct AssessmentResponseRow: Codable {
    let id: UUID
    let assessment_id: UUID
    let item_id: UUID
    let user_id: UUID
    let user_answer: String
    let correct_answer: String
    let is_correct: Bool
    let time_spent: Int
    let attempts: Int
    let confidence_level: Double?
    let created_at: Date?
    
    private enum CodingKeys: String, CodingKey {
        case id, assessment_id, item_id, user_id, user_answer, correct_answer
        case is_correct, time_spent, attempts, confidence_level, created_at
    }
    
    init(from response: MicroAssessmentResponse, userId: UUID) {
        self.id = UUID(uuidString: response.id) ?? UUID()
        self.assessment_id = UUID(uuidString: response.assessmentId) ?? UUID()
        self.item_id = UUID(uuidString: response.itemId) ?? UUID()
        self.user_id = userId
        self.user_answer = response.userAnswer
        self.correct_answer = response.correctAnswer
        self.is_correct = response.isCorrect
        self.time_spent = Int(response.timeSpent * 1000) // Convert to milliseconds
        self.attempts = 1
        self.confidence_level = nil
        self.created_at = nil
    }
}

struct LearningUnitCompletionRow: Codable {
    let id: UUID
    let user_id: UUID
    let lesson_id: UUID?
    let unit_id: String
    let unit_type: String
    let accuracy_score: Double
    let completion_time: Int
    let attempts_needed: Int
    let difficulty_level: Int
    let engagement_score: Double
    let completed_at: Date
    
    private enum CodingKeys: String, CodingKey {
        case id, user_id, lesson_id, unit_id, unit_type, accuracy_score
        case completion_time, attempts_needed, difficulty_level, engagement_score, completed_at
    }
    
    init(from unit: CompletedLearningUnit, userId: UUID) {
        self.id = UUID(uuidString: unit.id) ?? UUID()
        self.user_id = userId
        self.lesson_id = nil
        self.unit_id = unit.id
        self.unit_type = unit.type.rawValue
        self.accuracy_score = unit.performance.accuracyScore
        self.completion_time = Int(unit.performance.completionTime)
        self.attempts_needed = unit.performance.attemptsNeeded
        self.difficulty_level = unit.performance.currentDifficulty
        self.engagement_score = unit.performance.engagementScore
        self.completed_at = unit.completedAt
    }
}

struct AssessmentReportRow: Codable {
    let id: UUID
    let assessment_id: UUID
    let user_id: UUID
    let overall_score: Double
    let total_time_spent: Int
    let completion_percentage: Double
    let skill_scores: [String: Double]
    let improved_areas: [String]
    let areas_for_focus: [String]
    let generated_at: Date
}

// MARK: - Date Extensions

extension Date {
    func ISO8601String() -> String {
        let formatter = ISO8601DateFormatter()
        return formatter.string(from: self)
    }
}