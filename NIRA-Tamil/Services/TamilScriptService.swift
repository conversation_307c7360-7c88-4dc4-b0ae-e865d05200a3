//
//  TamilScriptService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import Foundation
import Supabase
import Combine
#if canImport(UIKit)
import UIKit
#endif

@MainActor
class TamilScriptService: ObservableObject {
    static let shared = TamilScriptService()
    
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // Character Data
    @Published var allCharacters: [TamilCharacter] = []
    @Published var vowels: [TamilCharacter] = []
    @Published var consonants: [TamilCharacter] = []
    @Published var specialCharacters: [TamilCharacter] = []
    @Published var characterCombinations: [TamilCharacterCombination] = []
    
    // Writing Content
    @Published var writingContent: [TamilWritingContent] = []
    @Published var contentByLevel: [CEFRLevel: [TamilWritingContent]] = [:]
    @Published var contentByMode: [WritingMode: [TamilWritingContent]] = [:]
    
    // Progress Data
    @Published var userProgress: [UserWritingProgress] = []
    @Published var writingSessions: [WritingSession] = []
    @Published var achievements: [WritingAchievement] = []
    
    private let supabase: SupabaseClient
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        // TODO: Add when SupabaseService is available
        // self.supabase = SupabaseService.shared.client
    }
    
    // MARK: - Character Management
    
    /// Load all Tamil characters from database
    func loadAllCharacters() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let characters: [TamilCharacter] = try await supabase
                .from("tamil_characters")
                .select("*, stroke_orders(*)")
                .order("learning_order")
                .execute()
                .value
            
            allCharacters = characters
            categorizeCharacters()
            
            print("✅ Loaded \(characters.count) Tamil characters")
            
        } catch {
            errorMessage = "Failed to load Tamil characters: \(error.localizedDescription)"
            print("❌ Error loading characters: \(error)")
        }
        
        isLoading = false
    }
    
    /// Load character combinations
    func loadCharacterCombinations() async {
        do {
            let combinations: [TamilCharacterCombination] = try await supabase
                .from("tamil_character_combinations")
                .select("*")
                .order("learning_order")
                .execute()
                .value
            
            characterCombinations = combinations
            print("✅ Loaded \(combinations.count) character combinations")
            
        } catch {
            errorMessage = "Failed to load character combinations: \(error.localizedDescription)"
            print("❌ Error loading combinations: \(error)")
        }
    }
    
    /// Get character by Unicode value
    func getCharacter(by unicode: String) -> TamilCharacter? {
        return allCharacters.first { $0.unicodeValue == unicode }
    }
    
    /// Get character by romanization
    func getCharacterByRomanization(_ romanization: String) -> TamilCharacter? {
        return allCharacters.first { $0.romanization.lowercased() == romanization.lowercased() }
    }
    
    /// Get characters by difficulty level
    func getCharacters(byDifficulty level: Int) -> [TamilCharacter] {
        return allCharacters.filter { $0.difficultyLevel == level }
    }
    
    /// Get characters by type
    func getCharacters(byType type: TamilCharacter.CharacterType) -> [TamilCharacter] {
        return allCharacters.filter { $0.characterType == type }
    }
    
    /// Get stroke order for character
    func getStrokeOrder(for characterId: UUID) -> [TamilStrokeOrder] {
        guard let character = allCharacters.first(where: { $0.id == characterId }) else {
            return []
        }
        return character.strokeOrders.sorted { $0.strokeNumber < $1.strokeNumber }
    }
    
    // MARK: - Writing Content Management
    
    /// Load writing content for specific level and mode
    func loadWritingContent(level: CEFRLevel? = nil, mode: WritingMode? = nil) async {
        isLoading = true
        
        do {
            var query = supabase
                .from("tamil_writing_content")
                .select("*")
                .eq("is_active", value: true)
            
            if let level = level {
                query = query.eq("cefr_level", value: level.rawValue)
            }
            
            if let mode = mode {
                query = query.eq("writing_mode", value: mode.rawValue)
            }
            
            let content: [TamilWritingContent] = try await query
                .order("difficulty_score")
                .execute()
                .value
            
            writingContent = content
            organizeContentByLevelAndMode()
            
            print("✅ Loaded \(content.count) writing content items")
            
        } catch {
            errorMessage = "Failed to load writing content: \(error.localizedDescription)"
            print("❌ Error loading writing content: \(error)")
        }
        
        isLoading = false
    }
    
    /// Get writing content for lesson
    func getWritingContent(for lessonId: String) -> [TamilWritingContent] {
        return writingContent.filter { $0.lessonId == lessonId }
    }
    
    /// Get writing content by character
    func getWritingContent(for characterId: UUID) -> [TamilWritingContent] {
        return writingContent.filter { $0.targetCharacters.contains(characterId) }
    }
    
    // MARK: - Progress Management
    
    /// Load user writing progress
    func loadUserProgress(userId: UUID) async {
        do {
            let progress: [UserWritingProgress] = try await supabase
                .from("user_writing_progress")
                .select("*")
                .eq("user_id", value: userId.uuidString)
                .order("practice_date", ascending: false)
                .execute()
                .value
            
            userProgress = progress
            print("✅ Loaded \(progress.count) progress records")
            
        } catch {
            errorMessage = "Failed to load user progress: \(error.localizedDescription)"
            print("❌ Error loading progress: \(error)")
        }
    }
    
    /// Save writing progress
    func saveProgress(_ progress: UserWritingProgress) async {
        do {
            let _: UserWritingProgress = try await supabase
                .from("user_writing_progress")
                .insert(progress)
                .execute()
                .value
            
            // Update local progress
            if let index = userProgress.firstIndex(where: { $0.id == progress.id }) {
                userProgress[index] = progress
            } else {
                userProgress.append(progress)
            }
            
            print("✅ Saved writing progress")
            
        } catch {
            errorMessage = "Failed to save progress: \(error.localizedDescription)"
            print("❌ Error saving progress: \(error)")
        }
    }
    
    /// Get character mastery level
    func getCharacterMastery(characterId: UUID, userId: UUID) -> UserWritingProgress.MasteryLevel {
        let characterProgress = userProgress.filter { 
            $0.characterId == characterId && $0.userId == userId 
        }
        
        guard !characterProgress.isEmpty else { return .beginner }
        
        let averageAccuracy = characterProgress.compactMap { $0.accuracyScore }.reduce(0, +) / Double(characterProgress.count)
        let completedSessions = characterProgress.filter { $0.isCompleted }.count
        
        if averageAccuracy >= 95 && completedSessions >= 5 {
            return .mastered
        } else if averageAccuracy >= 85 && completedSessions >= 3 {
            return .advanced
        } else if averageAccuracy >= 70 && completedSessions >= 2 {
            return .intermediate
        } else {
            return .beginner
        }
    }
    
    /// Get overall writing accuracy
    func getOverallAccuracy(userId: UUID, days: Int = 7) -> Double {
        let recentProgress = userProgress.filter { progress in
            progress.userId == userId &&
            Calendar.current.dateInterval(of: .day, for: Date())?.contains(progress.practiceDate) == true
        }
        
        let accuracyScores = recentProgress.compactMap { $0.accuracyScore }
        guard !accuracyScores.isEmpty else { return 0.0 }
        
        return accuracyScores.reduce(0, +) / Double(accuracyScores.count)
    }
    
    // MARK: - Writing Session Management
    
    /// Start new writing session
    func startWritingSession(userId: UUID, sessionType: WritingSession.SessionType, lessonId: String? = nil) async -> WritingSession {
        let session = WritingSession(
            id: UUID(),
            userId: userId,
            sessionType: sessionType,
            lessonId: lessonId,
            totalCharactersPracticed: 0,
            totalAccuracyScore: nil,
            sessionDurationSeconds: nil,
            charactersMastered: 0,
            improvementAreas: [],
            sessionNotes: nil,
            deviceType: getCurrentDeviceType(),
            startedAt: Date(),
            completedAt: nil,
            isCompleted: false
        )
        
        do {
            let _: WritingSession = try await supabase
                .from("writing_sessions")
                .insert(session)
                .execute()
                .value
            
            writingSessions.append(session)
            print("✅ Started writing session")
            
        } catch {
            errorMessage = "Failed to start session: \(error.localizedDescription)"
            print("❌ Error starting session: \(error)")
        }
        
        return session
    }
    
    /// Complete writing session
    func completeWritingSession(_ session: WritingSession, totalAccuracy: Double, charactersMastered: Int) async {
        let completedSession = WritingSession(
            id: session.id,
            userId: session.userId,
            sessionType: session.sessionType,
            lessonId: session.lessonId,
            totalCharactersPracticed: session.totalCharactersPracticed,
            totalAccuracyScore: totalAccuracy,
            sessionDurationSeconds: Int(Date().timeIntervalSince(session.startedAt)),
            charactersMastered: charactersMastered,
            improvementAreas: session.improvementAreas,
            sessionNotes: session.sessionNotes,
            deviceType: session.deviceType,
            startedAt: session.startedAt,
            completedAt: Date(),
            isCompleted: true
        )
        
        do {
            let _: WritingSession = try await supabase
                .from("writing_sessions")
                .update(completedSession)
                .eq("id", value: session.id.uuidString)
                .execute()
                .value
            
            // Update local sessions
            if let index = writingSessions.firstIndex(where: { $0.id == session.id }) {
                writingSessions[index] = completedSession
            }
            
            print("✅ Completed writing session")
            
        } catch {
            errorMessage = "Failed to complete session: \(error.localizedDescription)"
            print("❌ Error completing session: \(error)")
        }
    }
    
    // MARK: - Helper Methods
    
    private func categorizeCharacters() {
        vowels = allCharacters.filter { $0.characterType == .vowel }
        consonants = allCharacters.filter { $0.characterType == .consonant }
        specialCharacters = allCharacters.filter { $0.characterType == .special }
    }
    
    private func organizeContentByLevelAndMode() {
        // Group by CEFR level
        contentByLevel = Dictionary(grouping: writingContent) { $0.cefrLevel }
        
        // Group by writing mode
        contentByMode = Dictionary(grouping: writingContent) { $0.writingMode }
    }
    
    private func getCurrentDeviceType() -> UserWritingProgress.DeviceType {
        #if os(iOS)
        return UIDevice.current.userInterfaceIdiom == .pad ? .iPad : .iPhone
        #elseif os(watchOS)
        return .appleWatch
        #elseif os(macOS)
        return .mac
        #else
        return .iPhone
        #endif
    }
    
    /// Load all data needed for writing practice
    func loadAllWritingData() async {
        await loadAllCharacters()
        await loadCharacterCombinations()
        await loadWritingContent()
    }
    
    /// Get recommended characters for practice based on user level
    func getRecommendedCharacters(for level: CEFRLevel, limit: Int = 10) -> [TamilCharacter] {
        let maxDifficulty = level == .a1 ? 2 : (level == .a2 ? 3 : 5)
        
        return allCharacters
            .filter { $0.difficultyLevel <= maxDifficulty }
            .sorted { $0.learningOrder ?? 999 < $1.learningOrder ?? 999 }
            .prefix(limit)
            .map { $0 }
    }
}
