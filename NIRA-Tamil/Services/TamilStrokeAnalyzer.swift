//
//  TamilStrokeAnalyzer.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import Foundation
import PencilKit
import CoreGraphics
import UIKit

@MainActor
class TamilStrokeAnalyzer: ObservableObject {
    
    // MARK: - Stroke Analysis Result
    
    struct StrokeAnalysisResult {
        let overallAccuracy: Double
        let pathAccuracy: Double
        let directionAccuracy: Double
        let timingAccuracy: Double
        let pressureAccuracy: Double
        let suggestions: [String]
        let errors: [StrokeError]
    }
    
    struct StrokeError {
        let type: ErrorType
        let severity: Severity
        let description: String
        let suggestion: String
        
        enum ErrorType {
            case wrongDirection
            case incorrectPath
            case poorTiming
            case inconsistentPressure
            case wrongStartPoint
            case wrongEndPoint
        }
        
        enum Severity {
            case minor, moderate, major, critical
        }
    }
    
    // MARK: - Analysis Methods
    
    /// Analyze a user stroke against the expected stroke order
    func analyzeStroke(_ userStroke: PKStroke, against expectedStroke: TamilStrokeOrder) -> StrokeAnalysisResult {
        let pathAccuracy = analyzeStrokePath(userStroke, expected: expectedStroke)
        let directionAccuracy = analyzeStrokeDirection(userStroke, expected: expectedStroke)
        let timingAccuracy = analyzeStrokeTiming(userStroke, expected: expectedStroke)
        let pressureAccuracy = analyzeStrokePressure(userStroke, expected: expectedStroke)
        
        let overallAccuracy = calculateOverallAccuracy(
            path: pathAccuracy,
            direction: directionAccuracy,
            timing: timingAccuracy,
            pressure: pressureAccuracy
        )
        
        let errors = identifyStrokeErrors(
            userStroke: userStroke,
            expectedStroke: expectedStroke,
            pathAccuracy: pathAccuracy,
            directionAccuracy: directionAccuracy
        )
        
        let suggestions = generateImprovementSuggestions(errors: errors)
        
        return StrokeAnalysisResult(
            overallAccuracy: overallAccuracy,
            pathAccuracy: pathAccuracy,
            directionAccuracy: directionAccuracy,
            timingAccuracy: timingAccuracy,
            pressureAccuracy: pressureAccuracy,
            suggestions: suggestions,
            errors: errors
        )
    }
    
    // MARK: - Path Analysis
    
    private func analyzeStrokePath(_ userStroke: PKStroke, expected: TamilStrokeOrder) -> Double {
        let userPath = userStroke.path
        let expectedPath = createExpectedPath(from: expected)
        
        // Sample points along both paths
        let userPoints = samplePathPoints(userPath, sampleCount: 50)
        let expectedPoints = samplePathPoints(expectedPath, sampleCount: 50)
        
        // Calculate path deviation
        let deviations = zip(userPoints, expectedPoints).map { userPoint, expectedPoint in
            distance(from: userPoint, to: expectedPoint)
        }
        
        let averageDeviation = deviations.reduce(0, +) / Double(deviations.count)
        let maxAllowedDeviation: Double = 20.0 // pixels
        
        return max(0, 1.0 - (averageDeviation / maxAllowedDeviation))
    }
    
    private func createExpectedPath(from strokeOrder: TamilStrokeOrder) -> UIBezierPath {
        let path = UIBezierPath()
        path.move(to: strokeOrder.startPoint)
        
        if let controlPoints = strokeOrder.controlPoints, !controlPoints.isEmpty {
            // Bezier curve
            if controlPoints.count == 1 {
                path.addQuadCurve(to: strokeOrder.endPoint, controlPoint: controlPoints[0])
            } else if controlPoints.count >= 2 {
                path.addCurve(to: strokeOrder.endPoint, 
                            controlPoint1: controlPoints[0], 
                            controlPoint2: controlPoints[1])
            }
        } else {
            // Straight line
            path.addLine(to: strokeOrder.endPoint)
        }
        
        return path
    }
    
    private func samplePathPoints(_ path: UIBezierPath, sampleCount: Int) -> [CGPoint] {
        var points: [CGPoint] = []
        let pathLength = path.length
        
        for i in 0..<sampleCount {
            let t = Double(i) / Double(sampleCount - 1)
            let distance = t * pathLength
            let point = path.point(at: distance)
            points.append(point)
        }
        
        return points
    }
    
    // MARK: - Direction Analysis
    
    private func analyzeStrokeDirection(_ userStroke: PKStroke, expected: TamilStrokeOrder) -> Double {
        let userDirection = calculateStrokeDirection(userStroke)
        let expectedDirection = expected.strokeDirection
        
        return calculateDirectionSimilarity(userDirection, expected: expectedDirection)
    }
    
    private func calculateStrokeDirection(_ stroke: PKStroke) -> TamilStrokeOrder.StrokeDirection {
        let path = stroke.path
        let startPoint = path.interpolatedLocation(at: 0.0).location
        let endPoint = path.interpolatedLocation(at: 1.0).location
        
        let deltaX = endPoint.x - startPoint.x
        let deltaY = endPoint.y - startPoint.y
        
        let angle = atan2(deltaY, deltaX)
        let degrees = angle * 180 / .pi
        
        // Determine direction based on angle
        switch degrees {
        case -45...45:
            return .leftToRight
        case 45...135:
            return .topToBottom
        case 135...180, -180...(-135):
            return .rightToLeft
        case -135...(-45):
            return .bottomToTop
        default:
            return .leftToRight
        }
    }
    
    private func calculateDirectionSimilarity(_ userDirection: TamilStrokeOrder.StrokeDirection, 
                                            expected: TamilStrokeOrder.StrokeDirection) -> Double {
        if userDirection == expected {
            return 1.0
        }
        
        // Some directions are more similar than others
        let similarityMatrix: [TamilStrokeOrder.StrokeDirection: [TamilStrokeOrder.StrokeDirection: Double]] = [
            .leftToRight: [.rightToLeft: 0.3, .topToBottom: 0.1, .bottomToTop: 0.1],
            .rightToLeft: [.leftToRight: 0.3, .topToBottom: 0.1, .bottomToTop: 0.1],
            .topToBottom: [.bottomToTop: 0.3, .leftToRight: 0.1, .rightToLeft: 0.1],
            .bottomToTop: [.topToBottom: 0.3, .leftToRight: 0.1, .rightToLeft: 0.1],
            .clockwise: [.counterClockwise: 0.2],
            .counterClockwise: [.clockwise: 0.2]
        ]
        
        return similarityMatrix[expected]?[userDirection] ?? 0.0
    }
    
    // MARK: - Timing Analysis
    
    private func analyzeStrokeTiming(_ userStroke: PKStroke, expected: TamilStrokeOrder) -> Double {
        let userDuration = calculateStrokeDuration(userStroke)
        let expectedDuration = Double(expected.timingDuration) / 1000.0 // Convert ms to seconds
        
        let timingRatio = min(userDuration, expectedDuration) / max(userDuration, expectedDuration)
        
        // Good timing is within 50% of expected duration
        if timingRatio >= 0.5 {
            return timingRatio
        } else {
            return timingRatio * 0.5 // Penalize heavily for very poor timing
        }
    }
    
    private func calculateStrokeDuration(_ stroke: PKStroke) -> Double {
        let path = stroke.path
        let startTime = path.interpolatedLocation(at: 0.0).timeOffset
        let endTime = path.interpolatedLocation(at: 1.0).timeOffset
        return endTime - startTime
    }
    
    // MARK: - Pressure Analysis
    
    private func analyzeStrokePressure(_ userStroke: PKStroke, expected: TamilStrokeOrder) -> Double {
        guard let expectedPressure = expected.pressureVariation else {
            return 1.0 // No pressure requirements
        }
        
        let userPressurePoints = extractPressurePoints(from: userStroke)
        let expectedPressurePoints = expectedPressure
        
        // Compare pressure patterns
        let pressureDeviations = zip(userPressurePoints, expectedPressurePoints).map { user, expected in
            abs(user.pressure - expected.pressure)
        }
        
        let averageDeviation = pressureDeviations.reduce(0, +) / Double(pressureDeviations.count)
        return max(0, 1.0 - averageDeviation)
    }
    
    private func extractPressurePoints(from stroke: PKStroke) -> [PressurePoint] {
        let path = stroke.path
        var pressurePoints: [PressurePoint] = []
        
        let sampleCount = 20
        for i in 0..<sampleCount {
            let t = Double(i) / Double(sampleCount - 1)
            let location = path.interpolatedLocation(at: t)
            
            let pressurePoint = PressurePoint(
                position: t,
                pressure: Double(location.force),
                timestamp: location.timeOffset
            )
            pressurePoints.append(pressurePoint)
        }
        
        return pressurePoints
    }
    
    // MARK: - Overall Accuracy Calculation
    
    private func calculateOverallAccuracy(path: Double, direction: Double, timing: Double, pressure: Double) -> Double {
        // Weighted average - path and direction are most important for Tamil writing
        let pathWeight = 0.4
        let directionWeight = 0.3
        let timingWeight = 0.2
        let pressureWeight = 0.1
        
        return (path * pathWeight) + 
               (direction * directionWeight) + 
               (timing * timingWeight) + 
               (pressure * pressureWeight)
    }
    
    // MARK: - Error Identification
    
    private func identifyStrokeErrors(userStroke: PKStroke, 
                                    expectedStroke: TamilStrokeOrder,
                                    pathAccuracy: Double,
                                    directionAccuracy: Double) -> [StrokeError] {
        var errors: [StrokeError] = []
        
        // Path errors
        if pathAccuracy < 0.7 {
            errors.append(StrokeError(
                type: .incorrectPath,
                severity: pathAccuracy < 0.4 ? .major : .moderate,
                description: "The stroke path deviates from the expected path",
                suggestion: "Try to follow the guide line more closely"
            ))
        }
        
        // Direction errors
        if directionAccuracy < 0.8 {
            errors.append(StrokeError(
                type: .wrongDirection,
                severity: directionAccuracy < 0.5 ? .major : .moderate,
                description: "The stroke direction is incorrect",
                suggestion: "Pay attention to the stroke direction: \(expectedStroke.strokeDirection.displayName)"
            ))
        }
        
        return errors
    }
    
    // MARK: - Improvement Suggestions
    
    private func generateImprovementSuggestions(errors: [StrokeError]) -> [String] {
        var suggestions: [String] = []
        
        for error in errors {
            suggestions.append(error.suggestion)
        }
        
        // Add general suggestions based on error patterns
        if errors.contains(where: { $0.type == .wrongDirection }) {
            suggestions.append("Practice stroke direction with the guidance overlay enabled")
        }
        
        if errors.contains(where: { $0.type == .incorrectPath }) {
            suggestions.append("Slow down and focus on accuracy over speed")
        }
        
        return Array(Set(suggestions)) // Remove duplicates
    }
    
    // MARK: - Helper Functions
    
    private func distance(from point1: CGPoint, to point2: CGPoint) -> Double {
        let dx = point1.x - point2.x
        let dy = point1.y - point2.y
        return sqrt(Double(dx * dx + dy * dy))
    }
}

// MARK: - UIBezierPath Extensions

extension UIBezierPath {
    var length: Double {
        // Approximate path length by sampling points
        var length: Double = 0
        let sampleCount = 100
        var previousPoint: CGPoint?
        
        for i in 0...sampleCount {
            let t = Double(i) / Double(sampleCount)
            let point = self.point(at: t * self.length)
            
            if let prev = previousPoint {
                length += sqrt(pow(Double(point.x - prev.x), 2) + pow(Double(point.y - prev.y), 2))
            }
            previousPoint = point
        }
        
        return length
    }
    
    func point(at distance: Double) -> CGPoint {
        // This is a simplified implementation
        // In practice, you'd need a more sophisticated path sampling algorithm
        return CGPoint(x: 0, y: 0)
    }
}
