//
//  TamilWritingAssessmentEngine.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import Foundation
import PencilKit
import UIKit

@MainActor
class TamilWritingAssessmentEngine: ObservableObject {
    static let shared = TamilWritingAssessmentEngine()

    @Published var currentAssessment: WritingAssessment?
    @Published var assessmentHistory: [WritingAssessment] = []
    @Published var isAssessing = false
    
    private let strokeAnalyzer = TamilStrokeAnalyzer()
    private let characterRecognizer = TamilCharacterRecognizer()
    private let scriptService = TamilScriptService.shared

    private init() {}
    
    // MARK: - Assessment Models
    
    struct WritingAssessment: Identifiable, Codable {
        let id: UUID
        let userId: UUID
        let character: TamilCharacter
        let writingMode: WritingMode
        let startTime: Date
        let endTime: Date
        let overallScore: Double
        let metrics: AssessmentMetrics
        let feedback: AssessmentFeedback
        let recommendations: [String]
        let achievements: [String]
        let nextSteps: [String]
        
        var duration: TimeInterval {
            endTime.timeIntervalSince(startTime)
        }
        
        var grade: AssessmentGrade {
            switch overallScore {
            case 0.9...1.0: return .excellent
            case 0.8..<0.9: return .good
            case 0.7..<0.8: return .satisfactory
            case 0.6..<0.7: return .needsImprovement
            default: return .poor
            }
        }
    }
    
    struct AssessmentMetrics: Codable {
        let accuracy: Double
        let strokeOrder: Double
        let characterFormation: Double
        let timing: Double
        let consistency: Double
        let pressure: Double
        let spacing: Double
        let recognition: Double
        
        var averageScore: Double {
            (accuracy + strokeOrder + characterFormation + timing + consistency + pressure + spacing + recognition) / 8.0
        }
    }
    
    struct AssessmentFeedback: Codable {
        let strengths: [String]
        let weaknesses: [String]
        let specificIssues: [IssueDetail]
        let improvementAreas: [ImprovementArea]
        let culturalNotes: [String]
    }
    
    struct IssueDetail: Codable {
        let strokeNumber: Int?
        let issue: String
        let severity: IssueSeverity
        let suggestion: String
    }
    
    enum IssueSeverity: String, Codable, CaseIterable {
        case minor = "minor"
        case moderate = "moderate"
        case major = "major"
        case critical = "critical"
        
        var color: UIColor {
            switch self {
            case .minor: return .systemGreen
            case .moderate: return .systemYellow
            case .major: return .systemOrange
            case .critical: return .systemRed
            }
        }
    }
    
    enum AssessmentGrade: String, Codable, CaseIterable {
        case excellent = "excellent"
        case good = "good"
        case satisfactory = "satisfactory"
        case needsImprovement = "needs_improvement"
        case poor = "poor"
        
        var displayName: String {
            switch self {
            case .excellent: return "Excellent"
            case .good: return "Good"
            case .satisfactory: return "Satisfactory"
            case .needsImprovement: return "Needs Improvement"
            case .poor: return "Poor"
            }
        }
        
        var emoji: String {
            switch self {
            case .excellent: return "🌟"
            case .good: return "👍"
            case .satisfactory: return "👌"
            case .needsImprovement: return "📈"
            case .poor: return "💪"
            }
        }
    }
    
    // MARK: - Assessment Methods
    
    /// Perform comprehensive assessment of user's writing
    func assessWriting(
        character: TamilCharacter,
        userStrokes: [PKStroke],
        writingMode: WritingMode,
        userId: UUID,
        startTime: Date
    ) async -> WritingAssessment {
        
        isAssessing = true
        defer { isAssessing = false }
        
        let endTime = Date()
        
        // Get expected stroke orders
        let expectedStrokes = scriptService.getStrokeOrder(for: character.id)
        
        // Perform detailed analysis
        let metrics = await analyzeWritingMetrics(
            userStrokes: userStrokes,
            expectedStrokes: expectedStrokes,
            character: character,
            duration: endTime.timeIntervalSince(startTime)
        )
        
        // Generate feedback
        let feedback = generateDetailedFeedback(
            metrics: metrics,
            character: character,
            userStrokes: userStrokes,
            expectedStrokes: expectedStrokes
        )
        
        // Calculate overall score
        let overallScore = calculateOverallScore(metrics: metrics, writingMode: writingMode)
        
        // Generate recommendations
        let recommendations = generateRecommendations(
            metrics: metrics,
            feedback: feedback,
            character: character
        )
        
        // Check for achievements
        let achievements = checkForAchievements(
            metrics: metrics,
            character: character,
            userId: userId
        )
        
        // Generate next steps
        let nextSteps = generateNextSteps(
            metrics: metrics,
            character: character,
            grade: AssessmentGrade.fromScore(overallScore)
        )
        
        let assessment = WritingAssessment(
            id: UUID(),
            userId: userId,
            character: character,
            writingMode: writingMode,
            startTime: startTime,
            endTime: endTime,
            overallScore: overallScore,
            metrics: metrics,
            feedback: feedback,
            recommendations: recommendations,
            achievements: achievements,
            nextSteps: nextSteps
        )
        
        currentAssessment = assessment
        assessmentHistory.append(assessment)
        
        // Save to database
        await saveAssessment(assessment)
        
        return assessment
    }
    
    // MARK: - Metrics Analysis
    
    private func analyzeWritingMetrics(
        userStrokes: [PKStroke],
        expectedStrokes: [TamilStrokeOrder],
        character: TamilCharacter,
        duration: TimeInterval
    ) async -> AssessmentMetrics {
        
        var strokeOrderScore = 0.0
        var accuracyScores: [Double] = []
        var timingScores: [Double] = []
        var pressureScores: [Double] = []
        
        // Analyze each stroke
        for (index, userStroke) in userStrokes.enumerated() {
            if index < expectedStrokes.count {
                let expectedStroke = expectedStrokes[index]
                let analysis = strokeAnalyzer.analyzeStroke(userStroke, against: expectedStroke)
                
                accuracyScores.append(analysis.pathAccuracy)
                timingScores.append(analysis.timingAccuracy)
                pressureScores.append(analysis.pressureAccuracy)
                strokeOrderScore += analysis.directionAccuracy
            }
        }
        
        // Character recognition analysis
        let recognitionScore = await analyzeCharacterRecognition(userStrokes: userStrokes, character: character)
        
        // Formation analysis
        let formationScore = analyzeCharacterFormation(userStrokes: userStrokes, character: character)
        
        // Consistency analysis
        let consistencyScore = analyzeWritingConsistency(userStrokes: userStrokes)
        
        // Spacing analysis (for multi-character writing)
        let spacingScore = analyzeSpacing(userStrokes: userStrokes)
        
        return AssessmentMetrics(
            accuracy: accuracyScores.isEmpty ? 0 : accuracyScores.reduce(0, +) / Double(accuracyScores.count),
            strokeOrder: strokeOrderScore / Double(max(1, expectedStrokes.count)),
            characterFormation: formationScore,
            timing: timingScores.isEmpty ? 0 : timingScores.reduce(0, +) / Double(timingScores.count),
            consistency: consistencyScore,
            pressure: pressureScores.isEmpty ? 0 : pressureScores.reduce(0, +) / Double(pressureScores.count),
            spacing: spacingScore,
            recognition: recognitionScore
        )
    }
    
    private func analyzeCharacterRecognition(userStrokes: [PKStroke], character: TamilCharacter) async -> Double {
        // Create image from strokes
        let drawing = PKDrawing()
        userStrokes.forEach { drawing.strokes.append($0) }
        let image = drawing.image(from: CGRect(x: 0, y: 0, width: 300, height: 300), scale: 1.0)
        
        do {
            let results = try await characterRecognizer.recognizeCharacter(in: image)
            
            // Find best match for target character
            if let match = results.first(where: { $0.recognizedCharacter == character.character }) {
                return match.confidence
            } else {
                // Check for similar characters
                let similarityScore = results.first?.confidence ?? 0.0
                return similarityScore * 0.5 // Partial credit for similar characters
            }
        } catch {
            return 0.0
        }
    }
    
    private func analyzeCharacterFormation(userStrokes: [PKStroke], character: TamilCharacter) -> Double {
        // Analyze overall character shape and proportions
        guard !userStrokes.isEmpty else { return 0.0 }
        
        let boundingBox = calculateBoundingBox(userStrokes: userStrokes)
        let expectedAspectRatio = getExpectedAspectRatio(for: character)
        let actualAspectRatio = boundingBox.width / boundingBox.height
        
        let aspectRatioScore = 1.0 - min(1.0, abs(expectedAspectRatio - actualAspectRatio) / expectedAspectRatio)
        
        // Check stroke count
        let strokeCountScore = userStrokes.count == character.strokeCount ? 1.0 : 0.7
        
        return (aspectRatioScore + strokeCountScore) / 2.0
    }
    
    private func analyzeWritingConsistency(userStrokes: [PKStroke]) -> Double {
        guard userStrokes.count > 1 else { return 1.0 }
        
        // Analyze pressure consistency
        let pressures = userStrokes.map { stroke in
            let path = stroke.path
            return path.interpolatedLocation(at: 0.5).force // Mid-stroke pressure
        }
        
        let averagePressure = pressures.reduce(0, +) / Float(pressures.count)
        let pressureVariance = pressures.map { pow($0 - averagePressure, 2) }.reduce(0, +) / Float(pressures.count)
        
        // Lower variance indicates better consistency
        return max(0, 1.0 - Double(pressureVariance))
    }
    
    private func analyzeSpacing(userStrokes: [PKStroke]) -> Double {
        // For single character, spacing is not applicable
        if userStrokes.count <= 1 {
            return 1.0
        }
        
        // Analyze spacing between stroke groups (for compound characters)
        return 0.8 // Placeholder - would implement proper spacing analysis
    }
    
    // MARK: - Feedback Generation
    
    private func generateDetailedFeedback(
        metrics: AssessmentMetrics,
        character: TamilCharacter,
        userStrokes: [PKStroke],
        expectedStrokes: [TamilStrokeOrder]
    ) -> AssessmentFeedback {
        
        var strengths: [String] = []
        var weaknesses: [String] = []
        var specificIssues: [IssueDetail] = []
        var improvementAreas: [ImprovementArea] = []
        var culturalNotes: [String] = []
        
        // Analyze strengths
        if metrics.accuracy > 0.8 {
            strengths.append("Excellent stroke accuracy")
        }
        if metrics.strokeOrder > 0.8 {
            strengths.append("Good understanding of stroke order")
        }
        if metrics.timing > 0.7 {
            strengths.append("Appropriate writing speed")
        }
        if metrics.recognition > 0.8 {
            strengths.append("Character is clearly recognizable")
        }
        
        // Analyze weaknesses
        if metrics.accuracy < 0.6 {
            weaknesses.append("Stroke paths need improvement")
            improvementAreas.append(ImprovementArea(
                areaType: .characterFormation,
                description: "Focus on following the correct stroke paths",
                severity: .medium,
                suggestions: ["Practice with guidance overlay", "Slow down your writing"],
                practiceRecommendations: ["Repeat this character 10 times with guidance"]
            ))
        }
        
        if metrics.strokeOrder < 0.6 {
            weaknesses.append("Stroke order needs attention")
            improvementAreas.append(ImprovementArea(
                areaType: .strokeOrder,
                description: "Learn the correct stroke sequence",
                severity: .high,
                suggestions: ["Watch stroke order animation", "Practice one stroke at a time"],
                practiceRecommendations: ["Complete stroke order tutorial for this character"]
            ))
        }
        
        // Generate specific issues for each stroke
        for (index, userStroke) in userStrokes.enumerated() {
            if index < expectedStrokes.count {
                let expectedStroke = expectedStrokes[index]
                let analysis = strokeAnalyzer.analyzeStroke(userStroke, against: expectedStroke)
                
                if analysis.overallAccuracy < 0.6 {
                    specificIssues.append(IssueDetail(
                        strokeNumber: index + 1,
                        issue: "Stroke \(index + 1) deviates from expected path",
                        severity: analysis.overallAccuracy < 0.4 ? .major : .moderate,
                        suggestion: "Focus on the \(expectedStroke.strokeDirection.displayName) direction"
                    ))
                }
            }
        }
        
        // Add cultural notes
        culturalNotes.append("The character '\(character.character)' (\(character.characterNameTamil)) is fundamental in Tamil writing")
        if character.characterType == .vowel {
            culturalNotes.append("Vowels in Tamil are the foundation of pronunciation and meaning")
        }
        
        return AssessmentFeedback(
            strengths: strengths,
            weaknesses: weaknesses,
            specificIssues: specificIssues,
            improvementAreas: improvementAreas,
            culturalNotes: culturalNotes
        )
    }
    
    // MARK: - Score Calculation
    
    private func calculateOverallScore(metrics: AssessmentMetrics, writingMode: WritingMode) -> Double {
        // Weight different metrics based on writing mode
        switch writingMode {
        case .guided:
            // In guided mode, focus more on following instructions
            return (metrics.strokeOrder * 0.3) +
                   (metrics.accuracy * 0.25) +
                   (metrics.characterFormation * 0.2) +
                   (metrics.timing * 0.1) +
                   (metrics.consistency * 0.1) +
                   (metrics.recognition * 0.05)
            
        case .freeform:
            // In freeform mode, focus more on final result
            return (metrics.recognition * 0.3) +
                   (metrics.characterFormation * 0.25) +
                   (metrics.accuracy * 0.2) +
                   (metrics.consistency * 0.15) +
                   (metrics.strokeOrder * 0.1)
            
        case .assessment:
            // In assessment mode, all metrics are important
            return metrics.averageScore
        }
    }
    
    // MARK: - Recommendations and Next Steps
    
    private func generateRecommendations(
        metrics: AssessmentMetrics,
        feedback: AssessmentFeedback,
        character: TamilCharacter
    ) -> [String] {
        var recommendations: [String] = []
        
        if metrics.strokeOrder < 0.7 {
            recommendations.append("Practice stroke order with the animated guide")
        }
        
        if metrics.accuracy < 0.7 {
            recommendations.append("Use the guidance overlay to improve accuracy")
        }
        
        if metrics.timing < 0.6 {
            recommendations.append("Practice writing at a steady, moderate pace")
        }
        
        if metrics.consistency < 0.6 {
            recommendations.append("Focus on maintaining consistent pressure and speed")
        }
        
        if recommendations.isEmpty {
            recommendations.append("Excellent work! Try practicing more complex characters")
        }
        
        return recommendations
    }
    
    private func checkForAchievements(
        metrics: AssessmentMetrics,
        character: TamilCharacter,
        userId: UUID
    ) -> [String] {
        var achievements: [String] = []
        
        if metrics.averageScore >= 0.95 {
            achievements.append("Perfect Writing! 🌟")
        } else if metrics.averageScore >= 0.9 {
            achievements.append("Excellent Writing! ⭐")
        }
        
        if metrics.strokeOrder >= 0.95 {
            achievements.append("Stroke Order Master! 🎯")
        }
        
        if metrics.recognition >= 0.95 {
            achievements.append("Crystal Clear Writing! 💎")
        }
        
        return achievements
    }
    
    private func generateNextSteps(
        metrics: AssessmentMetrics,
        character: TamilCharacter,
        grade: AssessmentGrade
    ) -> [String] {
        var nextSteps: [String] = []
        
        switch grade {
        case .excellent:
            nextSteps.append("Try writing this character in different sizes")
            nextSteps.append("Practice combining with other characters")
            nextSteps.append("Move to the next character in the sequence")
            
        case .good:
            nextSteps.append("Practice this character 5 more times")
            nextSteps.append("Focus on the areas that need improvement")
            
        case .satisfactory:
            nextSteps.append("Repeat this character with guidance enabled")
            nextSteps.append("Watch the stroke order animation again")
            
        case .needsImprovement, .poor:
            nextSteps.append("Start with guided practice mode")
            nextSteps.append("Practice individual strokes first")
            nextSteps.append("Take your time and focus on accuracy")
        }
        
        return nextSteps
    }
    
    // MARK: - Helper Methods
    
    private func calculateBoundingBox(userStrokes: [PKStroke]) -> CGRect {
        guard !userStrokes.isEmpty else { return .zero }
        
        var minX: CGFloat = .greatestFiniteMagnitude
        var maxX: CGFloat = -.greatestFiniteMagnitude
        var minY: CGFloat = .greatestFiniteMagnitude
        var maxY: CGFloat = -.greatestFiniteMagnitude
        
        for stroke in userStrokes {
            let strokeBounds = stroke.path.boundingBox
            minX = min(minX, strokeBounds.minX)
            maxX = max(maxX, strokeBounds.maxX)
            minY = min(minY, strokeBounds.minY)
            maxY = max(maxY, strokeBounds.maxY)
        }
        
        return CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
    }
    
    private func getExpectedAspectRatio(for character: TamilCharacter) -> CGFloat {
        // Return expected aspect ratio for character
        // This would be stored in the database in practice
        switch character.character {
        case "அ", "ஆ": return 0.8
        case "இ", "ஈ": return 0.6
        case "உ", "ஊ": return 0.7
        default: return 0.75
        }
    }
    
    private func saveAssessment(_ assessment: WritingAssessment) async {
        // Save assessment to database
        // Implementation would use SupabaseService
        print("Assessment saved: \(assessment.overallScore)")
    }
}

// MARK: - Extensions

extension TamilWritingAssessmentEngine.AssessmentGrade {
    static func fromScore(_ score: Double) -> TamilWritingAssessmentEngine.AssessmentGrade {
        switch score {
        case 0.9...1.0: return .excellent
        case 0.8..<0.9: return .good
        case 0.7..<0.8: return .satisfactory
        case 0.6..<0.7: return .needsImprovement
        default: return .poor
        }
    }
}
