//
//  TamilWritingCurriculumService.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import Foundation
import Combine

@MainActor
class TamilWritingCurriculumService: ObservableObject {
    static let shared = TamilWritingCurriculumService()
    
    @Published var curriculumLevels: [WritingCurriculumLevel] = []
    @Published var userProgress: [String: CurriculumProgress] = [:] // userId -> progress
    @Published var isGeneratingCurriculum = false
    
    private let scriptService = TamilScriptService.shared
    private let supabase = SupabaseService.shared.client
    
    private init() {}
    
    // MARK: - Curriculum Models
    
    struct WritingCurriculumLevel: Identifiable, Codable {
        let id: UUID
        let cefrLevel: CEFRLevel
        let levelName: String
        let description: String
        let prerequisites: [String]
        let learningOutcomes: [LearningOutcome]
        let writingModules: [WritingModule]
        let assessmentCriteria: AssessmentCriteria
        let estimatedHours: Int
        let difficultyProgression: DifficultyProgression
        
        struct LearningOutcome: Identifiable, Codable {
            let id: UUID
            let category: OutcomeCategory
            let description: String
            let measurableGoals: [String]
            let assessmentMethods: [String]
            
            enum OutcomeCategory: String, Codable, CaseIterable {
                case characterMastery = "character_mastery"
                case wordFormation = "word_formation"
                case sentenceConstruction = "sentence_construction"
                case grammarApplication = "grammar_application"
                case culturalExpression = "cultural_expression"
                case communicativeCompetence = "communicative_competence"
                
                var displayName: String {
                    switch self {
                    case .characterMastery: return "Character Mastery"
                    case .wordFormation: return "Word Formation"
                    case .sentenceConstruction: return "Sentence Construction"
                    case .grammarApplication: return "Grammar Application"
                    case .culturalExpression: return "Cultural Expression"
                    case .communicativeCompetence: return "Communicative Competence"
                    }
                }
            }
        }
        
        struct WritingModule: Identifiable, Codable {
            let id: UUID
            let moduleNumber: Int
            let title: String
            let description: String
            let focusAreas: [FocusArea]
            let writingUnits: [WritingUnit]
            let practiceActivities: [PracticeActivity]
            let assessmentTasks: [AssessmentTask]
            let estimatedTime: Int // hours
            
            enum FocusArea: String, Codable, CaseIterable {
                case basicCharacters = "basic_characters"
                case characterCombinations = "character_combinations"
                case wordWriting = "word_writing"
                case sentenceWriting = "sentence_writing"
                case paragraphWriting = "paragraph_writing"
                case creativeWriting = "creative_writing"
                case formalWriting = "formal_writing"
                case culturalWriting = "cultural_writing"
                
                var displayName: String {
                    switch self {
                    case .basicCharacters: return "Basic Characters"
                    case .characterCombinations: return "Character Combinations"
                    case .wordWriting: return "Word Writing"
                    case .sentenceWriting: return "Sentence Writing"
                    case .paragraphWriting: return "Paragraph Writing"
                    case .creativeWriting: return "Creative Writing"
                    case .formalWriting: return "Formal Writing"
                    case .culturalWriting: return "Cultural Writing"
                    }
                }
            }
        }
        
        struct WritingUnit: Identifiable, Codable {
            let id: UUID
            let unitNumber: Int
            let title: String
            let objectives: [String]
            let targetCharacters: [String]
            let targetVocabulary: [String]
            let grammarFocus: [String]
            let culturalElements: [String]
            let writingTasks: [WritingTask]
            let progressionSteps: [ProgressionStep]
            
            struct WritingTask: Identifiable, Codable {
                let id: UUID
                let taskType: TaskType
                let instruction: String
                let targetText: String
                let scaffolding: [String]
                let successCriteria: [String]
                let timeAllocation: Int // minutes
                
                enum TaskType: String, Codable, CaseIterable {
                    case guidedPractice = "guided_practice"
                    case independentPractice = "independent_practice"
                    case creativeApplication = "creative_application"
                    case assessmentTask = "assessment_task"
                }
            }
            
            struct ProgressionStep: Identifiable, Codable {
                let id: UUID
                let stepNumber: Int
                let description: String
                let skillFocus: String
                let supportLevel: SupportLevel
                
                enum SupportLevel: String, Codable, CaseIterable {
                    case fullGuidance = "full_guidance"
                    case partialGuidance = "partial_guidance"
                    case minimalGuidance = "minimal_guidance"
                    case independent = "independent"
                }
            }
        }
        
        struct PracticeActivity: Identifiable, Codable {
            let id: UUID
            let activityType: ActivityType
            let title: String
            let description: String
            let targetSkills: [String]
            let materials: [String]
            let instructions: [String]
            let variations: [String]
            let adaptations: [String]
            
            enum ActivityType: String, Codable, CaseIterable {
                case strokePractice = "stroke_practice"
                case characterFormation = "character_formation"
                case wordBuilding = "word_building"
                case sentenceConstruction = "sentence_construction"
                case dialogueWriting = "dialogue_writing"
                case creativeExpression = "creative_expression"
                case culturalWriting = "cultural_writing"
            }
        }
        
        struct AssessmentTask: Identifiable, Codable {
            let id: UUID
            let assessmentType: AssessmentType
            let title: String
            let description: String
            let criteria: [AssessmentCriterion]
            let rubric: AssessmentRubric
            let timeLimit: Int? // minutes
            
            enum AssessmentType: String, Codable, CaseIterable {
                case formative = "formative"
                case summative = "summative"
                case diagnostic = "diagnostic"
                case portfolio = "portfolio"
            }
            
            struct AssessmentCriterion: Identifiable, Codable {
                let id: UUID
                let criterion: String
                let weight: Double
                let descriptors: [String]
            }
            
            struct AssessmentRubric: Codable {
                let levels: [RubricLevel]
                let scoringGuide: [String]
                
                struct RubricLevel: Identifiable, Codable {
                    let id: UUID
                    let level: String
                    let score: Int
                    let description: String
                    let indicators: [String]
                }
            }
        }
        
        struct AssessmentCriteria: Codable {
            let accuracy: Double // minimum accuracy required
            let fluency: Double // writing speed/fluency
            let complexity: Double // complexity of writing tasks
            let culturalAppropriatenesss: Double // cultural sensitivity
            let communicativeEffectiveness: Double // message clarity
        }
        
        struct DifficultyProgression: Codable {
            let characterComplexity: ComplexityLevel
            let vocabularyRange: VocabularyRange
            let grammarComplexity: GrammarComplexity
            let textLength: TextLength
            let culturalDepth: CulturalDepth
            
            enum ComplexityLevel: String, Codable, CaseIterable {
                case basic = "basic"
                case intermediate = "intermediate"
                case advanced = "advanced"
                case expert = "expert"
            }
            
            struct VocabularyRange: Codable {
                let minWords: Int
                let maxWords: Int
                let thematicAreas: [String]
            }
            
            struct GrammarComplexity: Codable {
                let structures: [String]
                let complexity: ComplexityLevel
                let errorTolerance: Double
            }
            
            struct TextLength: Codable {
                let minCharacters: Int
                let maxCharacters: Int
                let textTypes: [String]
            }
            
            struct CulturalDepth: Codable {
                let level: ComplexityLevel
                let topics: [String]
                let expectations: [String]
            }
        }
    }
    
    struct CurriculumProgress: Codable {
        let userId: String
        let currentLevel: CEFRLevel
        let completedModules: [String]
        let currentModule: String?
        let overallProgress: Double
        let skillProgress: [String: Double] // skill -> progress percentage
        let assessmentScores: [String: Double] // assessment -> score
        let timeSpent: Int // total minutes
        let lastActivity: Date
        let achievements: [String]
        let nextRecommendations: [String]
    }
    
    // MARK: - Curriculum Generation
    
    /// Generate complete writing curriculum for all CEFR levels
    func generateCompleteCurriculum() async {
        isGeneratingCurriculum = true
        defer { isGeneratingCurriculum = false }
        
        var levels: [WritingCurriculumLevel] = []
        
        // Generate curriculum for each CEFR level
        for cefrLevel in [CEFRLevel.a1, .a2, .b1, .b2, .c1, .c2] {
            let level = await generateCurriculumLevel(cefrLevel)
            levels.append(level)
        }
        
        curriculumLevels = levels
        
        // Save to database
        await saveCurriculumLevels(levels)
        
        print("✅ Generated complete Tamil writing curriculum")
    }
    
    /// Generate curriculum for a specific CEFR level
    private func generateCurriculumLevel(_ cefrLevel: CEFRLevel) async -> WritingCurriculumLevel {
        let levelData = getCEFRLevelData(cefrLevel)
        
        let learningOutcomes = generateLearningOutcomes(for: cefrLevel)
        let writingModules = generateWritingModules(for: cefrLevel)
        let assessmentCriteria = generateAssessmentCriteria(for: cefrLevel)
        let difficultyProgression = generateDifficultyProgression(for: cefrLevel)
        
        return WritingCurriculumLevel(
            id: UUID(),
            cefrLevel: cefrLevel,
            levelName: levelData.name,
            description: levelData.description,
            prerequisites: levelData.prerequisites,
            learningOutcomes: learningOutcomes,
            writingModules: writingModules,
            assessmentCriteria: assessmentCriteria,
            estimatedHours: levelData.estimatedHours,
            difficultyProgression: difficultyProgression
        )
    }
    
    // MARK: - CEFR Level Data
    
    private func getCEFRLevelData(_ level: CEFRLevel) -> (name: String, description: String, prerequisites: [String], estimatedHours: Int) {
        switch level {
        case .a1:
            return (
                name: "A1 - Beginner Writing",
                description: "Basic Tamil character formation and simple word writing",
                prerequisites: [],
                estimatedHours: 40
            )
        case .a2:
            return (
                name: "A2 - Elementary Writing",
                description: "Simple sentences and basic communication in writing",
                prerequisites: ["A1 completion"],
                estimatedHours: 60
            )
        case .b1:
            return (
                name: "B1 - Intermediate Writing",
                description: "Connected text and personal expression in writing",
                prerequisites: ["A2 completion"],
                estimatedHours: 80
            )
        case .b2:
            return (
                name: "B2 - Upper Intermediate Writing",
                description: "Complex texts and detailed written communication",
                prerequisites: ["B1 completion"],
                estimatedHours: 100
            )
        case .c1:
            return (
                name: "C1 - Advanced Writing",
                description: "Sophisticated written expression and cultural nuance",
                prerequisites: ["B2 completion"],
                estimatedHours: 120
            )
        case .c2:
            return (
                name: "C2 - Proficient Writing",
                description: "Native-like writing proficiency and literary expression",
                prerequisites: ["C1 completion"],
                estimatedHours: 150
            )
        }
    }
    
    // MARK: - Learning Outcomes Generation
    
    private func generateLearningOutcomes(for level: CEFRLevel) -> [WritingCurriculumLevel.LearningOutcome] {
        switch level {
        case .a1:
            return [
                WritingCurriculumLevel.LearningOutcome(
                    id: UUID(),
                    category: .characterMastery,
                    description: "Master basic Tamil vowels and consonants",
                    measurableGoals: [
                        "Write all 12 Tamil vowels with 90% accuracy",
                        "Write 18 basic consonants with 85% accuracy",
                        "Demonstrate proper stroke order for basic characters"
                    ],
                    assessmentMethods: ["Character formation tests", "Stroke order assessments", "Timed writing exercises"]
                ),
                WritingCurriculumLevel.LearningOutcome(
                    id: UUID(),
                    category: .wordFormation,
                    description: "Form simple Tamil words",
                    measurableGoals: [
                        "Write 50 basic vocabulary words",
                        "Combine consonants and vowels correctly",
                        "Use proper spacing between characters"
                    ],
                    assessmentMethods: ["Word writing tests", "Dictation exercises", "Vocabulary assessments"]
                )
            ]
        case .a2:
            return [
                WritingCurriculumLevel.LearningOutcome(
                    id: UUID(),
                    category: .sentenceConstruction,
                    description: "Write simple Tamil sentences",
                    measurableGoals: [
                        "Construct basic subject-verb-object sentences",
                        "Use present tense correctly in writing",
                        "Write 5-10 word sentences with proper grammar"
                    ],
                    assessmentMethods: ["Sentence completion tasks", "Grammar writing tests", "Short composition"]
                )
            ]
        default:
            return [] // Would implement for other levels
        }
    }
    
    // MARK: - Writing Modules Generation
    
    private func generateWritingModules(for level: CEFRLevel) -> [WritingCurriculumLevel.WritingModule] {
        switch level {
        case .a1:
            return [
                generateA1Module1(), // Basic Characters
                generateA1Module2(), // Character Combinations
                generateA1Module3()  // Simple Words
            ]
        case .a2:
            return [
                generateA2Module1(), // Sentence Writing
                generateA2Module2(), // Basic Communication
                generateA2Module3()  // Personal Information
            ]
        default:
            return [] // Would implement for other levels
        }
    }
    
    private func generateA1Module1() -> WritingCurriculumLevel.WritingModule {
        return WritingCurriculumLevel.WritingModule(
            id: UUID(),
            moduleNumber: 1,
            title: "Basic Tamil Characters",
            description: "Master fundamental Tamil vowels and consonants",
            focusAreas: [.basicCharacters],
            writingUnits: [
                WritingCurriculumLevel.WritingUnit(
                    id: UUID(),
                    unitNumber: 1,
                    title: "Tamil Vowels",
                    objectives: [
                        "Recognize and write all 12 Tamil vowels",
                        "Understand vowel sounds and their written forms",
                        "Practice proper stroke order for vowels"
                    ],
                    targetCharacters: ["அ", "ஆ", "இ", "ஈ", "உ", "ஊ", "எ", "ஏ", "ஐ", "ஒ", "ஓ", "ஔ"],
                    targetVocabulary: [],
                    grammarFocus: ["Vowel sounds", "Character formation"],
                    culturalElements: ["Significance of vowels in Tamil", "Traditional writing methods"],
                    writingTasks: [
                        WritingCurriculumLevel.WritingUnit.WritingTask(
                            id: UUID(),
                            taskType: .guidedPractice,
                            instruction: "Practice writing vowel அ with guided strokes",
                            targetText: "அ",
                            scaffolding: ["Follow stroke order animation", "Use guidance overlay", "Practice 10 times"],
                            successCriteria: ["Correct stroke order", "Proper character formation", "Consistent size"],
                            timeAllocation: 15
                        )
                    ],
                    progressionSteps: [
                        WritingCurriculumLevel.WritingUnit.ProgressionStep(
                            id: UUID(),
                            stepNumber: 1,
                            description: "Trace vowel characters with full guidance",
                            skillFocus: "Character recognition and basic formation",
                            supportLevel: .fullGuidance
                        )
                    ]
                )
            ],
            practiceActivities: [
                WritingCurriculumLevel.PracticeActivity(
                    id: UUID(),
                    activityType: .strokePractice,
                    title: "Vowel Stroke Practice",
                    description: "Practice individual strokes for vowel formation",
                    targetSkills: ["Stroke control", "Character formation", "Muscle memory"],
                    materials: ["Digital canvas", "Stroke guides", "Practice sheets"],
                    instructions: [
                        "Start with simple vowels (அ, இ, உ)",
                        "Follow stroke order carefully",
                        "Practice each vowel 10 times",
                        "Focus on consistency"
                    ],
                    variations: ["Speed variations", "Size variations", "Style practice"],
                    adaptations: ["Larger characters for beginners", "Simplified strokes", "Audio guidance"]
                )
            ],
            assessmentTasks: [
                WritingCurriculumLevel.AssessmentTask(
                    id: UUID(),
                    assessmentType: .formative,
                    title: "Vowel Formation Assessment",
                    description: "Assess ability to write Tamil vowels correctly",
                    criteria: [
                        WritingCurriculumLevel.AssessmentTask.AssessmentCriterion(
                            id: UUID(),
                            criterion: "Stroke Order",
                            weight: 0.4,
                            descriptors: ["Follows correct sequence", "Maintains proper direction"]
                        ),
                        WritingCurriculumLevel.AssessmentTask.AssessmentCriterion(
                            id: UUID(),
                            criterion: "Character Formation",
                            weight: 0.4,
                            descriptors: ["Correct shape", "Proper proportions"]
                        ),
                        WritingCurriculumLevel.AssessmentTask.AssessmentCriterion(
                            id: UUID(),
                            criterion: "Consistency",
                            weight: 0.2,
                            descriptors: ["Uniform size", "Consistent spacing"]
                        )
                    ],
                    rubric: WritingCurriculumLevel.AssessmentTask.AssessmentRubric(
                        levels: [
                            WritingCurriculumLevel.AssessmentTask.AssessmentRubric.RubricLevel(
                                id: UUID(),
                                level: "Excellent",
                                score: 4,
                                description: "Perfect character formation with correct stroke order",
                                indicators: ["All strokes correct", "Perfect proportions", "Consistent execution"]
                            ),
                            WritingCurriculumLevel.AssessmentTask.AssessmentRubric.RubricLevel(
                                id: UUID(),
                                level: "Good",
                                score: 3,
                                description: "Good character formation with minor errors",
                                indicators: ["Most strokes correct", "Good proportions", "Generally consistent"]
                            )
                        ],
                        scoringGuide: ["4 = Excellent", "3 = Good", "2 = Satisfactory", "1 = Needs Improvement"]
                    ),
                    timeLimit: 30
                )
            ],
            estimatedTime: 12
        )
    }
    
    private func generateA1Module2() -> WritingCurriculumLevel.WritingModule {
        // Similar structure for character combinations
        return WritingCurriculumLevel.WritingModule(
            id: UUID(),
            moduleNumber: 2,
            title: "Character Combinations",
            description: "Learn to combine consonants and vowels",
            focusAreas: [.characterCombinations],
            writingUnits: [],
            practiceActivities: [],
            assessmentTasks: [],
            estimatedTime: 15
        )
    }
    
    private func generateA1Module3() -> WritingCurriculumLevel.WritingModule {
        // Similar structure for simple words
        return WritingCurriculumLevel.WritingModule(
            id: UUID(),
            moduleNumber: 3,
            title: "Simple Words",
            description: "Write basic Tamil vocabulary",
            focusAreas: [.wordWriting],
            writingUnits: [],
            practiceActivities: [],
            assessmentTasks: [],
            estimatedTime: 13
        )
    }
    
    private func generateA2Module1() -> WritingCurriculumLevel.WritingModule {
        return WritingCurriculumLevel.WritingModule(
            id: UUID(),
            moduleNumber: 1,
            title: "Sentence Writing",
            description: "Construct basic Tamil sentences",
            focusAreas: [.sentenceWriting],
            writingUnits: [],
            practiceActivities: [],
            assessmentTasks: [],
            estimatedTime: 20
        )
    }
    
    private func generateA2Module2() -> WritingCurriculumLevel.WritingModule {
        return WritingCurriculumLevel.WritingModule(
            id: UUID(),
            moduleNumber: 2,
            title: "Basic Communication",
            description: "Write simple communicative texts",
            focusAreas: [.sentenceWriting],
            writingUnits: [],
            practiceActivities: [],
            assessmentTasks: [],
            estimatedTime: 20
        )
    }
    
    private func generateA2Module3() -> WritingCurriculumLevel.WritingModule {
        return WritingCurriculumLevel.WritingModule(
            id: UUID(),
            moduleNumber: 3,
            title: "Personal Information",
            description: "Write about personal topics",
            focusAreas: [.sentenceWriting],
            writingUnits: [],
            practiceActivities: [],
            assessmentTasks: [],
            estimatedTime: 20
        )
    }
    
    // MARK: - Assessment and Progression
    
    private func generateAssessmentCriteria(for level: CEFRLevel) -> WritingCurriculumLevel.AssessmentCriteria {
        switch level {
        case .a1:
            return WritingCurriculumLevel.AssessmentCriteria(
                accuracy: 80.0,
                fluency: 60.0,
                complexity: 40.0,
                culturalAppropriatenesss: 70.0,
                communicativeEffectiveness: 70.0
            )
        case .a2:
            return WritingCurriculumLevel.AssessmentCriteria(
                accuracy: 85.0,
                fluency: 70.0,
                complexity: 60.0,
                culturalAppropriatenesss: 75.0,
                communicativeEffectiveness: 80.0
            )
        default:
            return WritingCurriculumLevel.AssessmentCriteria(
                accuracy: 90.0,
                fluency: 80.0,
                complexity: 80.0,
                culturalAppropriatenesss: 85.0,
                communicativeEffectiveness: 90.0
            )
        }
    }
    
    private func generateDifficultyProgression(for level: CEFRLevel) -> WritingCurriculumLevel.DifficultyProgression {
        switch level {
        case .a1:
            return WritingCurriculumLevel.DifficultyProgression(
                characterComplexity: .basic,
                vocabularyRange: WritingCurriculumLevel.DifficultyProgression.VocabularyRange(
                    minWords: 50,
                    maxWords: 150,
                    thematicAreas: ["Family", "Greetings", "Numbers", "Colors", "Food"]
                ),
                grammarComplexity: WritingCurriculumLevel.DifficultyProgression.GrammarComplexity(
                    structures: ["Simple sentences", "Present tense", "Basic pronouns"],
                    complexity: .basic,
                    errorTolerance: 0.8
                ),
                textLength: WritingCurriculumLevel.DifficultyProgression.TextLength(
                    minCharacters: 5,
                    maxCharacters: 50,
                    textTypes: ["Single words", "Simple phrases", "Basic sentences"]
                ),
                culturalDepth: WritingCurriculumLevel.DifficultyProgression.CulturalDepth(
                    level: .basic,
                    topics: ["Basic greetings", "Family relationships", "Daily activities"],
                    expectations: ["Appropriate formality", "Basic politeness"]
                )
            )
        default:
            return WritingCurriculumLevel.DifficultyProgression(
                characterComplexity: .intermediate,
                vocabularyRange: WritingCurriculumLevel.DifficultyProgression.VocabularyRange(
                    minWords: 200,
                    maxWords: 500,
                    thematicAreas: ["Education", "Work", "Travel", "Culture", "Technology"]
                ),
                grammarComplexity: WritingCurriculumLevel.DifficultyProgression.GrammarComplexity(
                    structures: ["Complex sentences", "Multiple tenses", "Case markers"],
                    complexity: .intermediate,
                    errorTolerance: 0.85
                ),
                textLength: WritingCurriculumLevel.DifficultyProgression.TextLength(
                    minCharacters: 100,
                    maxCharacters: 300,
                    textTypes: ["Paragraphs", "Short compositions", "Dialogues"]
                ),
                culturalDepth: WritingCurriculumLevel.DifficultyProgression.CulturalDepth(
                    level: .intermediate,
                    topics: ["Cultural practices", "Social norms", "Regional variations"],
                    expectations: ["Cultural sensitivity", "Appropriate register"]
                )
            )
        }
    }
    
    // MARK: - Database Operations
    
    private func saveCurriculumLevels(_ levels: [WritingCurriculumLevel]) async {
        // Save curriculum to database
        for level in levels {
            print("Saving curriculum level: \(level.levelName)")
        }
    }
    
    // MARK: - Progress Tracking
    
    /// Update user progress in the curriculum
    func updateUserProgress(userId: String, moduleId: String, progress: Double) async {
        // Update progress tracking
        if var currentProgress = userProgress[userId] {
            currentProgress.skillProgress[moduleId] = progress
            currentProgress.lastActivity = Date()
            userProgress[userId] = currentProgress
        }
    }
    
    /// Get recommended next activities for user
    func getRecommendedActivities(for userId: String) -> [String] {
        guard let progress = userProgress[userId] else {
            return ["Start with A1 Basic Characters"]
        }
        
        // Generate recommendations based on progress
        return progress.nextRecommendations
    }
    
    /// Get curriculum level for CEFR level
    func getCurriculumLevel(for cefrLevel: CEFRLevel) -> WritingCurriculumLevel? {
        return curriculumLevels.first { $0.cefrLevel == cefrLevel }
    }
}
