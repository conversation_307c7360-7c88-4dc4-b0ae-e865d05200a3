//
//  AgentsView.swift
//  NIRA
//
//  Enhanced AI Agents Dashboard with Modern UX
//  Based on Language Learning Content Development Standards v2.0
//

import SwiftUI

struct AgentsView: View {
    @ObservedObject var userPreferences = UserPreferencesService.shared
    
    // Core 3 agents for each language
    private var coreAgents: [Agent] {
        [
            Agent(
                id: "conversational_\(userPreferences.selectedLanguage.rawValue)",
                name: "<PERSON>",
                description: "Friendly \(userPreferences.selectedLanguage.displayName) teacher focused on conversation",
                expertise: ["Conversation", "Pronunciation", "Vocabulary"],
                rating: 4.9,
                isOnline: true,
                profileImageName: "agent_conversational",
                emoji: "👩‍🏫"
            ),
            Agent(
                id: "academic_\(userPreferences.selectedLanguage.rawValue)",
                name: "<PERSON>",
                description: "Academic expert in \(userPreferences.selectedLanguage.displayName) grammar and literature",
                expertise: ["Grammar", "Literature", "Formal Writing"],
                rating: 4.8,
                isOnline: true,
                profileImageName: "agent_academic",
                emoji: "👨‍🏫"
            ),
            Agent(
                id: "cultural_\(userPreferences.selectedLanguage.rawValue)",
                name: "<PERSON>",
                description: "Cultural ambassador sharing \(userPreferences.selectedLanguage.displayName) traditions",
                expertise: ["Culture", "History", "Traditions"],
                rating: 4.9,
                isOnline: false,
                profileImageName: "agent_cultural",
                emoji: "👩‍🎨"
            )
        ]
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Modern Header
            modernHeaderView
            
            // Main Content
            ScrollView {
                VStack(spacing: 24) {
                    // Hero Section
                    heroSectionView
                    
                    // Agents Grid
                    agentsGridView
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 100)
            }
            .background(
                LinearGradient(
                    colors: [
                        Color(.systemBackground),
                        Color(.systemGray6).opacity(0.3)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
        }
        .navigationBarHidden(true)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Header View
    private var modernHeaderView: some View {
        VStack(spacing: 0) {
            HStack {
                // Title Section
                VStack(alignment: .leading, spacing: 4) {
                    Text("Live Assist")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("AI Language Assistants")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                
                // Header Actions
                HStack(spacing: 12) {
                    // Tamil language indicator
                    HStack(spacing: 8) {
                        Text("🇮🇳")
                            .font(.title2)
                        Text("Tamil")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }

                    Spacer()

                    // Compact Theme Toggle
                    CompactThemeToggle()
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 12)
            .padding(.bottom, 20)
            
            // Divider
            Divider()
                .background(Color(.systemGray5))
        }
        .background(Color(.systemBackground))
    }
    
    // MARK: - Hero Section
    private var heroSectionView: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "brain.head.profile.fill")
                    .font(.title)
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Language Learning Assistants")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text("Connect with AI assistants to practice and improve your Tamil skills")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
            }
            .padding(.vertical, 20)
            .padding(.horizontal, 24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.blue.opacity(0.1),
                                Color.purple.opacity(0.05)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.blue.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .padding(.top, 20)
    }
    
    // MARK: - Agents Grid
    private var agentsGridView: some View {
        VStack(spacing: 20) {
            HStack {
                Text("Choose Your Assistant")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text("3 Specialized Agents")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color(.systemGray6))
                    .clipShape(Capsule())
            }
            
            // Agent Cards
            VStack(spacing: 16) {
                                 ForEach(Array(coreAgents.enumerated()), id: \.1.id) { index, agent in
                     ModernAgentCardView(
                         agent: agent,
                         isPrimary: index == 0
                     )
                }
            }
        }
    }
}

// MARK: - Modern Agent Card View
struct ModernAgentCardView: View {
    let agent: Agent
    let isPrimary: Bool
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            // TODO: Navigate to chat with agent
        }) {
            VStack(spacing: 0) {
                // Agent Header
                HStack(spacing: 16) {
                    // Profile Section
                    ZStack {
                        Circle()
                            .fill(
                                isPrimary ?
                                LinearGradient(
                                    colors: [Color.blue.opacity(0.3), Color.blue],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ) :
                                LinearGradient(
                                    colors: [Color(.systemGray5), Color(.systemGray4)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 60, height: 60)
                            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                        
                        Text(agent.emoji)
                            .font(.title)
                        
                        // Online Status
                        if agent.isOnline {
                            Circle()
                                .fill(Color.green)
                                .frame(width: 16, height: 16)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white, lineWidth: 2)
                                )
                                .offset(x: 20, y: 20)
                        }
                    }
                    
                    // Agent Info
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text(agent.name)
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            // Rating
                            HStack(spacing: 4) {
                                Image(systemName: "star.fill")
                                    .font(.caption)
                                    .foregroundColor(.yellow)
                                
                                Text("\(agent.rating, specifier: "%.1f")")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Text(agent.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.leading)
                            .lineLimit(2)
                        
                        // Expertise Tags
                        HStack {
                            ForEach(agent.expertise.prefix(3), id: \.self) { skill in
                                Text(skill)
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.blue)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.blue.opacity(0.1))
                                    .clipShape(Capsule())
                            }
                            Spacer()
                        }
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 20)
                
                // Action Section
                HStack {
                    Spacer()
                    
                    HStack(spacing: 8) {
                        if agent.isOnline {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.caption)
                                .foregroundColor(.green)
                        }
                        
                        Text(agent.isOnline ? "Start Chat" : "Offline")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(agent.isOnline ? .blue : .secondary)
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        agent.isOnline ?
                        Color.blue.opacity(0.1) :
                        Color(.systemGray6)
                    )
                    .clipShape(Capsule())
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
            }
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        isPrimary ?
                        LinearGradient(
                            colors: [
                                Color(.systemBackground),
                                Color.blue.opacity(0.05)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ) :
                        LinearGradient(
                            colors: [
                                Color(.systemBackground),
                                Color(.systemGray6).opacity(0.3)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(
                        color: Color.black.opacity(isPressed ? 0.15 : (isPrimary ? 0.12 : 0.08)),
                        radius: isPressed ? 6 : (isPrimary ? 12 : 10),
                        x: 0,
                        y: isPressed ? 2 : (isPrimary ? 4 : 3)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(
                                isPrimary ?
                                LinearGradient(
                                    colors: [Color.blue.opacity(0.3), Color.blue.opacity(0.1)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ) :
                                LinearGradient(
                                    colors: [Color.white.opacity(0.2), Color.clear],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: isPrimary ? 1.5 : 1
                            )
                    )
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.15), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .disabled(!agent.isOnline)
    }
}

// MARK: - Agent Model
struct Agent {
    let id: String
    let name: String
    let description: String
    let expertise: [String]
    let rating: Double
    let isOnline: Bool
    let profileImageName: String
    let emoji: String
}

#Preview {
    AgentsView()
}
