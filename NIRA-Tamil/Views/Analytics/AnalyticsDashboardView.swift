//
//  AnalyticsDashboardView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import SwiftUI
#if canImport(Charts)
import Charts
#endif

struct AnalyticsDashboardView: View {
    @StateObject private var analyticsService = TamilWritingAnalyticsService.shared
    @State private var selectedTimeRange: TamilWritingAnalyticsService.TimeRange = .week
    @State private var selectedMetric: TamilWritingAnalyticsService.AnalyticsMetric = .accuracy
    @State private var showingDetailedView = false
    @State private var selectedAnalyticsSection: AnalyticsSection = .overview
    
    enum AnalyticsSection: String, CaseIterable {
        case overview = "Overview"
        case progress = "Progress"
        case performance = "Performance"
        case insights = "Insights"
        case mastery = "Mastery"
        case patterns = "Patterns"
        case achievements = "Achievements"
        
        var icon: String {
            switch self {
            case .overview: return "chart.pie"
            case .progress: return "chart.line.uptrend.xyaxis"
            case .performance: return "speedometer"
            case .insights: return "brain.head.profile"
            case .mastery: return "star.circle"
            case .patterns: return "clock"
            case .achievements: return "trophy"
            }
        }
        
        var color: Color {
            switch self {
            case .overview: return .blue
            case .progress: return .green
            case .performance: return .orange
            case .insights: return .purple
            case .mastery: return .yellow
            case .patterns: return .red
            case .achievements: return .pink
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with time range selector
                analyticsHeader
                
                // Section selector
                sectionSelector
                
                // Main content
                if analyticsService.isLoadingAnalytics {
                    loadingView
                } else if let analytics = analyticsService.analyticsData {
                    analyticsContentView(analytics)
                } else {
                    emptyStateView
                }
            }
            .background(
                LinearGradient(
                    colors: [Color.blue.opacity(0.03), Color.purple.opacity(0.03)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .navigationBarHidden(true)
            .onAppear {
                loadAnalytics()
            }
        }
        .sheet(isPresented: $showingDetailedView) {
            AnalyticsDetailedView(
                analytics: analyticsService.analyticsData,
                section: selectedAnalyticsSection
            )
        }
    }
    
    private var analyticsHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Analytics Dashboard")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Track your Tamil writing progress")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(spacing: 4) {
                    Image(systemName: "chart.bar.fill")
                        .font(.title2)
                        .foregroundColor(.blue)
                    
                    Text("Analytics")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                }
            }
            
            // Time range selector
            TimeRangeSelector(
                selectedRange: $selectedTimeRange,
                onRangeChanged: { range in
                    selectedTimeRange = range
                    loadAnalytics()
                }
            )
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private var sectionSelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(AnalyticsSection.allCases, id: \.self) { section in
                    SectionChip(
                        section: section,
                        isSelected: selectedAnalyticsSection == section,
                        action: { selectedAnalyticsSection = section }
                    )
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
    }
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Analyzing Your Progress...")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text("Generating comprehensive analytics from your writing data")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "chart.bar")
                .font(.system(size: 80))
                .foregroundColor(.blue.opacity(0.6))
            
            VStack(spacing: 8) {
                Text("No Analytics Data")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Start practicing Tamil writing to see your progress analytics")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button("Start Writing Practice") {
                // Navigate to writing practice
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
    
    private func analyticsContentView(_ analytics: TamilWritingAnalyticsService.AnalyticsData) -> some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                switch selectedAnalyticsSection {
                case .overview:
                    OverviewSection(analytics: analytics)
                case .progress:
                    ProgressSection(analytics: analytics)
                case .performance:
                    PerformanceSection(analytics: analytics)
                case .insights:
                    InsightsSection(analytics: analytics)
                case .mastery:
                    MasterySection(analytics: analytics)
                case .patterns:
                    PatternsSection(analytics: analytics)
                case .achievements:
                    AchievementsSection(analytics: analytics)
                }
            }
            .padding()
        }
    }
    
    private func loadAnalytics() {
        Task {
            await analyticsService.generateAnalytics(
                for: selectedTimeRange,
                userId: "current-user"
            )
        }
    }
}

// MARK: - Time Range Selector

struct TimeRangeSelector: View {
    @Binding var selectedRange: TamilWritingAnalyticsService.TimeRange
    let onRangeChanged: (TamilWritingAnalyticsService.TimeRange) -> Void
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(TamilWritingAnalyticsService.TimeRange.allCases, id: \.self) { range in
                    Button(action: {
                        selectedRange = range
                        onRangeChanged(range)
                    }) {
                        Text(range.rawValue)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(selectedRange == range ? .white : .primary)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(selectedRange == range ? Color.blue : Color(.tertiarySystemBackground))
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 4)
        }
    }
}

// MARK: - Section Chip

struct SectionChip: View {
    let section: AnalyticsDashboardView.AnalyticsSection
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: section.icon)
                    .font(.caption)
                
                Text(section.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : section.color)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? section.color : section.color.opacity(0.1))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Overview Section

struct OverviewSection: View {
    let analytics: TamilWritingAnalyticsService.AnalyticsData
    
    var body: some View {
        VStack(spacing: 16) {
            // Key metrics grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                AnalyticsMetricCard(
                    title: "Practice Time",
                    value: "\(analytics.overviewMetrics.totalPracticeTime)",
                    unit: "minutes",
                    icon: "clock.fill",
                    color: .orange,
                    trend: analytics.overviewMetrics.improvementRate > 0 ? .up : .stable
                )

                AnalyticsMetricCard(
                    title: "Characters Learned",
                    value: "\(analytics.overviewMetrics.charactersLearned)",
                    unit: "characters",
                    icon: "textformat.abc",
                    color: .green,
                    trend: .up
                )

                AnalyticsMetricCard(
                    title: "Average Accuracy",
                    value: "\(Int(analytics.overviewMetrics.averageAccuracy * 100))",
                    unit: "%",
                    icon: "target",
                    color: .blue,
                    trend: analytics.overviewMetrics.improvementRate > 0 ? .up : .stable
                )

                AnalyticsMetricCard(
                    title: "Current Streak",
                    value: "\(analytics.overviewMetrics.currentStreak)",
                    unit: "days",
                    icon: "flame.fill",
                    color: .red,
                    trend: .up
                )
            }
            
            // Weekly goal progress
            WeeklyGoalCard(progress: analytics.overviewMetrics.weeklyGoalProgress)
            
            // Quick insights
            QuickInsightsOverviewCard(analytics: analytics)
        }
    }
}

// MARK: - Metric Card
// Using AnalyticsMetricCard to avoid conflicts with AdaptiveDashboardView.MetricCard

struct AnalyticsMetricCard: View {
    let title: String
    let value: String
    let unit: String
    let icon: String
    let color: Color
    let trend: AnalyticsTrendDirection

    enum AnalyticsTrendDirection {
        case up, down, stable

        var icon: String {
            switch self {
            case .up: return "arrow.up.right"
            case .down: return "arrow.down.right"
            case .stable: return "arrow.right"
            }
        }

        var color: Color {
            switch self {
            case .up: return .green
            case .down: return .red
            case .stable: return .gray
            }
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)

                Spacer()

                Image(systemName: trend.icon)
                    .font(.caption)
                    .foregroundColor(trend.color)
            }

            VStack(alignment: .leading, spacing: 2) {
                HStack(alignment: .firstTextBaseline, spacing: 4) {
                    Text(value)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text(unit)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Weekly Goal Card

struct WeeklyGoalCard: View {
    let progress: Double
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Weekly Goal Progress")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(Int(progress * 100))%")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.blue)
            }
            
            ProgressView(value: progress, total: 1.0)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                .frame(height: 8)
            
            Text("Great progress! You're \(Int(progress * 100))% towards your weekly goal.")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.blue.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.blue.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - Quick Insights Overview Card

struct QuickInsightsOverviewCard: View {
    let analytics: TamilWritingAnalyticsService.AnalyticsData
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Insights")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                AnalyticsInsightRow(
                    icon: "brain.head.profile",
                    text: "Your learning velocity is \(analytics.learningInsights.learningVelocity, specifier: "%.1f") characters per week",
                    color: .purple
                )

                AnalyticsInsightRow(
                    icon: "clock.arrow.circlepath",
                    text: "Optimal practice time: \(analytics.learningInsights.optimalPracticeTime) minutes",
                    color: .orange
                )

                AnalyticsInsightRow(
                    icon: "chart.line.uptrend.xyaxis",
                    text: "Retention rate: \(Int(analytics.learningInsights.retentionRate * 100))%",
                    color: .green
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

struct AnalyticsInsightRow: View {
    let icon: String
    let text: String
    let color: Color

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(color)
                .frame(width: 16)

            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()
        }
    }
}

// MARK: - Progress Section

struct ProgressSection: View {
    let analytics: TamilWritingAnalyticsService.AnalyticsData
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Progress Tracking")
                .font(.title2)
                .fontWeight(.bold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Progress charts would go here
            Text("Progress charts coming soon...")
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, maxHeight: 200)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
                )
        }
    }
}

// MARK: - Performance Section

struct PerformanceSection: View {
    let analytics: TamilWritingAnalyticsService.AnalyticsData
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Performance Analytics")
                .font(.title2)
                .fontWeight(.bold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Performance charts would go here
            Text("Performance analytics coming soon...")
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, maxHeight: 200)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
                )
        }
    }
}

// MARK: - Insights Section

struct InsightsSection: View {
    let analytics: TamilWritingAnalyticsService.AnalyticsData
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Learning Insights")
                .font(.title2)
                .fontWeight(.bold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Insights content would go here
            Text("AI-powered insights coming soon...")
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, maxHeight: 200)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
                )
        }
    }
}

// MARK: - Mastery Section

struct MasterySection: View {
    let analytics: TamilWritingAnalyticsService.AnalyticsData
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Character Mastery")
                .font(.title2)
                .fontWeight(.bold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Mastery content would go here
            Text("Character mastery tracking coming soon...")
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, maxHeight: 200)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
                )
        }
    }
}

// MARK: - Patterns Section

struct PatternsSection: View {
    let analytics: TamilWritingAnalyticsService.AnalyticsData
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Practice Patterns")
                .font(.title2)
                .fontWeight(.bold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Patterns content would go here
            Text("Practice pattern analysis coming soon...")
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, maxHeight: 200)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
                )
        }
    }
}

// MARK: - Achievements Section

struct AchievementsSection: View {
    let analytics: TamilWritingAnalyticsService.AnalyticsData
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Achievements & Milestones")
                .font(.title2)
                .fontWeight(.bold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Achievements content would go here
            Text("Achievement system coming soon...")
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, maxHeight: 200)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
                )
        }
    }
}

// MARK: - Detailed Analytics View

struct AnalyticsDetailedView: View {
    let analytics: TamilWritingAnalyticsService.AnalyticsData?
    let section: AnalyticsDashboardView.AnalyticsSection
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("Detailed \(section.rawValue) Analytics")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.top)

                    // Detailed analytics content would go here
                    Text("Detailed analytics for \(section.rawValue.lowercased()) coming soon...")
                        .foregroundColor(.secondary)
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    AnalyticsDashboardView()
}
