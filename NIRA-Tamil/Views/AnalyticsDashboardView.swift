//
//  AnalyticsDashboardView.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2024-12-19.
//

import SwiftUI

struct AnalyticsDashboardView: View {
    @StateObject private var analyticsService = AnalyticsService.shared
    @State private var showingPrivacySettings = false
    @State private var showingDataExport = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Image(systemName: "chart.bar.doc.horizontal")
                            .font(.system(size: 50))
                            .foregroundColor(.niraPrimary)
                        
                        Text("Learning Analytics")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("Insights into your Tamil learning journey")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                    
                    // Privacy Toggle
                    PrivacyToggleCard(
                        isEnabled: analyticsService.isTrackingEnabled,
                        onToggle: { enabled in
                            analyticsService.setTrackingEnabled(enabled)
                        }
                    )
                    
                    if analyticsService.isTrackingEnabled {
                        // Analytics Overview
                        if let analyticsData = analyticsService.analyticsData {
                            AnalyticsOverviewCard(data: analyticsData)
                        } else {
                            EmptyAnalyticsCard {
                                Task {
                                    await analyticsService.generateAnalyticsData()
                                }
                            }
                        }
                        
                        // Engagement Metrics
                        if let engagement = analyticsService.engagementMetrics {
                            EngagementMetricsCard(metrics: engagement)
                        }
                        
                        // Learning Patterns
                        if !analyticsService.learningPatterns.isEmpty {
                            LearningPatternsSection(patterns: analyticsService.learningPatterns)
                        }
                        
                        // Quick Actions
                        QuickActionsSection(
                            onRefresh: {
                                Task {
                                    await analyticsService.generateAnalyticsData()
                                }
                            },
                            onPrivacySettings: {
                                showingPrivacySettings = true
                            },
                            onExportData: {
                                showingDataExport = true
                            }
                        )
                    } else {
                        // Privacy Message
                        PrivacyMessageCard()
                    }
                    
                    Spacer(minLength: 50)
                }
            }
            .navigationTitle("Analytics")
            .navigationBarTitleDisplayMode(.inline)
            .refreshable {
                if analyticsService.isTrackingEnabled {
                    await analyticsService.generateAnalyticsData()
                }
            }
        }
        .sheet(isPresented: $showingPrivacySettings) {
            PrivacySettingsView()
        }
        .sheet(isPresented: $showingDataExport) {
            DataExportView()
        }
        .task {
            if analyticsService.isTrackingEnabled && analyticsService.analyticsData == nil {
                await analyticsService.generateAnalyticsData()
            }
        }
    }
}

struct PrivacyToggleCard: View {
    let isEnabled: Bool
    let onToggle: (Bool) -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Analytics Tracking")
                        .font(.headline)
                    
                    Text("Help improve the app with anonymous usage data")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Toggle("", isOn: Binding(
                    get: { isEnabled },
                    set: { onToggle($0) }
                ))
                .toggleStyle(SwitchToggleStyle(tint: .niraPrimary))
            }
            
            if isEnabled {
                HStack {
                    Image(systemName: "checkmark.shield.fill")
                        .foregroundColor(.green)
                    
                    Text("Privacy-compliant data collection enabled")
                        .font(.caption)
                        .foregroundColor(.green)
                    
                    Spacer()
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .padding(.horizontal)
    }
}

struct AnalyticsOverviewCard: View {
    let data: AnalyticsData
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Learning Overview")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                AnalyticsStatCard(
                    title: "Total Events",
                    value: "\(data.totalEvents)",
                    icon: "chart.bar.fill",
                    color: .blue
                )

                AnalyticsStatCard(
                    title: "Sessions",
                    value: "\(data.sessionsCount)",
                    icon: "clock.fill",
                    color: .green
                )

                AnalyticsStatCard(
                    title: "Study Time",
                    value: formatDuration(data.totalStudyTime),
                    icon: "timer.fill",
                    color: .orange
                )

                AnalyticsStatCard(
                    title: "Avg Session",
                    value: formatDuration(data.averageSessionDuration),
                    icon: "gauge.medium.fill",
                    color: .purple
                )
            }
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Most Active Hour")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Text("\(data.mostActiveHour):00")
                        .font(.subheadline)
                        .foregroundColor(.niraPrimary)
                }
                
                HStack {
                    Text("Preferred Mode")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Text(data.preferredLearningMode)
                        .font(.subheadline)
                        .foregroundColor(.niraPrimary)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .padding(.horizontal)
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60
        
        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

struct EmptyAnalyticsCard: View {
    let onGenerate: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "chart.bar.doc.horizontal")
                .font(.system(size: 40))
                .foregroundColor(.secondary)
            
            Text("No Analytics Data")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text("Start using the app to generate insights")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Generate Analytics", action: onGenerate)
                .buttonStyle(.borderedProminent)
                .tint(.niraPrimary)
        }
        .padding(40)
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .padding(.horizontal)
    }
}

struct EngagementMetricsCard: View {
    let metrics: EngagementMetrics
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Engagement Metrics")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                MetricRow(
                    label: "Retention Rate",
                    value: "\(Int(metrics.retentionRate * 100))%",
                    progress: metrics.retentionRate
                )
                
                MetricRow(
                    label: "Completion Rate",
                    value: "\(Int(metrics.completionRate * 100))%",
                    progress: metrics.completionRate
                )
                
                MetricRow(
                    label: "Events per Session",
                    value: String(format: "%.1f", metrics.eventsPerSession),
                    progress: min(metrics.eventsPerSession / 20.0, 1.0)
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .padding(.horizontal)
    }
}

struct LearningPatternsSection: View {
    let patterns: [LearningPatternData]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Learning Patterns")
                .font(.headline)
                .padding(.horizontal)
            
            ForEach(patterns) { pattern in
                LearningPatternCard(pattern: pattern)
            }
            .padding(.horizontal)
        }
    }
}

struct LearningPatternCard: View {
    let pattern: LearningPatternData
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(pattern.type.capitalized.replacingOccurrences(of: "_", with: " "))
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(Int(pattern.confidence * 100))% confidence")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Text(pattern.description)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            if !pattern.recommendations.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Recommendations:")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.niraPrimary)
                    
                    ForEach(pattern.recommendations, id: \.self) { recommendation in
                        Text("• \(recommendation)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct QuickActionsSection: View {
    let onRefresh: () -> Void
    let onPrivacySettings: () -> Void
    let onExportData: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Actions")
                .font(.headline)
                .padding(.horizontal)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                AnalyticsActionButton(
                    title: "Refresh",
                    icon: "arrow.clockwise",
                    color: .blue,
                    action: onRefresh
                )

                AnalyticsActionButton(
                    title: "Privacy",
                    icon: "shield.fill",
                    color: .green,
                    action: onPrivacySettings
                )

                AnalyticsActionButton(
                    title: "Export",
                    icon: "square.and.arrow.up",
                    color: .orange,
                    action: onExportData
                )
            }
            .padding(.horizontal)
        }
    }
}

struct PrivacyMessageCard: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "hand.raised.fill")
                .font(.system(size: 40))
                .foregroundColor(.niraPrimary)
            
            Text("Analytics Disabled")
                .font(.headline)
            
            Text("Analytics tracking is currently disabled. Enable it to see insights about your learning progress.")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Text("Your privacy is important to us. All data is collected anonymously and used only to improve your learning experience.")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(40)
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .padding(.horizontal)
    }
}

struct AnalyticsStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color.white.opacity(0.5))
        .cornerRadius(8)
    }
}

struct MetricRow: View {
    let label: String
    let value: String
    let progress: Double
    
    var body: some View {
        VStack(spacing: 4) {
            HStack {
                Text(label)
                    .font(.subheadline)
                
                Spacer()
                
                Text(value)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.niraPrimary)
            }
            
            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: .niraPrimary))
        }
    }
}

struct AnalyticsActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
    }
}

struct PrivacySettingsView: View {
    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Privacy Settings")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("We respect your privacy and give you full control over your data.")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                // Privacy information would go here
                
                Spacer()
            }
            .padding()
            .navigationBarItems(trailing: Button("Done") { dismiss() })
        }
    }
}

struct DataExportView: View {
    @Environment(\.dismiss) var dismiss
    @State private var isExporting = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Export Your Data")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Download all your analytics data in JSON format.")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Button(action: {
                    Task {
                        isExporting = true
                        let _ = await AnalyticsService.shared.exportUserData()
                        // Handle data export
                        isExporting = false
                    }
                }) {
                    HStack {
                        if isExporting {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "square.and.arrow.down")
                        }
                        
                        Text(isExporting ? "Exporting..." : "Export Data")
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.niraPrimary)
                    .cornerRadius(8)
                }
                .disabled(isExporting)
                
                Spacer()
            }
            .padding()
            .navigationBarItems(trailing: Button("Done") { dismiss() })
        }
    }
}

#Preview {
    AnalyticsDashboardView()
}
