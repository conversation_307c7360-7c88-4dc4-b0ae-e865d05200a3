//
//  AuthenticationView.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import SwiftUI

// MARK: - Premium Authentication View

struct AuthenticationView: View {
    @StateObject private var authService = AuthenticationService.shared
    @State private var animateElements = false
    @State private var showSignUp = false
    @State private var showPasswordReset = false
    @State private var email = ""
    @State private var password = ""
    @State private var showPassword = false
    @State private var currentFeatureIndex = 0
    @FocusState private var emailFocused: Bool
    @FocusState private var passwordFocused: Bool

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Premium European-inspired Background
                LinearGradient(
                    colors: Color.authPrimaryGradient,
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                .overlay(
                    // Subtle geometric patterns
                    ForEach(0..<8, id: \.self) { index in
                        Circle()
                            .fill(Color.white.opacity(0.05))
                            .frame(width: CGFloat.random(in: 100...200))
                            .position(
                                x: CGFloat.random(in: 0...geometry.size.width),
                                y: CGFloat.random(in: 0...geometry.size.height)
                            )
                            .scaleEffect(animateElements ? 1.1 : 0.9)
                            .animation(
                                .easeInOut(duration: Double.random(in: 4...8))
                                .repeatForever(autoreverses: true)
                                .delay(Double(index) * 0.5),
                                value: animateElements
                            )
                    }
                )

                ScrollView {
                    VStack(spacing: Spacing.xxxl) {
                        Spacer(minLength: Spacing.xxxl)

                        // Premium Brand Header
                        VStack(spacing: Spacing.xl) {
                            // Elegant Logo
                            ZStack {
                                // Outer ring with gradient
                                Circle()
                                    .stroke(
                                        LinearGradient(
                                            colors: [Color.white.opacity(0.4), Color.white.opacity(0.1)],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ),
                                        lineWidth: 3
                                    )
                                    .frame(width: 140, height: 140)

                                // Inner circle with glassmorphism
                                Circle()
                                    .fill(.ultraThinMaterial)
                                    .frame(width: 120, height: 120)
                                    .overlay(
                                        Circle()
                                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                                    )

                                // Modern logo symbol
                                VStack(spacing: 4) {
                                    Text("N")
                                        .font(.displayMedium)
                                        .fontWeight(.light)
                                        .foregroundColor(.white)

                                    Rectangle()
                                        .fill(Color.white.opacity(0.8))
                                        .frame(width: 30, height: 2)
                                        .cornerRadius(1)
                                }
                                .scaleEffect(animateElements ? 1.05 : 1.0)
                                .animation(.easeInOut(duration: 3).repeatForever(autoreverses: true), value: animateElements)
                            }

                            // Premium Typography
                            VStack(spacing: Spacing.md) {
                                Text("Welcome to NIRA")
                                    .font(.displaySmall)
                                    .fontWeight(.light)
                                    .foregroundColor(.white)
                                    .opacity(animateElements ? 1.0 : 0.0)
                                    .offset(y: animateElements ? 0 : 20)
                                    .animation(.easeOut(duration: 1.2).delay(0.3), value: animateElements)

                                Text("Master languages through intelligent conversations")
                                    .font(.titleLarge)
                                    .fontWeight(.regular)
                                    .foregroundColor(.white.opacity(0.9))
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal, Spacing.xl)
                                    .opacity(animateElements ? 1.0 : 0.0)
                                    .offset(y: animateElements ? 0 : 20)
                                    .animation(.easeOut(duration: 1.2).delay(0.6), value: animateElements)
                            }
                        }

                        // Premium Feature Showcase
                        PremiumCard {
                            VStack(spacing: Spacing.lg) {
                                Text("Why choose NIRA?")
                                    .font(.headlineSmall)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.authPrimaryGradient[0])

                                VStack(spacing: Spacing.md) {
                                    PremiumFeatureRow(
                                        icon: "brain.head.profile",
                                        title: "AI-Powered Learning",
                                        description: "Personalized tutoring that adapts to your pace"
                                    )

                                    PremiumFeatureRow(
                                        icon: "globe.europe.africa.fill",
                                        title: "50 Languages",
                                        description: "From major world languages to rare dialects"
                                    )

                                    PremiumFeatureRow(
                                        icon: "chart.line.uptrend.xyaxis",
                                        title: "Smart Analytics",
                                        description: "Track progress with detailed insights"
                                    )
                                }
                            }
                        }
                        .padding(.horizontal, Spacing.xl)
                        .opacity(animateElements ? 1.0 : 0.0)
                        .offset(y: animateElements ? 0 : 30)
                        .animation(.easeOut(duration: 1.2).delay(0.9), value: animateElements)

                        // Premium Authentication Section
                        VStack(spacing: Spacing.xl) {
                            // Sign In Form (if not showing sign up)
                            if !showSignUp {
                                premiumSignInForm
                            }

                            // Premium Action Buttons
                            VStack(spacing: Spacing.lg) {
                                if !showSignUp {
                                    Button("Sign In") {
                                        Task {
                                            await signIn()
                                        }
                                    }
                                    .buttonStyle(PremiumPrimaryButtonStyle())
                                    .disabled(email.isEmpty || password.isEmpty || authService.isLoading)
                                    .opacity(animateElements ? 1.0 : 0.0)
                                    .offset(y: animateElements ? 0 : 20)
                                    .animation(.easeOut(duration: 1.2).delay(1.2), value: animateElements)
                                }

                                Button(showSignUp ? "Already have an account? Sign In" : "New to NIRA? Create Account") {
                                    withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                        showSignUp.toggle()
                                        clearForm()
                                    }
                                }
                                .buttonStyle(PremiumSecondaryButtonStyle())
                                .opacity(animateElements ? 1.0 : 0.0)
                                .offset(y: animateElements ? 0 : 20)
                                .animation(.easeOut(duration: 1.2).delay(1.4), value: animateElements)

                                HStack {
                                    Button("Continue as Guest") {
                                        Task {
                                            await authService.continueAsGuest()
                                        }
                                    }
                                    .buttonStyle(PremiumGhostButtonStyle())

                                    if !showSignUp {
                                        Button("Forgot Password?") {
                                            showPasswordReset = true
                                        }
                                        .buttonStyle(PremiumGhostButtonStyle())
                                    }
                                }
                                .opacity(animateElements ? 1.0 : 0.0)
                                .offset(y: animateElements ? 0 : 20)
                                .animation(.easeOut(duration: 1.2).delay(1.6), value: animateElements)
                            }
                        }
                        .padding(.horizontal, Spacing.xl)

                        Spacer(minLength: 50)
                    }
                }

                // Loading Overlay
                if authService.isLoading {
                    LoadingOverlay()
                }
            }
        }
        .onAppear {
            withAnimation(.easeOut(duration: 0.8)) {
                animateElements = true
            }
        }
        .sheet(isPresented: $showSignUp) {
            SignUpView()
        }
        .sheet(isPresented: $showPasswordReset) {
            PasswordResetView()
        }
    }

    // MARK: - Premium Sign In Form

    private var premiumSignInForm: some View {
        PremiumCard {
            VStack(spacing: Spacing.lg) {
                Text("Welcome Back")
                    .font(.headlineSmall)
                    .fontWeight(.medium)
                    .foregroundColor(Color.authPrimaryGradient[0])

                VStack(spacing: Spacing.lg) {
                    // Email Field
                    VStack(alignment: .leading, spacing: Spacing.sm) {
                        Text("Email Address")
                            .font(.labelLarge)
                            .foregroundColor(Color.authPrimaryGradient[0])

                        TextField("Enter your email", text: $email)
                            .textFieldStyle(PremiumTextFieldStyle())
                            .keyboardType(.emailAddress)
                            .textContentType(.emailAddress)
                            .autocapitalization(.none)
                            .focused($emailFocused)
                    }

                    // Password Field
                    VStack(alignment: .leading, spacing: Spacing.sm) {
                        Text("Password")
                            .font(.labelLarge)
                            .foregroundColor(Color.authPrimaryGradient[0])

                        HStack {
                            Group {
                                if showPassword {
                                    TextField("Enter your password", text: $password)
                                } else {
                                    SecureField("Enter your password", text: $password)
                                }
                            }
                            .textFieldStyle(PremiumTextFieldStyle())
                            .textContentType(.password)
                            .focused($passwordFocused)

                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    showPassword.toggle()
                                }
                            }) {
                                Image(systemName: showPassword ? "eye.slash.fill" : "eye.fill")
                                    .foregroundColor(Color.authPrimaryGradient[0].opacity(0.6))
                                    .font(.bodyLarge)
                            }
                            .padding(.trailing, Spacing.md)
                        }
                    }
                }
            }
        }
        .padding(.horizontal, Spacing.xl)
        .opacity(animateElements ? 1.0 : 0.0)
        .offset(y: animateElements ? 0 : 30)
        .animation(.easeOut(duration: 1.2).delay(1.0), value: animateElements)
    }

    // MARK: - Actions

    private func signIn() async {
        guard !email.isEmpty, !password.isEmpty else { return }

        do {
            try await authService.signIn(email: email, password: password)
        } catch {
            // Error is handled by the AuthenticationService
            print("❌ Sign in failed: \(error)")
        }
    }

    private func clearForm() {
        email = ""
        password = ""
        showPassword = false
    }
}

// MARK: - Premium Supporting Views

struct PremiumFeatureRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: Spacing.lg) {
            // Premium Icon Container
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: Color.authAccentGradient,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 44, height: 44)

                Image(systemName: icon)
                    .font(.titleMedium)
                    .foregroundColor(.white)
            }

            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(title)
                    .font(.titleSmall)
                    .fontWeight(.semibold)
                    .foregroundColor(Color.authPrimaryGradient[0])

                Text(description)
                    .font(.bodyMedium)
                    .foregroundColor(Color.authPrimaryGradient[0].opacity(0.7))
                    .lineLimit(2)
            }

            Spacer()
        }
    }
}

struct SignUpView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var authService = AuthenticationService.shared
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var firstName = ""
    @State private var lastName = ""
    @State private var showPassword = false
    @State private var showConfirmPassword = false
    @State private var agreedToTerms = false

    var body: some View {
        NavigationView {
            ZStack {
                LinearGradient(
                    colors: [.niraGradientStart, .niraGradientEnd],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 30) {
                        Text("Join the NIRA Community! 🎉")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                            .padding(.top, 20)

                        Text("Start your language learning journey with millions of learners worldwide!")
                            .font(.title3)
                            .foregroundColor(.white.opacity(0.9))
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)

                        // Sign Up Form
                        VStack(spacing: 20) {
                            HStack(spacing: 12) {
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("First Name")
                                        .font(.subheadline)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.white)

                                    TextField("First name", text: $firstName)
                                        .textFieldStyle(ModernTextFieldStyle())
                                        .textContentType(.givenName)
                                }

                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Last Name")
                                        .font(.subheadline)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.white)

                                    TextField("Last name", text: $lastName)
                                        .textFieldStyle(ModernTextFieldStyle())
                                        .textContentType(.familyName)
                                }
                            }

                            VStack(alignment: .leading, spacing: 8) {
                                Text("Email")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)

                                TextField("Enter your email", text: $email)
                                    .textFieldStyle(ModernTextFieldStyle())
                                    .keyboardType(.emailAddress)
                                    .textContentType(.emailAddress)
                                    .autocapitalization(.none)
                            }

                            VStack(alignment: .leading, spacing: 8) {
                                Text("Password")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)

                                HStack {
                                    if showPassword {
                                        TextField("Create a password", text: $password)
                                    } else {
                                        SecureField("Create a password", text: $password)
                                    }

                                    Button(action: { showPassword.toggle() }) {
                                        Image(systemName: showPassword ? "eye.slash" : "eye")
                                            .foregroundColor(.gray)
                                    }
                                }
                                .textFieldStyle(ModernTextFieldStyle())
                                .textContentType(.newPassword)
                            }

                            VStack(alignment: .leading, spacing: 8) {
                                Text("Confirm Password")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)

                                HStack {
                                    if showConfirmPassword {
                                        TextField("Confirm your password", text: $confirmPassword)
                                    } else {
                                        SecureField("Confirm your password", text: $confirmPassword)
                                    }

                                    Button(action: { showConfirmPassword.toggle() }) {
                                        Image(systemName: showConfirmPassword ? "eye.slash" : "eye")
                                            .foregroundColor(.gray)
                                    }
                                }
                                .textFieldStyle(ModernTextFieldStyle())
                                .textContentType(.newPassword)
                            }

                            // Terms Agreement
                            HStack(alignment: .top, spacing: 12) {
                                Button(action: { agreedToTerms.toggle() }) {
                                    Image(systemName: agreedToTerms ? "checkmark.square.fill" : "square")
                                        .font(.title3)
                                        .foregroundColor(agreedToTerms ? .green : .white.opacity(0.7))
                                }

                                Text("I agree to the Terms of Service and Privacy Policy")
                                    .font(.subheadline)
                                    .foregroundColor(.white.opacity(0.9))
                                    .multilineTextAlignment(.leading)

                                Spacer()
                            }
                        }
                        .padding(.horizontal, 32)

                        Button("Create Account 🚀") {
                            Task {
                                await signUp()
                            }
                        }
                        .buttonStyle(AuthPrimaryButtonStyle())
                        .disabled(!isFormValid || authService.isLoading)
                        .padding(.horizontal, 32)

                        Spacer()
                    }
                    .padding()
                }

                if authService.isLoading {
                    LoadingOverlay()
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
            }
        }
    }

    private var isFormValid: Bool {
        !email.isEmpty &&
        !password.isEmpty &&
        !confirmPassword.isEmpty &&
        !firstName.isEmpty &&
        !lastName.isEmpty &&
        password == confirmPassword &&
        password.count >= 6 &&
        agreedToTerms
    }

    private func signUp() async {
        guard isFormValid else { return }

        do {
            try await authService.signUp(
                email: email,
                password: password,
                firstName: firstName,
                lastName: lastName
            )
            dismiss()
        } catch {
            // Error is handled by the AuthenticationService
            print("❌ Sign up failed: \(error)")
        }
    }
}

struct PasswordResetView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var authService = AuthenticationService.shared
    @State private var email = ""
    @State private var resetSent = false

    var body: some View {
        NavigationView {
            ZStack {
                LinearGradient(
                    colors: [.niraGradientStart, .niraGradientEnd],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                VStack(spacing: 30) {
                    if resetSent {
                        VStack(spacing: 20) {
                            Image(systemName: "envelope.circle.fill")
                                .font(.system(size: 80))
                                .foregroundColor(.white)

                            Text("Check Your Email")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.white)

                            Text("We've sent a password reset link to \(email)")
                                .font(.body)
                                .foregroundColor(.white.opacity(0.9))
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)

                            Button("Done") {
                                dismiss()
                            }
                            .buttonStyle(AuthPrimaryButtonStyle())
                        }
                    } else {
                        VStack(spacing: 30) {
                            VStack(spacing: 20) {
                                Image(systemName: "lock.rotation")
                                    .font(.system(size: 60))
                                    .foregroundColor(.white)

                                Text("Reset Password")
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)

                                Text("Enter your email address and we'll send you a link to reset your password.")
                                    .font(.body)
                                    .foregroundColor(.white.opacity(0.9))
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal)
                            }

                            VStack(alignment: .leading, spacing: 8) {
                                Text("Email")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)

                                TextField("Enter your email", text: $email)
                                    .textFieldStyle(ModernTextFieldStyle())
                                    .keyboardType(.emailAddress)
                                    .textContentType(.emailAddress)
                                    .autocapitalization(.none)
                            }
                            .padding(.horizontal, 32)

                            Button("Send Reset Link") {
                                Task {
                                    await resetPassword()
                                }
                            }
                            .buttonStyle(AuthPrimaryButtonStyle())
                            .disabled(email.isEmpty || authService.isLoading)
                            .padding(.horizontal, 32)
                        }
                    }

                    Spacer()
                }
                .padding()

                if authService.isLoading {
                    LoadingOverlay()
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
            }
        }
    }

    private func resetPassword() async {
        guard !email.isEmpty else { return }

        do {
            try await authService.resetPassword(email: email)
            resetSent = true
        } catch {
            // Error is handled by the AuthenticationService
            print("❌ Password reset failed: \(error)")
        }
    }
}

// MARK: - Custom Styles

struct AuthPrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Self.Configuration) -> some View {
        configuration.label
            .font(.headline)
            .fontWeight(.bold)
            .foregroundColor(.niraGradientStart)
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.white)
            .cornerRadius(25)
            .shadow(color: .black.opacity(0.2), radius: 10, x: 0, y: 5)
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct AuthSecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Self.Configuration) -> some View {
        configuration.label
            .font(.subheadline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.white.opacity(0.2))
            .cornerRadius(25)
            .overlay(
                RoundedRectangle(cornerRadius: 25)
                    .stroke(Color.white.opacity(0.5), lineWidth: 2)
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct ModernTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding()
            .background(Color.white.opacity(0.9))
            .cornerRadius(12)
            .font(.body)
    }
}

struct LoadingOverlay: View {
    var body: some View {
        ZStack {
            Color.black.opacity(0.3)
                .ignoresSafeArea()

            VStack(spacing: 16) {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.5)

                Text("Please wait...")
                    .font(.subheadline)
                    .foregroundColor(.white)
            }
            .padding(30)
            .background(Color.black.opacity(0.7))
            .cornerRadius(20)
        }
    }
}

#Preview {
    AuthenticationView()
}