import SwiftUI

// MARK: - Cultural Explore Tab Enum

enum CulturalExploreTab: Int, CaseIterable {
    case thirukkural = 0
    case calendar = 1
    case news = 2
    case culture = 3
    case map = 4

    var displayName: String {
        switch self {
        case .thirukkural: return "Thirukkural"
        case .calendar: return "Calendar"
        case .news: return "News"
        case .culture: return "Culture"
        case .map: return "Map"
        }
    }

    var icon: String {
        switch self {
        case .thirukkural: return "book.fill"
        case .calendar: return "calendar"
        case .news: return "newspaper.fill"
        case .culture: return "building.columns.fill"
        case .map: return "map.fill"
        }
    }
}

struct ExploreView: View {
    @State private var selectedTab = 0

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Clean Header matching Lessons style
                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Explore Tamil")
                            .font(.system(size: 24, weight: .bold, design: .rounded))
                            .foregroundColor(.primary)
                        Text("Discover culture, heritage & traditions")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    // Tamil cultural symbol
                    Text("🏛️")
                        .font(.title2)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)

                // Modern tab navigation matching Lessons design
                modernTabNavigation

                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    // Thirukkural detailed page
                    ThirukkuralExploreView()
                        .tag(0)

                    // Tamil Calendar detailed page
                    CalendarExploreView()
                        .tag(1)

                    // Tamil News detailed page
                    NewsExploreView()
                        .tag(2)

                    // Cultural Insights detailed page
                    CultureExploreView()
                        .tag(3)

                    // Cultural Map
                    MapExploreView()
                        .tag(4)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationBarHidden(true)
        }
    }

    // MARK: - Modern Tab Navigation

    private var modernTabNavigation: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(CulturalExploreTab.allCases, id: \.self) { tab in
                    modernTabButton(for: tab)
                }
            }
            .padding(.horizontal, 16)
        }
        .padding(.vertical, 12)
    }

    private func modernTabButton(for tab: CulturalExploreTab) -> some View {
        Button(action: {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                selectedTab = tab.rawValue
            }
        }) {
            HStack(spacing: 8) {
                Image(systemName: tab.icon)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(selectedTab == tab.rawValue ? .white : .secondary)

                Text(tab.displayName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(selectedTab == tab.rawValue ? .white : .secondary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                Group {
                    if selectedTab == tab.rawValue {
                        RoundedRectangle(cornerRadius: 20)
                            .fill(
                                LinearGradient(
                                    colors: [Color.blue, Color.purple],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                    } else {
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.gray.opacity(0.2))
                    }
                }
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}



// MARK: - Preview

#Preview {
    ExploreView()
}
