import SwiftUI

struct CalendarExploreView: View {
    @StateObject private var calendarService = TamilCalendarService.shared
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Header section
                VStack(alignment: .leading, spacing: 8) {
                    Text("தமிழ் நாள்காட்டி")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    Text("Traditional Tamil calendar system")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                .padding(.top)
                
                // Today's Date Card
                modernTodayDateCard
                
                // Current Month Events
                VStack(alignment: .leading, spacing: 12) {
                    Text("This Month's Events")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)
                    
                    ForEach(calendarService.upcomingEvents.prefix(3), id: \.id) { event in
                        modernEventCard(event: event)
                    }
                }
                
                // Tamil Months Grid
                VStack(alignment: .leading, spacing: 12) {
                    Text("Tamil Months")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                        ForEach(tamilMonths, id: \.name) { month in
                            modernMonthCard(month: month)
                        }
                    }
                    .padding(.horizontal)
                }
                
                // Festivals Section
                VStack(alignment: .leading, spacing: 12) {
                    Text("Major Festivals")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)
                    
                    ForEach(majorFestivals, id: \.name) { festival in
                        modernFestivalCard(festival: festival)
                    }
                }
            }
            .padding(.bottom, 100)
        }
        .task {
            await calendarService.loadTodayEvents()
        }
    }
    
    // MARK: - Modern Today Date Card

    private var modernTodayDateCard: some View {
        Button(action: {
            // Navigate to detailed calendar view
        }) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(calendarGradient)
                    .frame(width: 160, height: 160)
                    .blur(radius: 40)
                    .opacity(0.3)
                    .offset(x: 60, y: -60)

                VStack(alignment: .leading, spacing: 16) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(calendarGradient)
                        .frame(height: 6)
                        .clipShape(RoundedRectangle(cornerRadius: 3))

                    VStack(alignment: .leading, spacing: 12) {
                        // Header with date info
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Today's Date")
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)

                                Text(calendarService.currentTamilMonth)
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            Image(systemName: "calendar.circle.fill")
                                .font(.title2)
                                .foregroundColor(.blue)
                        }

                        // Tamil Date Display
                        Text(calendarService.currentTamilDate)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .lineLimit(2)

                        // English equivalent
                        Text("Today: \(Date().formatted(date: .abbreviated, time: .omitted))")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        // Metrics row
                        HStack(spacing: 16) {
                            CalendarMetricItem(icon: "calendar", label: "Month", value: calendarService.currentTamilMonth.prefix(3).description)
                            CalendarMetricItem(icon: "sun.max.fill", label: "Season", value: "Spring")

                            Spacer()

                            // Continue button
                            HStack(spacing: 6) {
                                Text("View Calendar")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                Image(systemName: "arrow.right")
                                    .font(.caption)
                            }
                            .foregroundColor(.primary)
                        }
                    }
                }
                .padding(20)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }

    private var calendarGradient: LinearGradient {
        LinearGradient(colors: [.blue, .indigo], startPoint: .topLeading, endPoint: .bottomTrailing)
    }
    
    // MARK: - Modern Event Card
    
    private func modernEventCard(event: TamilEvent) -> some View {
        HStack(spacing: 16) {
            // Date indicator
            VStack(spacing: 4) {
                Text("15")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(.niraPrimary)

                Text("Apr")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(width: 50)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.niraPrimary.opacity(0.1))
            )

            // Event details
            VStack(alignment: .leading, spacing: 4) {
                Text(event.tamilName)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(event.name)
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text(event.description)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            Spacer()

            // Event type icon
            Image(systemName: "star.fill")
                .foregroundColor(.orange)
                .font(.title3)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }
    
    // MARK: - Modern Month Card
    
    private func modernMonthCard(month: SimpleTamilMonth) -> some View {
        VStack(spacing: 8) {
            Text(month.tamilName)
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(month.name)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(month.season)
                .font(.caption2)
                .foregroundColor(.niraPrimary)
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(
                    Capsule()
                        .fill(Color.niraPrimary.opacity(0.1))
                )
        }
        .frame(maxWidth: .infinity)
        .frame(height: 80)
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 6, x: 0, y: 3)
        )
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    // MARK: - Modern Festival Card
    
    private func modernFestivalCard(festival: TamilFestival) -> some View {
        HStack(spacing: 16) {
            // Festival icon
            Text(festival.emoji)
                .font(.title)
                .frame(width: 50, height: 50)
                .background(
                    Circle()
                        .fill(festival.color.opacity(0.2))
                )
            
            // Festival details
            VStack(alignment: .leading, spacing: 4) {
                Text(festival.tamilName)
                    .font(.subheadline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(festival.name)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(festival.description)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }
}

// MARK: - Sample Data

private let tamilMonths = [
    SimpleTamilMonth(name: "Chithirai", tamilName: "சித்திரை", season: "Spring"),
    SimpleTamilMonth(name: "Vaikasi", tamilName: "வைகாசி", season: "Spring"),
    SimpleTamilMonth(name: "Aani", tamilName: "ஆனி", season: "Summer"),
    SimpleTamilMonth(name: "Aadi", tamilName: "ஆடி", season: "Summer"),
    SimpleTamilMonth(name: "Aavani", tamilName: "ஆவணி", season: "Monsoon"),
    SimpleTamilMonth(name: "Purattasi", tamilName: "புரட்டாசி", season: "Monsoon")
]

private let majorFestivals = [
    TamilFestival(name: "Pongal", tamilName: "பொங்கல்", description: "Harvest festival celebrating the Sun God", emoji: "🌾", color: .orange),
    TamilFestival(name: "Tamil New Year", tamilName: "தமிழ் புத்தாண்டு", description: "Beginning of the Tamil calendar year", emoji: "🎊", color: .green),
    TamilFestival(name: "Deepavali", tamilName: "தீபாவளி", description: "Festival of lights", emoji: "🪔", color: .yellow),
    TamilFestival(name: "Karthigai Deepam", tamilName: "கார்த்திகை தீபம்", description: "Festival of lamps", emoji: "🕯️", color: .red)
]

struct SimpleTamilMonth {
    let name: String
    let tamilName: String
    let season: String
}

struct TamilFestival {
    let name: String
    let tamilName: String
    let description: String
    let emoji: String
    let color: Color
}

struct CalendarMetricItem: View {
    let icon: String
    let label: String
    let value: String

    var body: some View {
        HStack(spacing: 8) {
            // Icon with gradient background
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.white)
                .frame(width: 28, height: 28)
                .background(LinearGradient(colors: [.blue, .indigo], startPoint: .topLeading, endPoint: .bottomTrailing))
                .clipShape(RoundedRectangle(cornerRadius: 8))

            VStack(alignment: .leading, spacing: 2) {
                Text(label)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                Text(value)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
        }
    }
}

#Preview {
    CalendarExploreView()
}
