import SwiftUI

struct CultureExploreView: View {
    @StateObject private var cultureService = CulturalInsightsService.shared
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Header section
                VStack(alignment: .leading, spacing: 8) {
                    Text("கலாச்சாரம்")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    Text("Explore Tamil culture and heritage")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                .padding(.top)
                
                // Today's Cultural Insight
                modernCultureCard(insight: cultureService.todayInsight, isToday: true)
                
                // Cultural Categories
                VStack(alignment: .leading, spacing: 12) {
                    Text("Explore by Category")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                        ForEach(culturalCategories, id: \.name) { category in
                            modernCategoryCard(category: category)
                        }
                    }
                    .padding(.horizontal)
                }
                
                // Recent Insights
                VStack(alignment: .leading, spacing: 12) {
                    Text("Recent Insights")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)
                    
                    ForEach(cultureService.featuredInsights.prefix(3), id: \.id) { insight in
                        modernCultureCard(insight: insight, isToday: false)
                    }
                }
            }
            .padding(.bottom, 100)
        }
        .task {
            await cultureService.loadTodayInsight()
        }
    }
    
    // MARK: - Modern Culture Card

    private func modernCultureCard(insight: TamilCulturalInsight, isToday: Bool) -> some View {
        Button(action: {
            // Navigate to insight detail
        }) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(cultureGradient)
                    .frame(width: 160, height: 160)
                    .blur(radius: 40)
                    .opacity(0.3)
                    .offset(x: 60, y: -60)

                VStack(alignment: .leading, spacing: 16) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(cultureGradient)
                        .frame(height: 6)
                        .clipShape(RoundedRectangle(cornerRadius: 3))

                    VStack(alignment: .leading, spacing: 12) {
                        // Header with insight info
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(isToday ? "Today's Insight" : "Cultural Insight")
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)

                                Text(insight.difficulty.rawValue)
                                    .font(.caption2)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(cultureGradient)
                                    .clipShape(Capsule())
                            }

                            Spacer()

                            if isToday {
                                Image(systemName: "star.fill")
                                    .foregroundColor(.yellow)
                                    .font(.title3)
                            }
                        }

                        // Title
                        Text(insight.title)
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .lineLimit(2)

                        // Content preview
                        Text(insight.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(3)

                        // Metrics row
                        HStack(spacing: 16) {
                            CultureMetricItem(icon: "book.fill", label: "Reading", value: "\(insight.readingTime)m")
                            CultureMetricItem(icon: "tag.fill", label: "Tags", value: "\(insight.tags.count)")

                            Spacer()

                            // Continue button
                            HStack(spacing: 6) {
                                Text("Read More")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                Image(systemName: "arrow.right")
                                    .font(.caption)
                            }
                            .foregroundColor(.primary)
                        }
                    }
                }
                .padding(20)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }

    private var cultureGradient: LinearGradient {
        LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
    }
    
    // MARK: - Modern Category Card
    
    private func modernCategoryCard(category: SimpleCulturalCategory) -> some View {
        VStack(spacing: 12) {
            // Icon
            Text(category.emoji)
                .font(.title)
                .frame(width: 50, height: 50)
                .background(
                    Circle()
                        .fill(category.color.opacity(0.2))
                )
            
            // Title
            Text(category.name)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
            
            // Count
            Text("\(category.count) insights")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .frame(height: 120)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }
}

// MARK: - Sample Data

private let culturalCategories = [
    SimpleCulturalCategory(name: "Arts & Dance", emoji: "💃", color: .pink, count: 25),
    SimpleCulturalCategory(name: "Literature", emoji: "📚", color: .blue, count: 40),
    SimpleCulturalCategory(name: "Music", emoji: "🎵", color: .purple, count: 30),
    SimpleCulturalCategory(name: "Festivals", emoji: "🎊", color: .orange, count: 20),
    SimpleCulturalCategory(name: "Architecture", emoji: "🏛️", color: .brown, count: 15),
    SimpleCulturalCategory(name: "Cuisine", emoji: "🍛", color: .green, count: 35)
]

struct SimpleCulturalCategory {
    let name: String
    let emoji: String
    let color: Color
    let count: Int
}

struct CultureMetricItem: View {
    let icon: String
    let label: String
    let value: String

    var body: some View {
        HStack(spacing: 8) {
            // Icon with gradient background
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.white)
                .frame(width: 28, height: 28)
                .background(LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing))
                .clipShape(RoundedRectangle(cornerRadius: 8))

            VStack(alignment: .leading, spacing: 2) {
                Text(label)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                Text(value)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
        }
    }
}

#Preview {
    CultureExploreView()
}
