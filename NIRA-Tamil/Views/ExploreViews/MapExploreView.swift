import SwiftUI

struct MapExploreView: View {
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Header section
                VStack(alignment: .leading, spacing: 8) {
                    Text("Cultural Map")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    Text("Explore Tamil heritage sites")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                .padding(.top)
                
                // Map placeholder
                modernMapPlaceholder
                
                // Heritage Sites
                VStack(alignment: .leading, spacing: 12) {
                    Text("Heritage Sites")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)
                    
                    ForEach(heritageSites, id: \.name) { site in
                        modernSiteCard(site: site)
                    }
                }
            }
            .padding(.bottom, 100)
        }
    }
    
    // MARK: - Modern Map Placeholder

    private var modernMapPlaceholder: some View {
        Button(action: {
            // Navigate to full map view
        }) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(mapGradient)
                    .frame(width: 120, height: 120)
                    .blur(radius: 30)
                    .opacity(0.3)
                    .offset(x: 50, y: -50)

                VStack(spacing: 16) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(mapGradient)
                        .frame(height: 6)
                        .clipShape(RoundedRectangle(cornerRadius: 3))

                    VStack(spacing: 12) {
                        // Map icon with gradient background
                        Image(systemName: "map.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.white)
                            .frame(width: 60, height: 60)
                            .background(mapGradient)
                            .clipShape(Circle())

                        Text("Interactive Tamil Cultural Map")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)

                        Text("Explore heritage sites across Tamil Nadu")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)

                        // Action button
                        HStack(spacing: 6) {
                            Text("Explore Map")
                                .font(.caption)
                                .fontWeight(.medium)
                            Image(systemName: "arrow.right")
                                .font(.caption)
                        }
                        .foregroundColor(.primary)
                    }
                }
                .padding(20)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .frame(height: 200)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }

    private var mapGradient: LinearGradient {
        LinearGradient(colors: [.pink, .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
    }
    
    // MARK: - Modern Site Card

    private func modernSiteCard(site: HeritageSite) -> some View {
        Button(action: {
            // Navigate to site detail
        }) {
            HStack(spacing: 16) {
                // Site image with gradient background
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(siteGradient(for: site))
                        .frame(width: 60, height: 60)

                    Text(site.emoji)
                        .font(.title2)
                }

                // Site details
                VStack(alignment: .leading, spacing: 4) {
                    Text(site.name)
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text(site.location)
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text(site.description)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }

                Spacer()

                // Distance indicator with gradient background
                VStack(spacing: 2) {
                    Text(site.distance)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(siteGradient(for: site))
                        .clipShape(Capsule())

                    Text("away")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding(16)
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }

    private func siteGradient(for site: HeritageSite) -> LinearGradient {
        switch site.color {
        case .orange: return LinearGradient(colors: [.orange, .yellow], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .pink: return LinearGradient(colors: [.pink, .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .blue: return LinearGradient(colors: [.blue, .indigo], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .purple: return LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
        default: return LinearGradient(colors: [.pink, .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }
}

// MARK: - Sample Data

private let heritageSites = [
    HeritageSite(name: "Brihadeeswarar Temple", location: "Thanjavur", description: "UNESCO World Heritage Site built by Chola dynasty", emoji: "🏛️", color: .orange, distance: "45 km"),
    HeritageSite(name: "Meenakshi Temple", location: "Madurai", description: "Historic Hindu temple dedicated to Goddess Meenakshi", emoji: "🕌", color: .pink, distance: "120 km"),
    HeritageSite(name: "Shore Temple", location: "Mahabalipuram", description: "Ancient stone temple by the sea", emoji: "🏖️", color: .blue, distance: "80 km"),
    HeritageSite(name: "Gangaikonda Cholapuram", location: "Ariyalur", description: "Former capital of Chola Empire", emoji: "👑", color: .purple, distance: "65 km")
]

struct HeritageSite {
    let name: String
    let location: String
    let description: String
    let emoji: String
    let color: Color
    let distance: String
}

#Preview {
    MapExploreView()
}
