//
//  GamificationDashboardView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import SwiftUI

struct GamificationDashboardView: View {
    @StateObject private var gamificationService = TamilWritingGamificationService.shared
    @State private var selectedTab: GamificationTab = .overview
    @State private var showingAchievementDetails = false
    @State private var showingChallengeDetails = false
    @State private var selectedAchievement: TamilWritingGamificationService.Achievement?
    @State private var selectedChallenge: TamilWritingGamificationService.Challenge?
    
    enum GamificationTab: String, CaseIterable {
        case overview = "Overview"
        case achievements = "Achievements"
        case challenges = "Challenges"
        case leaderboards = "Leaderboards"
        case social = "Social"
        
        var icon: String {
            switch self {
            case .overview: return "house"
            case .achievements: return "trophy"
            case .challenges: return "flag"
            case .leaderboards: return "chart.bar"
            case .social: return "person.2"
            }
        }
        
        var color: Color {
            switch self {
            case .overview: return .blue
            case .achievements: return .yellow
            case .challenges: return .orange
            case .leaderboards: return .purple
            case .social: return .pink
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                gamificationHeader
                
                // Tab selector
                tabSelector
                
                // Main content
                ScrollView {
                    VStack(spacing: 20) {
                        switch selectedTab {
                        case .overview:
                            overviewContent
                        case .achievements:
                            achievementsContent
                        case .challenges:
                            challengesContent
                        case .leaderboards:
                            leaderboardsContent
                        case .social:
                            socialContent
                        }
                    }
                    .padding()
                }
            }
            .background(
                LinearGradient(
                    colors: [Color.yellow.opacity(0.03), Color.orange.opacity(0.03)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingAchievementDetails) {
            if let achievement = selectedAchievement {
                AchievementDetailView(achievement: achievement)
            }
        }
        .sheet(isPresented: $showingChallengeDetails) {
            if let challenge = selectedChallenge {
                ChallengeDetailView(challenge: challenge)
            }
        }
        .onAppear {
            loadGamificationData()
        }
    }
    
    private var gamificationHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Tamil Writing Quest")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Level up your Tamil writing skills")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(spacing: 4) {
                    Image(systemName: "gamecontroller")
                        .font(.title2)
                        .foregroundColor(.orange)
                    
                    Text("Gamified")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                }
            }
            
            // User profile summary
            if let profile = gamificationService.userProfile {
                UserProfileSummaryCard(profile: profile)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private var tabSelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(GamificationTab.allCases, id: \.self) { tab in
                    TabChip(
                        tab: tab,
                        isSelected: selectedTab == tab,
                        action: { selectedTab = tab }
                    )
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
    }
    
    private var overviewContent: some View {
        VStack(spacing: 20) {
            // Quick stats
            quickStatsGrid
            
            // Current streak
            if let streakData = gamificationService.streakData {
                StreakCard(streakData: streakData)
            }
            
            // Recent achievements
            if !gamificationService.achievements.filter({ $0.isUnlocked }).isEmpty {
                recentAchievementsSection
            }
            
            // Active challenges
            if !gamificationService.activeChallenges.isEmpty {
                activeChallengesSection
            }
            
            // Recent rewards
            if !gamificationService.recentRewards.isEmpty {
                recentRewardsSection
            }
        }
    }
    
    private var quickStatsGrid: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
            if let profile = gamificationService.userProfile {
                GamificationQuickStatCard(
                    title: "Level",
                    value: "\(profile.level)",
                    subtitle: "Rank: \(profile.rank)",
                    icon: "star.fill",
                    color: .blue
                )
                
                QuickStatCard(
                    title: "Total Points",
                    value: "\(profile.totalPoints)",
                    subtitle: "Experience: \(profile.experience)",
                    icon: "diamond.fill",
                    color: .purple
                )
                
                QuickStatCard(
                    title: "Achievements",
                    value: "\(profile.statistics.achievementsUnlocked)",
                    subtitle: "Unlocked",
                    icon: "trophy.fill",
                    color: .yellow
                )
                
                QuickStatCard(
                    title: "Streak",
                    value: "\(profile.statistics.currentStreak)",
                    subtitle: "Days",
                    icon: "flame.fill",
                    color: .red
                )
            }
        }
    }
    
    private var achievementsContent: some View {
        VStack(spacing: 16) {
            // Achievement categories
            AchievementCategoriesView(
                achievements: gamificationService.achievements,
                onAchievementTap: { achievement in
                    selectedAchievement = achievement
                    showingAchievementDetails = true
                }
            )
        }
    }
    
    private var challengesContent: some View {
        VStack(spacing: 16) {
            // Active challenges
            ForEach(gamificationService.activeChallenges, id: \.id) { challenge in
                ChallengeCard(
                    challenge: challenge,
                    onTap: {
                        selectedChallenge = challenge
                        showingChallengeDetails = true
                    }
                )
            }
            
            if gamificationService.activeChallenges.isEmpty {
                EmptyStateCard(
                    icon: "flag",
                    title: "No Active Challenges",
                    description: "New challenges will appear daily. Check back tomorrow!"
                )
            }
        }
    }
    
    private var leaderboardsContent: some View {
        VStack(spacing: 16) {
            ForEach(gamificationService.leaderboards, id: \.id) { leaderboard in
                LeaderboardCard(leaderboard: leaderboard)
            }
        }
    }
    
    private var socialContent: some View {
        VStack(spacing: 16) {
            ForEach(gamificationService.socialFeed, id: \.id) { activity in
                SocialActivityCard(activity: activity)
            }
            
            if gamificationService.socialFeed.isEmpty {
                EmptyStateCard(
                    icon: "person.2",
                    title: "No Social Activity",
                    description: "Connect with other Tamil learners to see their achievements and progress!"
                )
            }
        }
    }
    
    private var recentAchievementsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Recent Achievements")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("View All") {
                    selectedTab = .achievements
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(gamificationService.achievements.filter({ $0.isUnlocked }).prefix(5), id: \.id) { achievement in
                        CompactAchievementCard(
                            achievement: achievement,
                            onTap: {
                                selectedAchievement = achievement
                                showingAchievementDetails = true
                            }
                        )
                    }
                }
                .padding(.horizontal, 4)
            }
        }
    }
    
    private var activeChallengesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Active Challenges")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("View All") {
                    selectedTab = .challenges
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            ForEach(gamificationService.activeChallenges.prefix(2), id: \.id) { challenge in
                CompactChallengeCard(
                    challenge: challenge,
                    onTap: {
                        selectedChallenge = challenge
                        showingChallengeDetails = true
                    }
                )
            }
        }
    }
    
    private var recentRewardsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Rewards")
                .font(.headline)
                .fontWeight(.semibold)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(gamificationService.recentRewards.prefix(5), id: \.id) { reward in
                        RewardCard(reward: reward)
                    }
                }
                .padding(.horizontal, 4)
            }
        }
    }
    
    private func loadGamificationData() {
        Task {
            await gamificationService.generateDailyChallenges()
        }
    }
}

// MARK: - Tab Chip

struct TabChip: View {
    let tab: GamificationDashboardView.GamificationTab
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: tab.icon)
                    .font(.caption)
                
                Text(tab.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : tab.color)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? tab.color : tab.color.opacity(0.1))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - User Profile Summary Card

struct UserProfileSummaryCard: View {
    let profile: TamilWritingGamificationService.GamificationProfile
    
    var body: some View {
        HStack(spacing: 16) {
            // Avatar and level
            VStack(spacing: 4) {
                ZStack {
                    Circle()
                        .fill(Color.blue.opacity(0.2))
                        .frame(width: 60, height: 60)
                    
                    Text("\(profile.level)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                }
                
                Text(profile.username)
                    .font(.caption)
                    .fontWeight(.semibold)
            }
            
            // Progress and stats
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Level \(profile.level)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Spacer()
                    
                    Text("\(profile.experience)/\(profile.experienceToNextLevel) XP")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                ProgressView(value: profile.progressToNextLevel, total: 1.0)
                    .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                
                HStack {
                    Text(profile.rank)
                        .font(.caption)
                        .foregroundColor(.blue)
                    
                    Spacer()
                    
                    Text("\(profile.totalPoints) points")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.blue.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.blue.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - Quick Stat Card

struct GamificationQuickStatCard: View {
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)
                
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 2) {
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(subtitle)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Streak Card

struct StreakCard: View {
    let streakData: TamilWritingGamificationService.StreakData
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "flame.fill")
                    .font(.title2)
                    .foregroundColor(.red)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Writing Streak")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("Keep the momentum going!")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(spacing: 2) {
                    Text("\(streakData.currentStreak)")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.red)
                    
                    Text("days")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            HStack {
                Text("Longest streak: \(streakData.longestStreak) days")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                if streakData.isActive {
                    Text("Active")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                } else {
                    Text("Inactive")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.red)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.red.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.red.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - Compact Achievement Card

struct GamificationCompactAchievementCard: View {
    let achievement: TamilWritingGamificationService.Achievement
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Image(systemName: achievement.icon)
                    .font(.title2)
                    .foregroundColor(achievement.category.color)
                
                Text(achievement.title)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .lineLimit(2)
                    .multilineTextAlignment(.center)
                
                Text("\(achievement.points) pts")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .padding()
            .frame(width: 100, height: 100)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(achievement.category.color.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(achievement.category.color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Compact Challenge Card

struct CompactChallengeCard: View {
    let challenge: TamilWritingGamificationService.Challenge
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                Image(systemName: challenge.icon)
                    .font(.title3)
                    .foregroundColor(challenge.difficulty.color)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(challenge.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(challenge.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                Spacer()
                
                VStack(spacing: 2) {
                    Text(challenge.challengeType.displayName)
                        .font(.caption2)
                        .foregroundColor(challenge.difficulty.color)
                    
                    Text(challenge.difficulty.rawValue.capitalized)
                        .font(.caption2)
                        .fontWeight(.semibold)
                        .foregroundColor(challenge.difficulty.color)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Reward Card

struct RewardCard: View {
    let reward: TamilWritingGamificationService.Reward
    
    var body: some View {
        VStack(spacing: 6) {
            Image(systemName: reward.icon)
                .font(.title3)
                .foregroundColor(reward.rarity.color)
            
            Text(reward.title)
                .font(.caption)
                .fontWeight(.semibold)
                .lineLimit(1)
            
            Text("\(reward.value)")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .padding()
        .frame(width: 80, height: 80)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(reward.rarity.color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(reward.rarity.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Empty State Card

struct GamificationEmptyStateCard: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 40))
                .foregroundColor(.gray)
            
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Achievement Categories View

struct AchievementCategoriesView: View {
    let achievements: [TamilWritingGamificationService.Achievement]
    let onAchievementTap: (TamilWritingGamificationService.Achievement) -> Void

    @State private var selectedCategory: TamilWritingGamificationService.Achievement.Category = .firstSteps

    var body: some View {
        VStack(spacing: 16) {
            // Category selector
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(TamilWritingGamificationService.Achievement.Category.allCases, id: \.self) { category in
                        CategoryButton(
                            category: category,
                            isSelected: selectedCategory == category,
                            count: achievements.filter { $0.category == category }.count,
                            action: { selectedCategory = category }
                        )
                    }
                }
                .padding(.horizontal)
            }

            // Achievements grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(filteredAchievements, id: \.id) { achievement in
                    AchievementCard(
                        achievement: achievement,
                        onTap: { onAchievementTap(achievement) }
                    )
                }
            }
        }
    }

    private var filteredAchievements: [TamilWritingGamificationService.Achievement] {
        achievements.filter { $0.category == selectedCategory }
    }
}

struct CategoryButton: View {
    let category: TamilWritingGamificationService.Achievement.Category
    let isSelected: Bool
    let count: Int
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Text(category.displayName)
                    .font(.caption)
                    .fontWeight(.medium)

                Text("\(count)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .foregroundColor(isSelected ? .white : category.color)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? category.color : category.color.opacity(0.1))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Achievement Card

struct AchievementCard: View {
    let achievement: TamilWritingGamificationService.Achievement
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Achievement icon and status
                ZStack {
                    Circle()
                        .fill(achievement.category.color.opacity(0.2))
                        .frame(width: 60, height: 60)

                    Image(systemName: achievement.icon)
                        .font(.title2)
                        .foregroundColor(achievement.isUnlocked ? achievement.category.color : .gray)

                    if achievement.isUnlocked {
                        VStack {
                            Spacer()
                            HStack {
                                Spacer()
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.caption)
                                    .foregroundColor(.green)
                                    .background(Circle().fill(Color.white))
                            }
                        }
                        .frame(width: 60, height: 60)
                    }
                }

                // Achievement details
                VStack(spacing: 4) {
                    Text(achievement.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .lineLimit(2)
                        .multilineTextAlignment(.center)

                    Text(achievement.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(3)
                        .multilineTextAlignment(.center)
                }

                // Progress or points
                if achievement.isUnlocked {
                    HStack {
                        Image(systemName: "star.fill")
                            .font(.caption2)
                            .foregroundColor(.yellow)

                        Text("\(achievement.points) pts")
                            .font(.caption2)
                            .fontWeight(.semibold)
                            .foregroundColor(.yellow)
                    }
                } else {
                    ProgressView(value: achievement.progress, total: 1.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: achievement.category.color))
                        .frame(height: 4)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(achievement.isUnlocked ? achievement.category.color : Color.gray.opacity(0.3), lineWidth: 1)
                    )
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .opacity(achievement.isUnlocked ? 1.0 : 0.7)
    }
}

// MARK: - Challenge Card

struct ChallengeCard: View {
    let challenge: TamilWritingGamificationService.Challenge
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Challenge header
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(challenge.title)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)

                        Text(challenge.challengeType.displayName)
                            .font(.caption)
                            .foregroundColor(challenge.difficulty.color)
                    }

                    Spacer()

                    VStack(spacing: 2) {
                        Image(systemName: challenge.icon)
                            .font(.title3)
                            .foregroundColor(challenge.difficulty.color)

                        Text(challenge.difficulty.rawValue.capitalized)
                            .font(.caption2)
                            .fontWeight(.semibold)
                            .foregroundColor(challenge.difficulty.color)
                    }
                }

                // Challenge description
                Text(challenge.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                // Challenge requirements
                VStack(spacing: 6) {
                    ForEach(challenge.requirements, id: \.id) { requirement in
                        HStack {
                            Text(requirement.description)
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Spacer()

                            Text("\(Int(requirement.current))/\(Int(requirement.target)) \(requirement.unit)")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(requirement.isCompleted ? .green : .blue)
                        }

                        ProgressView(value: requirement.progress, total: 1.0)
                            .progressViewStyle(LinearProgressViewStyle(tint: requirement.isCompleted ? .green : .blue))
                            .frame(height: 4)
                    }
                }

                // Challenge rewards and time
                HStack {
                    HStack(spacing: 4) {
                        Image(systemName: "gift")
                            .font(.caption)
                            .foregroundColor(.orange)

                        Text("\(challenge.rewards.count) rewards")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }

                    Spacer()

                    Text("Ends \(challenge.endDate, style: .relative)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(challenge.difficulty.color.opacity(0.3), lineWidth: 1)
                    )
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Leaderboard Card

struct LeaderboardCard: View {
    let leaderboard: TamilWritingGamificationService.Leaderboard

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(leaderboard.title)
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text(leaderboard.timeframe.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: leaderboard.category.icon)
                    .font(.title3)
                    .foregroundColor(.purple)
            }

            if leaderboard.entries.isEmpty {
                Text("No entries yet")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                VStack(spacing: 8) {
                    ForEach(leaderboard.entries.prefix(5), id: \.id) { entry in
                        LeaderboardEntryRow(entry: entry)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

struct LeaderboardEntryRow: View {
    let entry: TamilWritingGamificationService.Leaderboard.LeaderboardEntry

    var body: some View {
        HStack {
            // Rank
            Text("#\(entry.rank)")
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(rankColor)
                .frame(width: 30, alignment: .leading)

            // Username
            Text(entry.username)
                .font(.subheadline)
                .fontWeight(.medium)

            Spacer()

            // Score
            Text("\(Int(entry.score))")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.blue)

            // Change indicator
            Image(systemName: entry.change.icon)
                .font(.caption)
                .foregroundColor(entry.change.color)
        }
    }

    private var rankColor: Color {
        switch entry.rank {
        case 1: return .yellow
        case 2: return .gray
        case 3: return .brown
        default: return .secondary
        }
    }
}

// MARK: - Social Activity Card

struct SocialActivityCard: View {
    let activity: TamilWritingGamificationService.SocialActivity

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                // User info
                VStack(alignment: .leading, spacing: 2) {
                    Text(activity.username)
                        .font(.subheadline)
                        .fontWeight(.semibold)

                    Text(activity.timestamp, style: .relative)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: activity.activityType.icon)
                    .font(.title3)
                    .foregroundColor(.blue)
            }

            // Activity content
            VStack(alignment: .leading, spacing: 4) {
                Text(activity.title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text(activity.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // Social interactions
            HStack {
                Button(action: {
                    // Like action
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: activity.isLiked ? "heart.fill" : "heart")
                            .font(.caption)
                            .foregroundColor(activity.isLiked ? .red : .gray)

                        Text("\(activity.likes)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .buttonStyle(PlainButtonStyle())

                Button(action: {
                    // Comment action
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "bubble.left")
                            .font(.caption)
                            .foregroundColor(.gray)

                        Text("\(activity.comments.count)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .buttonStyle(PlainButtonStyle())

                Spacer()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Achievement Detail View

struct AchievementDetailView: View {
    let achievement: TamilWritingGamificationService.Achievement
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Achievement icon and title
                    VStack(spacing: 16) {
                        ZStack {
                            Circle()
                                .fill(achievement.category.color.opacity(0.2))
                                .frame(width: 120, height: 120)

                            Image(systemName: achievement.icon)
                                .font(.system(size: 60))
                                .foregroundColor(achievement.category.color)
                        }

                        VStack(spacing: 8) {
                            Text(achievement.title)
                                .font(.title2)
                                .fontWeight(.bold)
                                .multilineTextAlignment(.center)

                            Text(achievement.description)
                                .font(.body)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                    }
                    .padding(.top)

                    // Achievement details
                    VStack(spacing: 16) {
                        DetailRow(label: "Category", value: achievement.category.displayName)
                        DetailRow(label: "Difficulty", value: achievement.difficulty.rawValue.capitalized)
                        DetailRow(label: "Points", value: "\(achievement.points)")

                        if achievement.isUnlocked, let unlockedDate = achievement.unlockedDate {
                            DetailRow(label: "Unlocked", value: unlockedDate.formatted(date: .abbreviated, time: .omitted))
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.secondarySystemBackground))
                    )

                    // Requirements
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Requirements")
                            .font(.headline)
                            .fontWeight(.semibold)

                        ForEach(achievement.requirements, id: \.id) { requirement in
                            RequirementRow(requirement: requirement)
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.secondarySystemBackground))
                    )

                    // Rewards
                    if !achievement.rewards.isEmpty {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Rewards")
                                .font(.headline)
                                .fontWeight(.semibold)

                            ForEach(achievement.rewards, id: \.id) { reward in
                                RewardRow(reward: reward)
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.secondarySystemBackground))
                        )
                    }
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct RequirementRow: View {
    let requirement: TamilWritingGamificationService.Achievement.Requirement

    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            HStack {
                Text(requirement.description)
                    .font(.subheadline)

                Spacer()

                Text("\(Int(requirement.current))/\(Int(requirement.target))")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(requirement.isCompleted ? .green : .blue)
            }

            ProgressView(value: requirement.progress, total: 1.0)
                .progressViewStyle(LinearProgressViewStyle(tint: requirement.isCompleted ? .green : .blue))
        }
    }
}

struct RewardRow: View {
    let reward: TamilWritingGamificationService.Reward

    var body: some View {
        HStack {
            Image(systemName: reward.icon)
                .font(.title3)
                .foregroundColor(reward.rarity.color)

            VStack(alignment: .leading, spacing: 2) {
                Text(reward.title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text(reward.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Text("\(reward.value)")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(reward.rarity.color)
        }
    }
}

// MARK: - Challenge Detail View

struct ChallengeDetailView: View {
    let challenge: TamilWritingGamificationService.Challenge
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("Challenge details coming soon...")
                        .foregroundColor(.secondary)
                        .padding(.top)
                }
                .padding()
            }
            .navigationTitle(challenge.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct GamificationDetailRow: View {
    let label: String
    let value: String

    var body: some View {
        HStack {
            Text(label)
                .font(.subheadline)
                .foregroundColor(.secondary)

            Spacer()

            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
}

#Preview {
    GamificationDashboardView()
}
