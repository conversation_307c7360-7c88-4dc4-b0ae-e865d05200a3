//
//  LessonsView.swift
//  NIRA
//

import SwiftUI

struct LessonsView: View {
    @StateObject private var tamilContentService = TamilContentService.shared
    @StateObject private var userPreferences = UserPreferencesService.shared
    @State private var selectedLevel: CEFRLevel = .a1
    @State private var selectedTrack: ContentTrack = .conversational
    @State private var selectedLesson: TamilLesson?
    @State private var showingLessonDetail = false
    @State private var debugInfo: String = "Loading..."
    @State private var currentLessons: [TamilLesson] = []

    // Load lessons when level or track changes
    private func loadCurrentLessons() {
        switch selectedTrack {
        case .conversational:
            currentLessons = tamilContentService.getLessonsForLevel(selectedLevel)
        case .academic:
            // Academic content is handled separately in AcademicContentView
            currentLessons = []
        }
    }

    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 12) {


                        // Track selector
                        HStack(spacing: 12) {
                            ForEach(ContentTrack.allCases, id: \.self) { track in
                                Button(track.displayName) {
                                    selectedTrack = track
                                    tamilContentService.currentTrack = track
                                }
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(selectedTrack == track ? Color.blue : Color(.systemGray6))
                                .foregroundColor(selectedTrack == track ? .white : .primary)
                                .cornerRadius(20)
                            }
                        }
                        .padding(.horizontal)

                        // Level filter (for conversational track)
                        if selectedTrack == .conversational {
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 12) {
                                    ForEach(CEFRLevel.allCases, id: \.self) { level in
                                        Button(level.rawValue) {
                                            selectedLevel = level
                                            tamilContentService.currentLevel = level
                                            loadCurrentLessons()
                                        }
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(selectedLevel == level ? Color.green : Color(.systemGray6))
                                        .foregroundColor(selectedLevel == level ? .white : .primary)
                                        .cornerRadius(20)
                                    }
                                }
                                .padding(.horizontal)
                            }
                        }
                    }

                    // Content loading state
                    if tamilContentService.isLoading {
                        VStack(spacing: 16) {
                            ProgressView()
                                .scaleEffect(1.2)
                            Text("Loading Tamil content...")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        .frame(maxWidth: .infinity, minHeight: 200)
                    } else if let errorMessage = tamilContentService.errorMessage {
                        VStack(spacing: 16) {
                            Image(systemName: "exclamationmark.triangle")
                                .font(.system(size: 48))
                                .foregroundColor(.orange)
                            Text("Content Loading Error")
                                .font(.headline)
                                .fontWeight(.semibold)
                            Text(errorMessage)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                            Button("Retry") {
                                Task { @MainActor in
                                    await tamilContentService.loadAllContent()
                                }
                            }
                            .buttonStyle(.borderedProminent)
                        }
                        .frame(maxWidth: .infinity, minHeight: 200)
                        .padding()
                    } else if selectedTrack == .academic {
                        // Show academic content view
                        AcademicContentView()
                    } else if currentLessons.isEmpty {
                        VStack(spacing: 16) {
                            Image(systemName: "book.closed")
                                .font(.system(size: 48))
                                .foregroundColor(.secondary)
                            Text("No Lessons Available")
                                .font(.headline)
                                .fontWeight(.semibold)
                            Text("Content for \(selectedTrack.displayName) - \(selectedLevel.displayName) is coming soon!")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                        .frame(maxWidth: .infinity, minHeight: 200)
                        .padding()
                    } else {
                        // Recommendations section (if any)
                        let recommendations = tamilContentService.getRecommendedLessons()
                        if !recommendations.isEmpty && selectedTrack == .conversational {
                            VStack(alignment: .leading, spacing: 12) {
                                Text("Recommended for You")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .padding(.horizontal)

                                ScrollView(.horizontal, showsIndicators: false) {
                                    HStack(spacing: 16) {
                                        ForEach(recommendations.prefix(3)) { lesson in
                                            TamilLessonCard(lesson: lesson, isRecommended: true) {
                                                selectedLesson = lesson
                                                showingLessonDetail = true
                                                tamilContentService.currentLesson = lesson
                                            }
                                            .frame(width: 300)
                                        }
                                    }
                                    .padding(.horizontal)
                                }
                            }
                        }

                        // All lessons grid
                        VStack(alignment: .leading, spacing: 12) {
                            Text("All Lessons")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .padding(.horizontal)

                            LazyVStack(spacing: 16) {
                                ForEach(currentLessons) { lesson in
                                    TamilLessonCard(lesson: lesson) {
                                        if tamilContentService.isLessonUnlocked(lesson) {
                                            selectedLesson = lesson
                                            showingLessonDetail = true
                                            tamilContentService.currentLesson = lesson
                                        }
                                    }
                                }
                            }
                            .padding(.horizontal)
                        }
                    }
                }
                .padding(.vertical)
            }
        }
        .task {
            await tamilContentService.loadAllContent()
            loadCurrentLessons()
        }
        .onChange(of: selectedTrack) {
            loadCurrentLessons()
        }
        .sheet(isPresented: $showingLessonDetail) {
            if let selectedLesson = selectedLesson {
                TamilLessonDetailView(lesson: selectedLesson)
            }
        }
    }
}

// MARK: - Tamil Lesson Card

struct TamilLessonCard: View {
    let lesson: TamilLesson
    let isRecommended: Bool
    let onTap: () -> Void
    @StateObject private var audioManager = AudioContentManager.shared
    @StateObject private var contentService = TamilContentService.shared

    init(lesson: TamilLesson, isRecommended: Bool = false, onTap: @escaping () -> Void) {
        self.lesson = lesson
        self.isRecommended = isRecommended
        self.onTap = onTap
    }

    // Dynamic vibrant gradient based on lesson content
    private var cardVariant: CardVariant {
        let title = lesson.titleEnglish.lowercased()
        let focus = lesson.focus.lowercased()

        if title.contains("greeting") || focus.contains("greeting") || title.contains("introduction") {
            return .blue
        } else if title.contains("number") || focus.contains("number") || title.contains("count") {
            return .purple
        } else if title.contains("family") || focus.contains("family") || title.contains("relation") {
            return .green
        } else if title.contains("time") || focus.contains("time") || title.contains("calendar") {
            return .orange
        } else {
            return .pink
        }
    }

    private var levelColor: Color {
        switch CEFRLevel(rawValue: lesson.levelCode) ?? .a1 {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }

    private var isUnlocked: Bool {
        contentService.isLessonUnlocked(lesson)
    }

    private var isCompleted: Bool {
        contentService.isLessonCompleted(lesson)
    }

    var body: some View {
        Button(action: {
            // Add safety check before calling onTap
            guard isUnlocked else {
                print("⚠️ Lesson \(lesson.titleEnglish) is locked")
                return
            }
            onTap()
        }) {
            ZStack {
                // Animated glowing orb background
                Circle()
                    .fill(cardVariant.gradient)
                    .frame(width: 160, height: 160)
                    .blur(radius: 40)
                    .opacity(0.3)
                    .offset(x: 60, y: -60)

                VStack(alignment: .leading, spacing: 12) {
                    // Top gradient stripe
                    Rectangle()
                        .fill(cardVariant.gradient)
                        .frame(height: 4)
                        .clipShape(RoundedRectangle(cornerRadius: 2))

                    VStack(alignment: .leading, spacing: 8) {
                        // Header with lesson info
                        HStack {
                            HStack(spacing: 8) {
                                // Status indicator
                                if isCompleted {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.green)
                                        .font(.title3)
                                } else if !isUnlocked {
                                    Image(systemName: "lock.fill")
                                        .foregroundColor(.gray)
                                        .font(.title3)
                                } else if isRecommended {
                                    Image(systemName: "star.fill")
                                        .foregroundColor(.orange)
                                        .font(.title3)
                                } else {
                                    Image(systemName: "book.fill")
                                        .foregroundColor(levelColor)
                                        .font(.title3)
                                }

                                Text(lesson.levelCode)
                                    .font(.subheadline)
                                    .fontWeight(.bold)
                                    .foregroundColor(isUnlocked ? levelColor : .gray)

                                Text("Lesson \(lesson.lessonNumber)")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(isUnlocked ? .secondary : .gray)
                            }

                            Spacer()

                            HStack(spacing: 4) {
                                Image(systemName: "clock")
                                    .font(.caption)
                                Text("\(lesson.durationMinutes) mins")
                                    .font(.caption)
                            }
                            .foregroundColor(isUnlocked ? .secondary : .gray)
                        }

                        // Title and description
                        VStack(alignment: .leading, spacing: 8) {
                            Text(lesson.titleEnglish)
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.primary)
                                .multilineTextAlignment(.leading)
                                .lineLimit(2)

                            Text(lesson.titleTamil)
                                .font(.title3)
                                .fontWeight(.medium)
                                .foregroundColor(levelColor)
                                .multilineTextAlignment(.leading)
                                .lineLimit(2)
                        }
                    }
                    .padding(.horizontal, 4)
                }
                .padding(16)
            }
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isUnlocked ? Color(.systemBackground) : Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(
                                isCompleted ? Color.green.opacity(0.5) :
                                isRecommended ? Color.orange.opacity(0.5) :
                                isUnlocked ? cardVariant.primaryColor.opacity(0.3) : Color.gray.opacity(0.3),
                                lineWidth: isCompleted || isRecommended ? 2 : 1
                            )
                    )
            )
            .shadow(
                color: isCompleted ? Color.green.opacity(0.3) :
                       isRecommended ? Color.orange.opacity(0.3) :
                       isUnlocked ? cardVariant.primaryColor.opacity(0.2) : Color.clear,
                radius: 8, x: 0, y: 4
            )
        }
        .buttonStyle(PlainButtonStyle())
        .opacity(isUnlocked ? 1.0 : 0.6)
        .disabled(!isUnlocked)
    }
}

// MARK: - Supporting Views and Types

enum CardVariant {
    case blue, purple, green, orange, pink
    
    var primaryColor: Color {
        switch self {
        case .blue: return .blue
        case .purple: return .purple
        case .green: return .green
        case .orange: return .orange
        case .pink: return .pink
        }
    }
    
    var gradient: LinearGradient {
        switch self {
        case .blue:
            return LinearGradient(colors: [.blue, .indigo], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .purple:
            return LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .green:
            return LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .orange:
            return LinearGradient(colors: [.orange, .yellow], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .pink:
            return LinearGradient(colors: [.pink, .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }
}

// MARK: - CardVariant Extensions

extension CardVariant {
    static let thirukkural = CardVariant.orange
}

struct MetricItem: View {
    let icon: String
    let label: String
    let value: Int
    let variant: CardVariant
    
    var body: some View {
        HStack(spacing: 8) {
            // Icon with gradient background
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.white)
                .frame(width: 28, height: 28)
                .background(variant.gradient)
                .clipShape(RoundedRectangle(cornerRadius: 8))
            
            VStack(alignment: .leading, spacing: 1) {
                Text(label)
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                    .lineLimit(1)

                Text("\(value)")
                    .font(.subheadline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
            }
            
            Spacer()
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color(.systemGray6).opacity(0.6))
        )
    }
}

// MARK: - Tamil Lesson Detail View

struct TamilLessonDetailView: View {
    let lesson: TamilLesson
    @Environment(\.dismiss) private var dismiss
    @StateObject private var audioManager = AudioContentManager.shared

    // Collapsible section states
    @State private var isVocabularyExpanded = false
    @State private var isConversationsExpanded = false
    @State private var isGrammarExpanded = false
    @State private var isPracticeExpanded = false

    // Real database content
    @State private var realVocabulary: [TamilSupabaseVocabulary] = []
    @State private var realConversations: [TamilSupabaseConversation] = []
    @State private var realGrammar: [TamilSupabaseGrammarTopic] = []
    @State private var realPractice: [TamilSupabasePracticeExercise] = []
    @State private var isLoadingContent = false
    @State private var contentLoaded = false

    // Modal presentation states
    @State private var showVocabularyModal = false
    @State private var showConversationModal = false
    @State private var showGrammarModal = false
    @State private var showPracticeModal = false
    @State private var selectedVocabIndex = 0
    @State private var selectedConversationIndex = 0
    @State private var selectedGrammarIndex = 0
    @State private var selectedPracticeIndex = 0

    // Enhanced grammar data
    @State private var enhancedGrammarPoints: [LessonGrammarPoint] = []

    private var levelColor: Color {
        switch CEFRLevel(rawValue: lesson.levelCode) ?? .a1 {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Enhanced Header with gradient background
                    ZStack {
                        // Background gradient
                        RoundedRectangle(cornerRadius: 20)
                            .fill(
                                LinearGradient(
                                    colors: [levelColor.opacity(0.1), levelColor.opacity(0.05)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(levelColor.opacity(0.3), lineWidth: 1)
                            )

                        VStack(alignment: .leading, spacing: 16) {
                            HStack {
                                HStack(spacing: 8) {
                                    Text(lesson.levelCode)
                                        .font(.caption)
                                        .fontWeight(.bold)
                                        .padding(.horizontal, 10)
                                        .padding(.vertical, 6)
                                        .background(levelColor)
                                        .foregroundColor(.white)
                                        .cornerRadius(10)

                                    Text("Lesson \(lesson.lessonNumber)")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(levelColor)
                                }

                                Spacer()

                                HStack(spacing: 4) {
                                    Image(systemName: "clock.fill")
                                        .font(.caption)
                                        .foregroundColor(levelColor)
                                    Text("\(lesson.durationMinutes) min")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(levelColor)
                                }
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(levelColor.opacity(0.2))
                                .cornerRadius(8)
                            }

                            VStack(alignment: .leading, spacing: 8) {
                                Text(lesson.titleEnglish)
                                    .font(.largeTitle)
                                    .fontWeight(.bold)
                                    .foregroundColor(.primary)

                                Text(lesson.titleTamil)
                                    .font(.title2)
                                    .fontWeight(.medium)
                                    .foregroundColor(levelColor)
                            }

                            Text(lesson.focus)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 8)
                                .background(Color(.systemGray6))
                                .cornerRadius(10)
                        }
                        .padding(20)
                    }

                    // Vocabulary Section - Always show vocabulary (real or fallback)
                    if (!realVocabulary.isEmpty && contentLoaded) || !lesson.vocabulary.isEmpty || isLoadingContent {
                        VStack(alignment: .leading, spacing: 16) {
                            Button {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    isVocabularyExpanded.toggle()
                                }
                            } label: {
                                HStack {
                                    if isLoadingContent {
                                        HStack(spacing: 8) {
                                            ProgressView()
                                                .scaleEffect(0.8)
                                            Text("Loading vocabulary...")
                                                .font(.headline)
                                                .fontWeight(.semibold)
                                                .foregroundColor(.primary)
                                        }
                                    } else {
                                        Text("Vocabulary (\(contentLoaded ? realVocabulary.count : lesson.vocabulary.count) words)")
                                            .font(.headline)
                                            .fontWeight(.semibold)
                                            .foregroundColor(.primary)
                                    }

                                    Spacer()

                                    Image(systemName: isVocabularyExpanded ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                                        .font(.title3)
                                        .foregroundColor(levelColor)
                                }
                            }

                            if isVocabularyExpanded {
                                if contentLoaded && !realVocabulary.isEmpty {
                                    LazyVStack(spacing: 12) {
                                        ForEach(Array(realVocabulary.prefix(10).enumerated()), id: \.element.id) { index, vocab in
                                            RealVocabularyItemView(vocabulary: vocab, level: CEFRLevel(rawValue: lesson.levelCode) ?? .a1)
                                                .onTapGesture {
                                                    selectedVocabIndex = index
                                                    showVocabularyModal = true
                                                }
                                        }
                                    }

                                    if realVocabulary.count > 10 {
                                        Button {
                                            selectedVocabIndex = 10
                                            showVocabularyModal = true
                                        } label: {
                                            Text("+ \(realVocabulary.count - 10) more words • Tap any word for details")
                                                .font(.caption)
                                                .foregroundColor(levelColor)
                                                .underline()
                                        }
                                    } else {
                                        Text("💡 Tap any vocabulary word for detailed view with larger text")
                                            .font(.caption2)
                                            .foregroundColor(.secondary)
                                            .italic()
                                    }
                                } else if !lesson.vocabulary.isEmpty {
                                    LazyVStack(spacing: 12) {
                                        ForEach(lesson.vocabulary.prefix(10)) { vocab in
                                            VocabularyItemView(vocabulary: vocab, level: CEFRLevel(rawValue: lesson.levelCode) ?? .a1)
                                        }
                                    }

                                    if lesson.vocabulary.count > 10 {
                                        Text("+ \(lesson.vocabulary.count - 10) more words")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                } else {
                                    Text("No vocabulary available")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .padding()
                                }
                            }
                        }
                    }

                    // Conversations Section - Always show conversations (real or fallback)
                    if (!realConversations.isEmpty && contentLoaded) || !lesson.conversations.isEmpty {
                        VStack(alignment: .leading, spacing: 16) {
                            Button {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    isConversationsExpanded.toggle()
                                }
                            } label: {
                                HStack {
                                    Text("Conversations (\(contentLoaded && !realConversations.isEmpty ? realConversations.count : lesson.conversations.count))")
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.primary)

                                    Spacer()

                                    Image(systemName: isConversationsExpanded ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                                        .font(.title3)
                                        .foregroundColor(levelColor)
                                }
                            }

                            if isConversationsExpanded {
                                if contentLoaded && !realConversations.isEmpty {
                                    // Show real Supabase conversations
                                    ForEach(Array(realConversations.prefix(5).enumerated()), id: \.element.id) { index, conversation in
                                        RealConversationPreviewView(conversation: conversation, level: CEFRLevel(rawValue: lesson.levelCode) ?? .a1)
                                            .onTapGesture {
                                                selectedConversationIndex = index
                                                showConversationModal = true
                                            }
                                    }

                                    if realConversations.count > 5 {
                                        Button {
                                            selectedConversationIndex = 5
                                            showConversationModal = true
                                        } label: {
                                            Text("+ \(realConversations.count - 5) more conversations • Tap for details")
                                                .font(.caption)
                                                .foregroundColor(levelColor)
                                                .underline()
                                        }
                                    } else {
                                        Text("💡 Tap any conversation for detailed view")
                                            .font(.caption2)
                                            .foregroundColor(.secondary)
                                            .italic()
                                    }
                                } else {
                                    // Show fallback conversations
                                    ForEach(lesson.conversations.prefix(5)) { conversation in
                                        ConversationPreviewView(conversation: conversation, level: CEFRLevel(rawValue: lesson.levelCode) ?? .a1)
                                    }

                                    if lesson.conversations.count > 5 {
                                        Text("+ \(lesson.conversations.count - 5) more conversations")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                }
                            }
                        }
                    }

                    // Grammar Section - Always show grammar (real or fallback)
                    if (!realGrammar.isEmpty && contentLoaded) || !lesson.grammar.isEmpty {
                        VStack(alignment: .leading, spacing: 16) {
                            Button {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    isGrammarExpanded.toggle()
                                }
                            } label: {
                                HStack {
                                    Text("Grammar (\(contentLoaded && !realGrammar.isEmpty ? realGrammar.count : lesson.grammar.count) topics)")
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.primary)

                                    Spacer()

                                    Image(systemName: isGrammarExpanded ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                                        .font(.title3)
                                        .foregroundColor(levelColor)
                                }
                            }

                            if isGrammarExpanded {
                                if contentLoaded && !realGrammar.isEmpty {
                                    // Show real Supabase grammar
                                    ForEach(Array(realGrammar.enumerated()), id: \.element.id) { index, grammar in
                                        RealGrammarPreviewView(grammar: grammar, level: CEFRLevel(rawValue: lesson.levelCode) ?? .a1)
                                            .onTapGesture {
                                                selectedGrammarIndex = index
                                                showGrammarModal = true
                                            }
                                    }

                                    Text("💡 Tap any grammar topic for detailed explanations and tips")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                        .italic()
                                } else {
                                    // Show fallback grammar
                                    ForEach(lesson.grammar) { grammar in
                                        GrammarPreviewView(grammar: grammar, level: CEFRLevel(rawValue: lesson.levelCode) ?? .a1)
                                    }
                                }
                            }
                        }
                    }

                    // Practice Section - Always show practice (real or fallback)
                    if (!realPractice.isEmpty && contentLoaded) || !lesson.practice.isEmpty {
                        VStack(alignment: .leading, spacing: 16) {
                            Button {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    isPracticeExpanded.toggle()
                                }
                            } label: {
                                HStack {
                                    Text("Practice (\(contentLoaded && !realPractice.isEmpty ? realPractice.count : lesson.practice.count) activities)")
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.primary)

                                    Spacer()

                                    Image(systemName: isPracticeExpanded ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                                        .font(.title3)
                                        .foregroundColor(levelColor)
                                }
                            }

                            if isPracticeExpanded {
                                if contentLoaded && !realPractice.isEmpty {
                                    // Show real Supabase practice
                                    ForEach(Array(realPractice.prefix(5).enumerated()), id: \.element.id) { index, practice in
                                        RealPracticePreviewView(practice: practice, level: CEFRLevel(rawValue: lesson.levelCode) ?? .a1)
                                            .onTapGesture {
                                                selectedPracticeIndex = index
                                                showPracticeModal = true
                                            }
                                    }

                                    if realPractice.count > 5 {
                                        Button {
                                            selectedPracticeIndex = 5
                                            showPracticeModal = true
                                        } label: {
                                            Text("+ \(realPractice.count - 5) more exercises • Tap for details")
                                                .font(.caption)
                                                .foregroundColor(levelColor)
                                                .underline()
                                        }
                                    } else {
                                        Text("💡 Tap any exercise for detailed instructions and preview")
                                            .font(.caption2)
                                            .foregroundColor(.secondary)
                                            .italic()
                                    }
                                } else {
                                    // Show fallback practice
                                    ForEach(lesson.practice.prefix(5)) { practice in
                                        PracticePreviewView(practice: practice, level: CEFRLevel(rawValue: lesson.levelCode) ?? .a1)
                                    }

                                    if lesson.practice.count > 5 {
                                        Text("+ \(lesson.practice.count - 5) more practice activities")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                }
                            }
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("Lesson Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                loadRealContent()
            }
            .sheet(isPresented: $showVocabularyModal) {
                VocabularyDetailModal(
                    vocabularies: contentLoaded && !realVocabulary.isEmpty ? realVocabulary : [],
                    selectedIndex: $selectedVocabIndex,
                    level: CEFRLevel(rawValue: lesson.levelCode) ?? .a1
                )
            }
            .sheet(isPresented: $showConversationModal) {
                // ALWAYS use new ConversationDetailView to see the new features
                if contentLoaded && !realConversations.isEmpty && selectedConversationIndex < realConversations.count {
                    ConversationDetailView(conversation: realConversations[selectedConversationIndex])
                } else {
                    // Create a test conversation to demonstrate the new features
                    ConversationDetailView(conversation: createTestConversationForDemo())
                }
            }
            .sheet(isPresented: $showGrammarModal) {
                if !enhancedGrammarPoints.isEmpty {
                    GrammarDetailView(grammarPoints: enhancedGrammarPoints)
                } else {
                    GrammarDetailModal(
                        grammarTopics: contentLoaded && !realGrammar.isEmpty ? realGrammar : [],
                        selectedIndex: $selectedGrammarIndex,
                        level: CEFRLevel(rawValue: lesson.levelCode) ?? .a1
                    )
                }
            }
            .sheet(isPresented: $showPracticeModal) {
                PracticeDetailModal(
                    practiceExercises: contentLoaded && !realPractice.isEmpty ? realPractice : [],
                    selectedIndex: $selectedPracticeIndex,
                    level: CEFRLevel(rawValue: lesson.levelCode) ?? .a1
                )
            }
        }
    }

    // Create test conversation to demonstrate new features
    private func createTestConversationForDemo() -> TamilSupabaseConversation {
        return TamilSupabaseConversation(
            id: "demo-conversation",
            lessonId: "demo-lesson",
            conversationId: "L1C1",
            titleEnglish: "Meeting a Friend",
            titleTamil: "நண்பரைச் சந்திப்பது",
            contextDescription: "Two friends meet on the street and exchange greetings. This demonstrates the new conversation view with key phrases, romanization, and pronunciation features.",
            participants: "Raj & Priya (Friends)",
            formalityLevel: "informal",
            culturalSetting: "casual_street_meeting",
            audioFullUrl: nil,
            createdAt: "2025-06-24T00:00:00Z"
        )
    }

    // Load real content from Supabase database
    private func loadRealContent() {
        // Try to find a matching lesson in Supabase by level and lesson number
        print("🔄 Searching for real content for \(lesson.levelCode)-\(lesson.lessonNumber)")

        isLoadingContent = true

        Task { @MainActor in
            let supabaseService = SupabaseContentService.shared

            // First, try to get all lessons for this level from Supabase
            await supabaseService.fetchLessons(for: CEFRLevel(rawValue: lesson.levelCode) ?? .a1)

            // Find a matching lesson by title or lesson number
            let matchingLesson = supabaseService.lessons.first(where: { supabaseLesson in
                // Try to match by lesson number first (most reliable)
                if supabaseLesson.lessonNumber == lesson.lessonNumber {
                    return true
                }

                // Try to match by title (case-insensitive, flexible matching)
                let supabaseTitle = supabaseLesson.titleEnglish.lowercased()
                let lessonTitle = lesson.titleEnglish.lowercased()

                // Direct title match
                if supabaseTitle == lessonTitle {
                    return true
                }

                // Partial title match (contains key words)
                if supabaseTitle.contains(lessonTitle) || lessonTitle.contains(supabaseTitle) {
                    return true
                }

                // Match by lesson number in title
                if supabaseTitle.contains("lesson \(lesson.lessonNumber)") ||
                   supabaseTitle.contains("\(lesson.lessonNumber)") {
                    return true
                }

                // Special case for "Basic Greetings" - common lesson title
                if lessonTitle.contains("greeting") && supabaseTitle.contains("greeting") {
                    return true
                }

                return false
            })

            guard let foundLesson = matchingLesson else {
                print("⚠️ No matching Supabase lesson found for \(lesson.levelCode)-\(lesson.lessonNumber)")
                print("📋 Available Supabase lessons:")
                for availableLesson in supabaseService.lessons {
                    print("   - \(availableLesson.lessonNumber): \(availableLesson.titleEnglish)")
                }
                print("🔍 Looking for: \(lesson.lessonNumber): \(lesson.titleEnglish)")

                await MainActor.run {
                    isLoadingContent = false
                    // Keep using fallback content from lesson model
                    print("📝 Using fallback content: \(lesson.vocabulary.count) vocab, \(lesson.conversations.count) conversations, \(lesson.grammar.count) grammar, \(lesson.practice.count) practice")
                }
                return
            }

            let lessonIdString = foundLesson.id
            print("🔄 Loading real content for lesson ID: \(lessonIdString)")

            // Fetch all content types in parallel
            async let vocabulary = supabaseService.fetchVocabulary(for: lessonIdString)
            async let conversations = supabaseService.fetchConversations(for: lessonIdString)
            async let grammarTopics = supabaseService.fetchGrammarTopics(for: lessonIdString)
            async let practiceExercises = supabaseService.fetchPracticeExercises(for: lessonIdString)

            // Wait for all content to load
            let vocabItems = await vocabulary
            let convItems = await conversations
            let grammarItems = await grammarTopics
            let exerciseItems = await practiceExercises

            // Convert grammar data to enhanced format
            let enhancedGrammar = await supabaseService.convertToLessonGrammarPoints(grammarItems)

            await MainActor.run {
                realVocabulary = vocabItems
                realConversations = convItems
                realGrammar = grammarItems

                // Use focused A1 practice exercises for A1 Basic Greetings lesson
                if lesson.levelCode == "A1" && lesson.lessonNumber == 1 {
                    print("🎯 Using focused A1 Basic Greetings practice exercises")
                    realPractice = A1BasicGreetingsPracticeGenerator.generateFocusedA1Exercises()
                } else {
                    realPractice = exerciseItems
                }

                enhancedGrammarPoints = enhancedGrammar
                isLoadingContent = false
                contentLoaded = true

                print("✅ Loaded real content: \(realVocabulary.count) vocab, \(realConversations.count) conversations, \(realGrammar.count) grammar, \(realPractice.count) practice")
                print("✅ Enhanced grammar points: \(enhancedGrammar.count)")
            }


        }
    }


}

// MARK: - Supporting Views

struct RealVocabularyItemView: View {
    let vocabulary: TamilSupabaseVocabulary
    let level: CEFRLevel
    @StateObject private var audioManager = AudioContentManager.shared
    @State private var isExpanded = false

    private var levelColor: Color {
        switch level {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Main vocabulary content
            HStack(spacing: 12) {
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text(vocabulary.englishWord)
                            .font(.subheadline)
                            .fontWeight(.semibold)

                        Text("(\(vocabulary.partOfSpeech ?? ""))")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color(.systemGray5))
                            .cornerRadius(4)
                    }

                    Text(vocabulary.tamilTranslation)
                        .font(.title3)
                        .fontWeight(.medium)
                        .foregroundColor(levelColor)

                    Text(vocabulary.romanization)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                VStack(spacing: 8) {
                    Button {
                        // Play audio if available
                        if vocabulary.audioWordUrl != nil {
                            // audioManager.playAudio(from: audioUrl)
                        }
                    } label: {
                        Image(systemName: "speaker.wave.2.fill")
                            .font(.title3)
                            .foregroundColor(.white)
                            .frame(width: 36, height: 36)
                            .background(levelColor)
                            .clipShape(Circle())
                    }

                    Button {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isExpanded.toggle()
                        }
                    } label: {
                        Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                            .font(.caption)
                            .foregroundColor(levelColor)
                    }
                }
            }

            // Expanded content
            if isExpanded {
                VStack(alignment: .leading, spacing: 10) {
                    Divider()

                    // Example sentence
                    if let exampleEnglish = vocabulary.exampleSentenceEnglish, !exampleEnglish.isEmpty {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Example:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(levelColor)

                            Text(exampleEnglish)
                                .font(.caption)
                                .foregroundColor(.primary)

                            if let exampleTamil = vocabulary.exampleSentenceTamil, !exampleTamil.isEmpty {
                                Text(exampleTamil)
                                    .font(.caption)
                                    .foregroundColor(levelColor)
                            }
                        }
                    }

                    // Cultural notes
                    if let culturalNotes = vocabulary.culturalNotes, !culturalNotes.isEmpty {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Cultural Note:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.orange)

                            Text(culturalNotes)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(levelColor.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: levelColor.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct VocabularyItemView: View {
    let vocabulary: TamilVocabulary
    let level: CEFRLevel
    @StateObject private var audioManager = AudioContentManager.shared
    @State private var isExpanded = false

    private var levelColor: Color {
        switch level {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Main vocabulary content
            HStack(spacing: 12) {
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text(vocabulary.englishWord)
                            .font(.subheadline)
                            .fontWeight(.semibold)

                        Text("(\(vocabulary.partOfSpeech))")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color(.systemGray5))
                            .cornerRadius(4)
                    }

                    Text(vocabulary.tamilTranslation)
                        .font(.title3)
                        .fontWeight(.medium)
                        .foregroundColor(levelColor)

                    HStack {
                        Text(vocabulary.romanization)
                            .font(.caption)
                            .foregroundColor(.secondary)

                        if !vocabulary.ipa.isEmpty {
                            Text("• \(vocabulary.ipa)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }

                Spacer()

                VStack(spacing: 8) {
                    Button {
                        audioManager.playVocabularyWord(vocabulary, level: level)
                    } label: {
                        Image(systemName: "speaker.wave.2.fill")
                            .font(.title3)
                            .foregroundColor(.white)
                            .frame(width: 36, height: 36)
                            .background(levelColor)
                            .clipShape(Circle())
                    }

                    Button {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isExpanded.toggle()
                        }
                    } label: {
                        Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                            .font(.caption)
                            .foregroundColor(levelColor)
                    }
                }
            }

            // Expanded content
            if isExpanded {
                VStack(alignment: .leading, spacing: 10) {
                    Divider()

                    // Example sentence
                    if !vocabulary.exampleSentenceEnglish.isEmpty {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Example:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(levelColor)

                            Text(vocabulary.exampleSentenceEnglish)
                                .font(.caption)
                                .foregroundColor(.primary)

                            Text(vocabulary.exampleSentenceTamil)
                                .font(.caption)
                                .foregroundColor(levelColor)

                            if !vocabulary.exampleSentenceRomanization.isEmpty {
                                Text(vocabulary.exampleSentenceRomanization)
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                    .italic()
                            }
                        }
                    }

                    // Cultural notes
                    if !vocabulary.culturalNotes.isEmpty {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Cultural Note:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.orange)

                            Text(vocabulary.culturalNotes)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    // Related terms
                    if !vocabulary.relatedTerms.isEmpty {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Related:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.purple)

                            Text(vocabulary.relatedTerms)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(levelColor.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: levelColor.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct ConversationPreviewView: View {
    let conversation: TamilConversation
    let level: CEFRLevel
    @State private var isExpanded = false
    @StateObject private var audioManager = AudioContentManager.shared

    private var levelColor: Color {
        switch level {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }

    private var formalityColor: Color {
        switch conversation.formality.lowercased() {
        case "formal": return .blue
        case "informal": return .green
        case "semi-formal": return .orange
        default: return .gray
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(conversation.titleEnglish)
                        .font(.subheadline)
                        .fontWeight(.semibold)

                    Spacer()

                    Text(conversation.formality)
                        .font(.caption)
                        .fontWeight(.medium)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(formalityColor.opacity(0.2))
                        .foregroundColor(formalityColor)
                        .cornerRadius(8)
                }

                Text(conversation.titleTamil)
                    .font(.title3)
                    .fontWeight(.medium)
                    .foregroundColor(levelColor)

                HStack {
                    Text(conversation.context)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(isExpanded ? nil : 2)

                    Spacer()

                    Button {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isExpanded.toggle()
                        }
                    } label: {
                        Image(systemName: isExpanded ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                            .font(.title3)
                            .foregroundColor(levelColor)
                    }
                }

                if !conversation.participants.isEmpty {
                    Text("Participants: \(conversation.participants)")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color(.systemGray6))
                        .cornerRadius(6)
                }
            }

            // Dialogue preview
            if isExpanded && !conversation.dialogue.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    Divider()

                    Text("Dialogue Preview:")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(levelColor)

                    LazyVStack(spacing: 8) {
                        ForEach(conversation.dialogue.prefix(3)) { dialogue in
                            DialogueLineView(dialogue: dialogue, levelColor: levelColor)
                        }

                        if conversation.dialogue.count > 3 {
                            Text("+ \(conversation.dialogue.count - 3) more lines")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                                .padding(.top, 4)
                        }
                    }

                    if !conversation.educationalIntegration.isEmpty {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Learning Focus:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.purple)

                            Text(conversation.educationalIntegration)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.top, 8)
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(levelColor.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: levelColor.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct DialogueLineView: View {
    let dialogue: TamilDialogue
    let levelColor: Color
    @StateObject private var audioManager = AudioContentManager.shared

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // Speaker indicator
            Text(dialogue.speaker)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(levelColor)
                .cornerRadius(8)
                .frame(minWidth: 50)

            VStack(alignment: .leading, spacing: 4) {
                Text(dialogue.lineEnglish)
                    .font(.caption)
                    .foregroundColor(.primary)

                Text(dialogue.lineTamil)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(levelColor)

                if !dialogue.lineRomanization.isEmpty {
                    Text(dialogue.lineRomanization)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .italic()
                }
            }

            Spacer()

            if !dialogue.audioUrl.isEmpty {
                Button {
                    // Play dialogue audio
                    print("Playing audio: \(dialogue.audioUrl)")
                } label: {
                    Image(systemName: "play.circle.fill")
                        .font(.title3)
                        .foregroundColor(levelColor)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6).opacity(0.5))
        .cornerRadius(12)
    }
}

struct GrammarPreviewView: View {
    let grammar: TamilGrammar
    let level: CEFRLevel
    @State private var isExpanded = false

    private var levelColor: Color {
        switch level {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }

    private var difficultyColor: Color {
        switch grammar.difficulty.lowercased() {
        case "beginner": return .green
        case "intermediate": return .orange
        case "advanced": return .red
        default: return .gray
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(grammar.titleEnglish)
                        .font(.subheadline)
                        .fontWeight(.semibold)

                    Spacer()

                    Text(grammar.difficulty)
                        .font(.caption)
                        .fontWeight(.medium)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(difficultyColor.opacity(0.2))
                        .foregroundColor(difficultyColor)
                        .cornerRadius(8)
                }

                Text(grammar.titleTamil)
                    .font(.title3)
                    .fontWeight(.medium)
                    .foregroundColor(levelColor)

                HStack {
                    Text(grammar.ruleEnglish)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(isExpanded ? nil : 2)

                    Spacer()

                    Button {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isExpanded.toggle()
                        }
                    } label: {
                        Image(systemName: isExpanded ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                            .font(.title3)
                            .foregroundColor(levelColor)
                    }
                }
            }

            // Expanded content
            if isExpanded {
                VStack(alignment: .leading, spacing: 12) {
                    Divider()

                    // Rule in Tamil
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Tamil Rule:")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(levelColor)

                        Text(grammar.ruleTamil)
                            .font(.caption)
                            .foregroundColor(levelColor)
                    }

                    // Explanation
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Explanation:")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)

                        Text(grammar.explanation)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    // Examples
                    if !grammar.examples.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Examples:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.purple)

                            ForEach(grammar.examples.prefix(2)) { example in
                                GrammarExampleView(example: example, levelColor: levelColor)
                            }

                            if grammar.examples.count > 2 {
                                Text("+ \(grammar.examples.count - 2) more examples")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }

                    // Tips
                    if !grammar.tips.isEmpty {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Tips:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.orange)

                            Text(grammar.tips)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(levelColor.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: levelColor.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct GrammarExampleView: View {
    let example: TamilGrammarExample
    let levelColor: Color
    @StateObject private var audioManager = AudioContentManager.shared

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(example.exampleEnglish)
                        .font(.caption)
                        .foregroundColor(.primary)

                    Text(example.exampleTamil)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(levelColor)

                    if !example.exampleRomanization.isEmpty {
                        Text(example.exampleRomanization)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .italic()
                    }
                }

                Spacer()

                if !example.audioUrl.isEmpty {
                    Button {
                        print("Playing grammar example audio: \(example.audioUrl)")
                    } label: {
                        Image(systemName: "play.circle.fill")
                            .font(.title3)
                            .foregroundColor(levelColor)
                    }
                }
            }

            if !example.breakdown.isEmpty {
                Text("Breakdown: \(example.breakdown)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .padding(.top, 2)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6).opacity(0.5))
        .cornerRadius(12)
    }
}

struct PracticePreviewView: View {
    let practice: TamilPractice
    let level: CEFRLevel
    @State private var isExpanded = false

    private var levelColor: Color {
        switch level {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }

    private var typeColor: Color {
        switch practice.type.lowercased() {
        case "sentence_building": return .blue
        case "pronunciation": return .green
        case "listening": return .purple
        case "writing": return .orange
        default: return .gray
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(practice.titleEnglish)
                        .font(.subheadline)
                        .fontWeight(.semibold)

                    Spacer()

                    Text(practice.type.replacingOccurrences(of: "_", with: " ").capitalized)
                        .font(.caption)
                        .fontWeight(.medium)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(typeColor.opacity(0.2))
                        .foregroundColor(typeColor)
                        .cornerRadius(8)
                }

                Text(practice.titleTamil)
                    .font(.title3)
                    .fontWeight(.medium)
                    .foregroundColor(levelColor)

                HStack {
                    Text(practice.instructionsEnglish)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(isExpanded ? nil : 2)

                    Spacer()

                    Button {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isExpanded.toggle()
                        }
                    } label: {
                        Image(systemName: isExpanded ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                            .font(.title3)
                            .foregroundColor(levelColor)
                    }
                }

                HStack {
                    Image(systemName: "target")
                        .font(.caption)
                        .foregroundColor(levelColor)

                    Text("\(practice.exercises.count) exercises")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()

                    Text(practice.difficulty)
                        .font(.caption2)
                        .fontWeight(.medium)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(levelColor.opacity(0.2))
                        .foregroundColor(levelColor)
                        .cornerRadius(6)
                }
            }

            // Expanded content
            if isExpanded {
                VStack(alignment: .leading, spacing: 12) {
                    Divider()

                    // Instructions in Tamil
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Tamil Instructions:")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(levelColor)

                        Text(practice.instructionsTamil)
                            .font(.caption)
                            .foregroundColor(levelColor)
                    }

                    // Exercise preview
                    if !practice.exercises.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Exercise Preview:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.purple)

                            ForEach(practice.exercises.prefix(1)) { exercise in
                                PracticeExerciseView(exercise: exercise, levelColor: levelColor)
                            }

                            if practice.exercises.count > 1 {
                                Text("+ \(practice.exercises.count - 1) more exercises")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }

                    // Audio instructions
                    if !practice.audioInstructionsUrl.isEmpty {
                        HStack {
                            Text("Audio Instructions Available")
                                .font(.caption)
                                .foregroundColor(.blue)

                            Spacer()

                            Button {
                                print("Playing practice instructions audio: \(practice.audioInstructionsUrl)")
                            } label: {
                                Image(systemName: "speaker.wave.2.fill")
                                    .font(.caption)
                                    .foregroundColor(.blue)
                            }
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(8)
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(levelColor.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: levelColor.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct PracticeExerciseView: View {
    let exercise: TamilPracticeExercise
    let levelColor: Color
    @StateObject private var audioManager = AudioContentManager.shared

    private var exerciseTypeColor: Color {
        switch exercise.exerciseType {
        case "multiple_choice": return .blue
        case "true_false": return .green
        case "fill_blank": return .orange
        case "match_following": return .purple
        default: return .gray
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Exercise type badge
            HStack {
                Text(exercise.exerciseType.replacingOccurrences(of: "_", with: " ").capitalized)
                    .font(.caption2)
                    .fontWeight(.semibold)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(exerciseTypeColor.opacity(0.2))
                    .foregroundColor(exerciseTypeColor)
                    .cornerRadius(6)

                Spacer()

                HStack(spacing: 4) {
                    Image(systemName: "star.fill")
                        .font(.caption2)
                        .foregroundColor(.yellow)

                    Text("\(exercise.points) points")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }

            // Question
            VStack(alignment: .leading, spacing: 4) {
                Text("Question:")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(levelColor)

                Text(exercise.question)
                    .font(.caption)
                    .foregroundColor(.primary)

                if !exercise.questionTamil.isEmpty {
                    Text(exercise.questionTamil)
                        .font(.caption)
                        .foregroundColor(levelColor)
                }
            }

            // Exercise content based on type
            switch exercise.exerciseType {
            case "multiple_choice", "true_false":
                if !exercise.options.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Options:")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(exerciseTypeColor)

                        ForEach(Array(exercise.options.enumerated()), id: \.offset) { index, option in
                            HStack {
                                Text("\(index + 1).")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)

                                Text(option)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .fontWeight(index == exercise.correctAnswer ? .semibold : .regular)
                            }
                        }
                    }
                }

            case "fill_blank":
                if let blanks = exercise.blanks, !blanks.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Fill in the blank:")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(exerciseTypeColor)

                        Text("Answer: \(blanks.joined(separator: ", "))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

            case "match_following":
                if let pairs = exercise.matchPairs, !pairs.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Match pairs:")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(exerciseTypeColor)

                        ForEach(pairs.prefix(2)) { pair in
                            HStack {
                                Text(pair.left)
                                    .font(.caption)
                                    .foregroundColor(levelColor)

                                Image(systemName: "arrow.right")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)

                                Text(pair.right)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }

                        if pairs.count > 2 {
                            Text("+ \(pairs.count - 2) more pairs")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }

            default:
                EmptyView()
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6).opacity(0.5))
        .cornerRadius(12)
    }
}

// MARK: - Real Supabase Content Preview Views

struct RealConversationPreviewView: View {
    let conversation: TamilSupabaseConversation
    let level: CEFRLevel
    @State private var isExpanded = false
    @StateObject private var audioManager = AudioContentManager.shared

    private var levelColor: Color {
        switch level {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(conversation.titleEnglish)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    if !conversation.titleTamil.isEmpty {
                        Text(conversation.titleTamil)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(levelColor)
                    }

                    if let contextDescription = conversation.contextDescription, !contextDescription.isEmpty {
                        Text(contextDescription)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                Button {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isExpanded.toggle()
                    }
                } label: {
                    Image(systemName: isExpanded ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                        .font(.title3)
                        .foregroundColor(levelColor)
                }
            }

            // Expanded content
            if isExpanded {
                VStack(alignment: .leading, spacing: 8) {
                    Divider()

                    // Conversation details
                    if let participants = conversation.participants, !participants.isEmpty {
                        VStack(alignment: .leading, spacing: 6) {
                            Text("Participants:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)

                            Text(participants)
                                .font(.caption)
                                .foregroundColor(.primary)
                        }
                    }

                    if let formalityLevel = conversation.formalityLevel, !formalityLevel.isEmpty {
                        VStack(alignment: .leading, spacing: 6) {
                            Text("Formality Level:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(levelColor)

                            Text(formalityLevel.capitalized)
                                .font(.caption)
                                .foregroundColor(levelColor)
                        }
                    }

                    if let culturalSetting = conversation.culturalSetting, !culturalSetting.isEmpty {
                        VStack(alignment: .leading, spacing: 6) {
                            Text("Cultural Setting:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.orange)

                            Text(culturalSetting)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(levelColor.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: levelColor.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct RealGrammarPreviewView: View {
    let grammar: TamilSupabaseGrammarTopic
    let level: CEFRLevel
    @State private var isExpanded = false

    private var levelColor: Color {
        switch level {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(grammar.titleEnglish)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(grammar.titleTamil)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(levelColor)
                }

                Spacer()

                Button {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isExpanded.toggle()
                    }
                } label: {
                    Image(systemName: isExpanded ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                        .font(.title3)
                        .foregroundColor(levelColor)
                }
            }

            // Expanded content
            if isExpanded {
                VStack(alignment: .leading, spacing: 8) {
                    Divider()

                    // Rule
                    VStack(alignment: .leading, spacing: 6) {
                        Text("Rule:")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)

                        Text(grammar.ruleEnglish)
                            .font(.caption)
                            .foregroundColor(.primary)

                        Text(grammar.ruleTamil)
                            .font(.caption)
                            .foregroundColor(levelColor)
                    }

                    // Explanation
                    if let explanation = grammar.explanation, !explanation.isEmpty {
                        VStack(alignment: .leading, spacing: 6) {
                            Text("Explanation:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.secondary)

                            Text(explanation)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    // Tips
                    if let tips = grammar.tips, !tips.isEmpty {
                        VStack(alignment: .leading, spacing: 6) {
                            Text("Tips:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.orange)

                            ForEach(tips.prefix(2), id: \.self) { tip in
                                Text("• \(tip)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(levelColor.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: levelColor.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct RealPracticePreviewView: View {
    let practice: TamilSupabasePracticeExercise
    let level: CEFRLevel
    @State private var isExpanded = false

    private var levelColor: Color {
        switch level {
        case .a1: return .green
        case .a2: return .blue
        case .b1: return .orange
        case .b2: return .purple
        case .c1: return .red
        case .c2: return .indigo
        }
    }

    private var exerciseTypeColor: Color {
        switch practice.exerciseType.lowercased() {
        case "multiple_choice": return .blue
        case "true_false": return .green
        case "fill_blank": return .orange
        case "match_following": return .purple
        default: return .gray
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(practice.exerciseType.replacingOccurrences(of: "_", with: " ").capitalized)
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(exerciseTypeColor)
                            .cornerRadius(8)

                        Spacer()
                    }

                    Text(practice.titleEnglish)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .foregroundColor(levelColor)
                }

                Spacer()

                Button {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isExpanded.toggle()
                    }
                } label: {
                    Image(systemName: isExpanded ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                        .font(.title3)
                        .foregroundColor(levelColor)
                }
            }

            // Expanded content
            if isExpanded {
                VStack(alignment: .leading, spacing: 8) {
                    Divider()

                    // Instructions
                    VStack(alignment: .leading, spacing: 6) {
                        Text("Instructions:")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)

                        Text(practice.instructionsEnglish)
                            .font(.caption)
                            .foregroundColor(.primary)

                        Text(practice.instructionsTamil)
                            .font(.caption)
                            .foregroundColor(levelColor)
                    }

                    // Exercise details
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Difficulty:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.secondary)

                            Text("Level \(practice.difficultyLevel)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        VStack(alignment: .trailing, spacing: 4) {
                            Text("Points:")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.orange)

                            Text("\(practice.pointsValue)")
                                .font(.caption)
                                .foregroundColor(.orange)
                        }

                        if let timeLimit = practice.timeLimitSeconds {
                            Spacer()

                            VStack(alignment: .trailing, spacing: 4) {
                                Text("Time Limit:")
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.blue)

                                Text("\(timeLimit)s")
                                    .font(.caption)
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(levelColor.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: levelColor.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}