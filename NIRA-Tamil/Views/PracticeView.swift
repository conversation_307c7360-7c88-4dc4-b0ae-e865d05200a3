import SwiftUI

struct PracticeView: View {
    @State private var selectedSubTab = 0
    private let selectedLanguage = UserPreferencesService.shared.selectedLanguage
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Adaptive sub-tab picker based on language
                Picker("Practice Type", selection: $selectedSubTab) {
                    if selectedLanguage.requiresScriptLiteracy {
                        Text("Read").tag(0)
                        Text("Write").tag(1)
                    } else {
                        Text("Listen").tag(0) 
                        Text("Speak").tag(1)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding(.horizontal)
                .padding(.bottom, 10)
                
                // Content based on selected sub-tab and language type
                TabView(selection: $selectedSubTab) {
                    if selectedLanguage.requiresScriptLiteracy {
                        // Script-based language practice
                        EnhancedReadingPracticeView()
                            .tag(0)
                        
                        EnhancedWritingPracticeView()
                            .tag(1)
                    } else {
                        // Audio-based language practice  
                        EnhancedListeningPracticeView()
                            .tag(0)
                        
                        EnhancedSpeakingPracticeView()
                            .tag(1)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Practice")
        }
    }
}

struct EnhancedReadingPracticeView: View {
    var body: some View {
        ReadingContentView()
    }
}

struct EnhancedWritingPracticeView: View {
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Header section
                VStack(alignment: .leading, spacing: 8) {
                    Text("Writing Practice")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    Text("Master Tamil script with guided practice")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                .padding(.top)
                
                EnhancedPracticeCard(
                    title: "Stroke Order Practice",
                    description: "Learn proper stroke order for Tamil letters",
                    difficulty: "Beginner", 
                    timeEstimate: "7 mins",
                    exerciseCount: 10,
                    completedCount: 3,
                    type: .writing,
                    gradient: LinearGradient(
                        colors: [Color.orange.opacity(0.8), Color.red.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                
                EnhancedPracticeCard(
                    title: "Character Recognition",
                    description: "Write characters shown on screen",
                    difficulty: "Intermediate",
                    timeEstimate: "12 mins",
                    exerciseCount: 18,
                    completedCount: 9,
                    type: .writing,
                    gradient: LinearGradient(
                        colors: [Color.indigo.opacity(0.8), Color.purple.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            }
            .padding()
        }
    }
}

struct EnhancedListeningPracticeView: View {
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Header section
                VStack(alignment: .leading, spacing: 8) {
                    Text("Listening Practice")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    Text("Develop your listening comprehension")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                .padding(.top)
                
                EnhancedPracticeCard(
                    title: "Basic Pronunciation",
                    description: "Listen and identify different sounds",
                    difficulty: "Beginner",
                    timeEstimate: "6 mins",
                    exerciseCount: 14,
                    completedCount: 10,
                    type: .listening,
                    gradient: LinearGradient(
                        colors: [Color.mint.opacity(0.8), Color.cyan.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                
                EnhancedPracticeCard(
                    title: "Word Recognition",
                    description: "Identify words from audio clips",
                    difficulty: "Intermediate",
                    timeEstimate: "9 mins",
                    exerciseCount: 16,
                    completedCount: 4,
                    type: .listening,
                    gradient: LinearGradient(
                        colors: [Color.teal.opacity(0.8), Color.green.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            }
            .padding()
        }
    }
}

struct EnhancedSpeakingPracticeView: View {
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Header section
                VStack(alignment: .leading, spacing: 8) {
                    Text("Speaking Practice")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    Text("Practice pronunciation and fluency")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                .padding(.top)
                
                EnhancedPracticeCard(
                    title: "Pronunciation Drills",
                    description: "Practice proper pronunciation of words",
                    difficulty: "Beginner",
                    timeEstimate: "8 mins",
                    exerciseCount: 12,
                    completedCount: 7,
                    type: .speaking,
                    gradient: LinearGradient(
                        colors: [Color.pink.opacity(0.8), Color.purple.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                
                EnhancedPracticeCard(
                    title: "Conversation Practice",
                    description: "Speak in context with AI feedback",
                    difficulty: "Advanced",
                    timeEstimate: "15 mins",
                    exerciseCount: 8,
                    completedCount: 2,
                    type: .speaking,
                    gradient: LinearGradient(
                        colors: [Color.orange.opacity(0.8), Color.yellow.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            }
            .padding()
        }
    }
}

struct EnhancedPracticeCard: View {
    let title: String
    let description: String
    let difficulty: String
    let timeEstimate: String
    let exerciseCount: Int
    let completedCount: Int
    let type: PracticeType
    let gradient: LinearGradient
    
    enum PracticeType: String {
        case reading, writing, listening, speaking
        
        var icon: String {
            switch self {
            case .reading: return "book.pages"
            case .writing: return "pencil"
            case .listening: return "ear"
            case .speaking: return "mic"
            }
        }
        
        var color: Color {
            switch self {
            case .reading: return .blue
            case .writing: return .orange
            case .listening: return .green
            case .speaking: return .purple
            }
        }
    }
    
    var body: some View {
        ZStack {
            // Glow effect background
            RoundedRectangle(cornerRadius: 20)
                .fill(gradient)
                .blur(radius: 8)
                .opacity(0.3)
                .scaleEffect(1.05)
            
            VStack(alignment: .leading, spacing: 12) {
                // Top accent bar
                Rectangle()
                    .fill(gradient)
                    .frame(height: 4)
                    .frame(maxWidth: .infinity)
                
                VStack(alignment: .leading, spacing: 12) {
                    // Header with badges
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Image(systemName: type.icon)
                                    .foregroundColor(type.color)
                                Text(type.rawValue.capitalized)
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(type.color)
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(type.color.opacity(0.1))
                            .cornerRadius(8)
                            
                            HStack {
                                ForEach(0..<3) { index in
                                    Image(systemName: "star.fill")
                                        .font(.caption2)
                                        .foregroundColor(index < difficultyStars ? .yellow : .gray.opacity(0.3))
                                }
                            }
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .trailing, spacing: 2) {
                            HStack {
                                Image(systemName: "clock")
                                    .font(.caption)
                                Text(timeEstimate)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Text("\(exerciseCount) exercises")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // Title and description
                    Text(title)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text(description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    // Progress section
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("Progress")
                                .font(.caption)
                                .fontWeight(.medium)
                            Spacer()
                            Text("\(completedCount)/\(exerciseCount)")
                                .font(.caption)
                        }
                        
                        ProgressView(value: Double(completedCount), total: Double(exerciseCount))
                            .progressViewStyle(LinearProgressViewStyle(tint: type.color))
                            .scaleEffect(y: 0.5)
                    }
                    
                    // Action button
                    HStack {
                        Spacer()
                        Button(action: {
                            // Action to start practice
                        }) {
                            HStack {
                                Image(systemName: completedCount == exerciseCount ? "checkmark" : "play.fill")
                                    .font(.caption)
                                Text(completedCount == exerciseCount ? "Review" : "Continue")
                                    .font(.caption)
                                    .fontWeight(.medium)
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(completedCount == exerciseCount ? Color.green : type.color)
                            .cornerRadius(16)
                        }
                        Spacer()
                    }
                }
                .padding()
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.systemBackground)
                .shadow(color: Color.gray.opacity(0.2), radius: 8, x: 0, y: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }
    
    private var difficultyStars: Int {
        switch difficulty.lowercased() {
        case "beginner": return 1
        case "intermediate": return 2
        case "advanced": return 3
        default: return 1
        }
    }
}

#Preview {
    PracticeView()
} 