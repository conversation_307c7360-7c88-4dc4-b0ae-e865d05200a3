//
//  SyncStatusView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import SwiftUI

struct SyncStatusView: View {
    @StateObject private var syncManager = CrossDeviceSyncManager.shared
    @StateObject private var cloudKitService = CloudKitSyncService.shared
    #if os(watchOS)
    @StateObject private var watchConnectivity = WatchConnectivityService.shared
    #endif
    
    @State private var showingSyncDetails = false
    @State private var showingConflictResolution = false
    
    var body: some View {
        NavigationView {
            List {
                // Sync Status Section
                Section {
                    syncStatusCard
                } header: {
                    Text("Sync Status")
                }
                
                // Device Status Section
                Section {
                    deviceStatusRows
                } header: {
                    Text("Connected Devices")
                }
                
                // Sync Controls Section
                Section {
                    syncControlsRows
                } header: {
                    Text("Sync Controls")
                }
                
                // Conflicts Section
                if !syncManager.syncConflicts.isEmpty {
                    Section {
                        conflictRows
                    } header: {
                        Text("Sync Conflicts")
                    }
                }
                
                // Sync History Section
                Section {
                    syncHistoryRows
                } header: {
                    Text("Recent Activity")
                }
            }
            .navigationTitle("Sync & Backup")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                await syncManager.performFullSync()
            }
            .sheet(isPresented: $showingSyncDetails) {
                SyncDetailsView()
            }
            .sheet(isPresented: $showingConflictResolution) {
                ConflictResolutionView()
            }
        }
    }
    
    private var syncStatusCard: some View {
        VStack(spacing: 12) {
            HStack {
                syncStatusIcon
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(syncManager.getSyncStatusText())
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    if case .syncing = syncManager.syncStatus {
                        Text("Syncing your writing progress...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    } else if let lastSync = syncManager.lastSyncDate {
                        Text("Last synced: \(lastSync, style: .relative)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                if case .syncing = syncManager.syncStatus {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            
            // Sync progress bar
            if case .syncing = syncManager.syncStatus {
                ProgressView(value: cloudKitService.syncProgress, total: 1.0)
                    .progressViewStyle(LinearProgressViewStyle(tint: .blue))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(syncStatusBackgroundColor)
        )
        .onTapGesture {
            showingSyncDetails = true
        }
    }
    
    private var syncStatusIcon: some View {
        Group {
            switch syncManager.syncStatus {
            case .idle:
                Image(systemName: "icloud")
                    .foregroundColor(.blue)
            case .syncing:
                Image(systemName: "icloud.and.arrow.up")
                    .foregroundColor(.blue)
            case .success:
                Image(systemName: "icloud.and.arrow.up.fill")
                    .foregroundColor(.green)
            case .error:
                Image(systemName: "icloud.slash")
                    .foregroundColor(.red)
            }
        }
        .font(.title2)
    }
    
    private var syncStatusBackgroundColor: Color {
        switch syncManager.syncStatus {
        case .idle:
            return Color(.secondarySystemBackground)
        case .syncing:
            return Color.blue.opacity(0.1)
        case .success:
            return Color.green.opacity(0.1)
        case .error:
            return Color.red.opacity(0.1)
        }
    }
    
    private var deviceStatusRows: some View {
        Group {
            DeviceStatusRow(
                deviceName: "iPhone",
                deviceIcon: "iphone",
                isConnected: true,
                lastSync: syncManager.lastSyncDate
            )
            
            DeviceStatusRow(
                deviceName: "iPad",
                deviceIcon: "ipad",
                isConnected: true,
                lastSync: syncManager.lastSyncDate
            )
            
            #if os(watchOS)
            DeviceStatusRow(
                deviceName: "Apple Watch",
                deviceIcon: "applewatch",
                isConnected: watchConnectivity.isConnected,
                lastSync: syncManager.lastSyncDate
            )
            #else
            DeviceStatusRow(
                deviceName: "Apple Watch",
                deviceIcon: "applewatch",
                isConnected: false,
                lastSync: syncManager.lastSyncDate
            )
            #endif
            
            DeviceStatusRow(
                deviceName: "iCloud",
                deviceIcon: "icloud",
                isConnected: cloudKitService.isOnline,
                lastSync: cloudKitService.lastSyncDate
            )
        }
    }
    
    private var syncControlsRows: some View {
        Group {
            // Auto-sync toggle
            HStack {
                Image(systemName: "arrow.triangle.2.circlepath")
                    .foregroundColor(.blue)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Auto Sync")
                        .font(.body)
                    
                    Text("Automatically sync across devices")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Toggle("", isOn: Binding(
                    get: { syncManager.autoSyncEnabled },
                    set: { syncManager.enableAutoSync($0) }
                ))
            }
            
            // Manual sync button
            Button(action: {
                Task {
                    await syncManager.performFullSync()
                }
            }) {
                HStack {
                    Image(systemName: "arrow.clockwise")
                        .foregroundColor(.blue)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Sync Now")
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Text("Force sync all devices")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    if case .syncing = syncManager.syncStatus {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .disabled(case .syncing = syncManager.syncStatus)
            
            // Sync details
            Button(action: {
                showingSyncDetails = true
            }) {
                HStack {
                    Image(systemName: "info.circle")
                        .foregroundColor(.blue)
                        .frame(width: 24)
                    
                    Text("Sync Details")
                        .font(.body)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    private var conflictRows: some View {
        ForEach(syncManager.syncConflicts) { conflict in
            ConflictRow(
                conflict: conflict,
                onResolve: { useLocal in
                    Task {
                        await syncManager.resolveSyncConflict(conflict, useLocal: useLocal)
                    }
                }
            )
        }
    }
    
    private var syncHistoryRows: some View {
        Group {
            if let lastSync = syncManager.lastSyncDate {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Full Sync Completed")
                            .font(.body)
                        
                        Text(lastSync, style: .relative)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                }
            } else {
                HStack {
                    Image(systemName: "clock")
                        .foregroundColor(.orange)
                        .frame(width: 24)
                    
                    Text("No sync history")
                        .font(.body)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
            }
        }
    }
}

// MARK: - Device Status Row

struct DeviceStatusRow: View {
    let deviceName: String
    let deviceIcon: String
    let isConnected: Bool
    let lastSync: Date?
    
    var body: some View {
        HStack {
            Image(systemName: deviceIcon)
                .foregroundColor(isConnected ? .blue : .gray)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(deviceName)
                    .font(.body)
                
                Text(statusText)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Circle()
                .fill(isConnected ? Color.green : Color.gray)
                .frame(width: 8, height: 8)
        }
    }
    
    private var statusText: String {
        if isConnected {
            if let lastSync = lastSync {
                return "Synced \(lastSync, style: .relative)"
            } else {
                return "Connected"
            }
        } else {
            return "Not connected"
        }
    }
}

// MARK: - Conflict Row

struct ConflictRow: View {
    let conflict: CrossDeviceSyncManager.SyncConflict
    let onResolve: (Bool) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.orange)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(conflictTitle)
                        .font(.body)
                        .fontWeight(.semibold)
                    
                    Text("Data conflict detected")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            HStack(spacing: 12) {
                Button("Use Local") {
                    onResolve(true)
                }
                .buttonStyle(.bordered)
                
                Button("Use Remote") {
                    onResolve(false)
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .padding(.vertical, 4)
    }
    
    private var conflictTitle: String {
        switch conflict.type {
        case .writingProgress:
            return "Writing Progress Conflict"
        case .userPreferences:
            return "Settings Conflict"
        case .sessionData:
            return "Session Data Conflict"
        }
    }
}

// MARK: - Sync Details View

struct SyncDetailsView: View {
    @StateObject private var syncManager = CrossDeviceSyncManager.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                Section("Sync Statistics") {
                    DetailRow(label: "Total Characters Synced", value: "247")
                    DetailRow(label: "Writing Sessions Synced", value: "156")
                    DetailRow(label: "Data Size", value: "2.3 MB")
                    DetailRow(label: "Last Full Sync", value: syncManager.lastSyncDate?.formatted() ?? "Never")
                }
                
                Section("Sync Settings") {
                    DetailRow(label: "Auto Sync", value: syncManager.autoSyncEnabled ? "Enabled" : "Disabled")
                    DetailRow(label: "Sync Frequency", value: "Every 5 minutes")
                    DetailRow(label: "Background Sync", value: "Enabled")
                }
                
                Section("Troubleshooting") {
                    Button("Reset Sync Data") {
                        // Implementation for resetting sync data
                    }
                    .foregroundColor(.red)
                    
                    Button("Force Full Sync") {
                        Task {
                            await syncManager.performFullSync()
                        }
                    }
                }
            }
            .navigationTitle("Sync Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct DetailRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
                .foregroundColor(.primary)
            
            Spacer()
            
            Text(value)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Conflict Resolution View

struct ConflictResolutionView: View {
    @StateObject private var syncManager = CrossDeviceSyncManager.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                if syncManager.syncConflicts.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.green)
                        
                        Text("No Conflicts")
                            .font(.headline)
                        
                        Text("All your data is in sync across devices")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                } else {
                    ForEach(syncManager.syncConflicts) { conflict in
                        ConflictDetailView(
                            conflict: conflict,
                            onResolve: { useLocal in
                                Task {
                                    await syncManager.resolveSyncConflict(conflict, useLocal: useLocal)
                                }
                            }
                        )
                    }
                }
            }
            .navigationTitle("Resolve Conflicts")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct ConflictDetailView: View {
    let conflict: CrossDeviceSyncManager.SyncConflict
    let onResolve: (Bool) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(conflictTitle)
                .font(.headline)
                .fontWeight(.semibold)
            
            Text("Choose which version to keep:")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            HStack(spacing: 16) {
                Button("Keep Local") {
                    onResolve(true)
                }
                .buttonStyle(.bordered)
                .frame(maxWidth: .infinity)
                
                Button("Keep Remote") {
                    onResolve(false)
                }
                .buttonStyle(.borderedProminent)
                .frame(maxWidth: .infinity)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }
    
    private var conflictTitle: String {
        switch conflict.type {
        case .writingProgress:
            return "Writing Progress Conflict"
        case .userPreferences:
            return "User Preferences Conflict"
        case .sessionData:
            return "Session Data Conflict"
        }
    }
}

#Preview {
    SyncStatusView()
}
