//
//  UserProgressView.swift
//  NIRA-Tamil
//
//  Created by AI Assistant on 2024-12-19.
//

import SwiftUI

struct UserProgressView: View {
    @StateObject private var progressService = UserProgressService.shared
    @State private var showingProgressDetail = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Image(systemName: "chart.line.uptrend.xyaxis")
                            .font(.system(size: 50))
                            .foregroundColor(.niraPrimary)
                        
                        Text("Learning Progress")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("Track your Tamil learning journey")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    
                    // Sync Status
                    HStack {
                        Circle()
                            .fill(syncStatusColor)
                            .frame(width: 12, height: 12)
                        
                        Text(syncStatusText)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        if progressService.syncStatus == .syncing {
                            ProgressView()
                                .scaleEffect(0.7)
                        }
                    }
                    .padding(.horizontal)
                    
                    // Progress Overview
                    if let userProgress = progressService.userProgress {
                        ProgressOverviewCard(progress: userProgress)
                    } else {
                        EmptyProgressCard()
                    }
                    
                    // Quick Actions
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Quick Actions")
                            .font(.headline)
                            .padding(.horizontal)
                        
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                            ProgressActionButton(
                                title: "Initialize Progress",
                                icon: "play.circle.fill",
                                color: .green,
                                action: {
                                    Task {
                                        await progressService.initializeUserProgress()
                                    }
                                }
                            )

                            ProgressActionButton(
                                title: "Sync Progress",
                                icon: "arrow.triangle.2.circlepath",
                                color: .blue,
                                action: {
                                    Task {
                                        await progressService.syncWithSupabase()
                                    }
                                }
                            )

                            ProgressActionButton(
                                title: "Complete Test Lesson",
                                icon: "checkmark.circle.fill",
                                color: .orange,
                                action: {
                                    Task {
                                        await progressService.completeLessonProgress(
                                            lessonId: "a1-lesson-1",
                                            score: 85,
                                            timeSpentMinutes: 15
                                        )
                                    }
                                }
                            )

                            ProgressActionButton(
                                title: "View Details",
                                icon: "info.circle.fill",
                                color: .purple,
                                action: {
                                    showingProgressDetail = true
                                }
                            )
                        }
                        .padding(.horizontal)
                    }
                    
                    // Recent Lessons
                    if !progressService.lessonProgress.isEmpty {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Recent Lessons")
                                .font(.headline)
                                .padding(.horizontal)
                            
                            ForEach(Array(progressService.lessonProgress.values.prefix(5)), id: \.id) { lesson in
                                LessonProgressCard(lessonProgress: lesson)
                            }
                            .padding(.horizontal)
                        }
                    }
                    
                    Spacer(minLength: 50)
                }
            }
            .navigationTitle("Progress")
            .navigationBarTitleDisplayMode(.inline)
            .refreshable {
                await progressService.loadProgressFromSupabase()
            }
        }
        .sheet(isPresented: $showingProgressDetail) {
            ProgressDetailView()
        }
        .task {
            if progressService.userProgress == nil {
                await progressService.initializeUserProgress()
            }
        }
    }
    
    private var syncStatusColor: Color {
        switch progressService.syncStatus {
        case .idle: return .gray
        case .syncing: return .yellow
        case .synced: return .green
        case .failed: return .red
        }
    }
    
    private var syncStatusText: String {
        switch progressService.syncStatus {
        case .idle: return "Ready to sync"
        case .syncing: return "Syncing..."
        case .synced: return "Synced"
        case .failed: return "Sync failed"
        }
    }
}

struct ProgressOverviewCard: View {
    let progress: SupabaseUserProgressData
    
    var body: some View {
        VStack(spacing: 16) {
            // Main Stats
            HStack(spacing: 20) {
                ProgressStatItem(
                    title: "Lessons",
                    value: "\(progress.totalLessonsCompleted)",
                    icon: "book.fill",
                    color: .blue
                )

                ProgressStatItem(
                    title: "Streak",
                    value: "\(progress.currentStreak)",
                    icon: "flame.fill",
                    color: .orange
                )

                ProgressStatItem(
                    title: "Study Time",
                    value: "\(progress.totalStudyTimeMinutes)m",
                    icon: "clock.fill",
                    color: .green
                )
            }
            
            // Weekly Progress
            VStack(spacing: 8) {
                HStack {
                    Text("Weekly Goal")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Text("\(progress.weeklyProgress)/\(progress.weeklyGoal)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                ProgressView(value: Double(progress.weeklyProgress), total: Double(progress.weeklyGoal))
                    .progressViewStyle(LinearProgressViewStyle(tint: .niraPrimary))
            }
            
            // Current Level
            HStack {
                Text("Current Level")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Text(progress.currentLevel.rawValue)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.niraPrimary)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(Color.niraPrimary.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .padding(.horizontal)
    }
}

struct EmptyProgressCard: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "chart.bar.doc.horizontal")
                .font(.system(size: 40))
                .foregroundColor(.secondary)
            
            Text("No Progress Yet")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text("Start learning to track your progress")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(40)
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .padding(.horizontal)
    }
}

struct ProgressStatItem: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct ProgressActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
    }
}

struct LessonProgressCard: View {
    let lessonProgress: LessonProgress
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Lesson \(lessonProgress.lessonId)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("Score: \(lessonProgress.score)% • \(lessonProgress.timeSpentMinutes)m")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if lessonProgress.isCompleted {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
            } else {
                Image(systemName: "circle")
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct ProgressDetailView: View {
    @Environment(\.dismiss) var dismiss
    @StateObject private var progressService = UserProgressService.shared
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    if let progress = progressService.userProgress {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("User Statistics")
                                .font(.headline)
                            
                            ProgressDetailRow(label: "Total Lessons", value: "\(progress.totalLessonsCompleted)")
                            ProgressDetailRow(label: "Current Streak", value: "\(progress.currentStreak) days")
                            ProgressDetailRow(label: "Longest Streak", value: "\(progress.longestStreak) days")
                            ProgressDetailRow(label: "Study Time", value: "\(progress.totalStudyTimeMinutes) minutes")
                            ProgressDetailRow(label: "Current Level", value: progress.currentLevel.rawValue)
                            ProgressDetailRow(label: "Weekly Goal", value: "\(progress.weeklyGoal) lessons")
                            ProgressDetailRow(label: "Weekly Progress", value: "\(progress.weeklyProgress) lessons")
                            
                            if let lastStudyDate = progress.lastStudyDate {
                                ProgressDetailRow(label: "Last Study", value: formatDate(lastStudyDate))
                            }
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                    }
                    
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Lesson Progress")
                            .font(.headline)
                        
                        ForEach(Array(progressService.lessonProgress.values), id: \.id) { lesson in
                            LessonProgressCard(lessonProgress: lesson)
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("Progress Details")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") { dismiss() })
        }
    }
    
    private func formatDate(_ dateString: String) -> String {
        let formatter = ISO8601DateFormatter()
        if let date = formatter.date(from: dateString) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateStyle = .medium
            displayFormatter.timeStyle = .short
            return displayFormatter.string(from: date)
        }
        return dateString
    }
}

struct ProgressDetailRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
                .font(.subheadline)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.niraPrimary)
        }
    }
}

#Preview {
    UserProgressView()
}
