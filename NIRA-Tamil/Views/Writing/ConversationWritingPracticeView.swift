//
//  ConversationWritingPracticeView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import SwiftUI

struct ConversationWritingPracticeView: View {
    @StateObject private var conversationService = ConversationWritingService.shared
    @StateObject private var scriptService = TamilScriptService.shared
    
    @State private var selectedExercise: ConversationWritingService.ConversationWritingExercise?
    @State private var currentTaskIndex = 0
    @State private var showingWritingCanvas = false
    @State private var completedTasks: Set<Int> = []
    @State private var exerciseProgress: Double = 0.0
    @State private var showingConversationPreview = false
    
    let lessonId: String
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            conversationPracticeHeader
            
            // Main Content
            if let exercise = selectedExercise {
                conversationExerciseView(exercise)
            } else {
                exerciseSelectionView
            }
        }
        .background(
            LinearGradient(
                colors: [Color.green.opacity(0.05), Color.teal.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .onAppear {
            loadConversationExercises()
        }
        .sheet(isPresented: $showingConversationPreview) {
            if let exercise = selectedExercise {
                ConversationPreviewView(exercise: exercise)
            }
        }
    }
    
    private var conversationPracticeHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Conversation Writing")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Practice writing Tamil conversations")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(spacing: 4) {
                    Image(systemName: "bubble.left.and.bubble.right.fill")
                        .font(.title2)
                        .foregroundColor(.green)
                    
                    Text("Dialogue")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                }
            }
            
            if let exercise = selectedExercise {
                // Exercise progress
                VStack(spacing: 8) {
                    HStack {
                        Text(exercise.title)
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Spacer()
                        
                        Button("Preview") {
                            showingConversationPreview = true
                        }
                        .buttonStyle(.bordered)
                        
                        Text("\(currentTaskIndex + 1)/\(exercise.writingTasks.count)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    ProgressView(value: exerciseProgress, total: 1.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: .green))
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.secondarySystemBackground))
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private var exerciseSelectionView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                Text("Select a Conversation")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .padding(.top)
                
                ForEach(conversationService.conversationWritingExercises.filter { $0.lessonId == lessonId }, id: \.id) { exercise in
                    ConversationExerciseCard(
                        exercise: exercise,
                        onSelect: {
                            selectedExercise = exercise
                            currentTaskIndex = 0
                            updateProgress()
                        }
                    )
                }
                
                if conversationService.conversationWritingExercises.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "bubble.left.and.bubble.right")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)
                        
                        Text("No conversation exercises available")
                            .font(.headline)
                            .foregroundColor(.secondary)
                        
                        Text("Conversation writing exercises will be generated for this lesson")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                }
            }
            .padding()
        }
    }
    
    private func conversationExerciseView(_ exercise: ConversationWritingService.ConversationWritingExercise) -> some View {
        VStack(spacing: 0) {
            // Current task content
            if currentTaskIndex < exercise.writingTasks.count {
                let currentTask = exercise.writingTasks[currentTaskIndex]
                
                ScrollView {
                    VStack(spacing: 20) {
                        // Context card
                        conversationContextCard(exercise.conversationContext)
                        
                        // Task content
                        conversationTaskView(currentTask, exercise: exercise)
                        
                        // Key phrases reference
                        keyPhrasesReferenceView(exercise.keyPhrases)
                        
                        // Navigation controls
                        taskNavigationControls(exercise: exercise)
                    }
                    .padding()
                }
            }
        }
        .fullScreenCover(isPresented: $showingWritingCanvas) {
            if let task = exercise.writingTasks[safe: currentTaskIndex],
               let character = getCharacterForTask(task) {
                TamilWritingCanvasContainer(
                    character: character,
                    writingMode: task.writingMode,
                    onComplete: handleTaskComplete
                )
            }
        }
    }
    
    private func conversationContextCard(_ context: ConversationWritingService.ConversationWritingExercise.ConversationContext) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "location.fill")
                    .foregroundColor(.green)
                
                Text("Conversation Context")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                contextInfoRow("Situation", context.situation)
                contextInfoRow("Setting", context.setting)
                contextInfoRow("Participants", context.participants.joined(separator: ", "))
                contextInfoRow("Formality", context.formalityLevel.displayName)
            }
            
            if !context.culturalBackground.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Cultural Context")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                    
                    Text(context.culturalBackground)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .italic()
                }
                .padding(.top, 8)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    private func contextInfoRow(_ label: String, _ value: String) -> some View {
        HStack {
            Text(label + ":")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.caption)
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
    
    private func conversationTaskView(_ task: ConversationWritingService.ConversationWritingExercise.ConversationWritingTask, exercise: ConversationWritingService.ConversationWritingExercise) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // Task header
            HStack {
                Text("Task \(task.taskNumber)")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(Color.green.opacity(0.2))
                    .foregroundColor(.green)
                    .cornerRadius(8)
                
                Text(task.taskType.displayName)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Image(systemName: task.taskType.icon)
                    .foregroundColor(.green)
                
                if completedTasks.contains(task.taskNumber) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                }
            }
            
            // Task instruction
            Text(task.instruction)
                .font(.headline)
                .fontWeight(.semibold)
            
            // Context
            if !task.context.isEmpty {
                Text(task.context)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.green.opacity(0.1))
                    )
            }
            
            // Target phrase display
            targetPhraseView(task.targetPhrase)
            
            // Writing hints
            if !task.hints.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Writing Hints")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                    
                    ForEach(task.hints, id: \.self) { hint in
                        HStack(alignment: .top, spacing: 8) {
                            Image(systemName: "lightbulb.fill")
                                .font(.caption)
                                .foregroundColor(.yellow)
                            
                            Text(hint)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.blue.opacity(0.05))
                )
            }
            
            // Action button
            taskActionButton(task: task)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    private func targetPhraseView(_ phrase: ConversationWritingService.ConversationWritingExercise.KeyPhrase) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Target Phrase")
                .font(.subheadline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                Text(phrase.tamil)
                    .font(.title)
                    .fontWeight(.medium)
                    .frame(maxWidth: .infinity, alignment: .center)
                
                Text(phrase.romanization)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                
                Text(phrase.english)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.secondarySystemBackground))
            )
            
            if let significance = phrase.culturalSignificance {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Cultural Note")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.purple)
                    
                    Text(significance)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .italic()
                }
                .padding(.top, 4)
            }
        }
    }
    
    private func keyPhrasesReferenceView(_ phrases: [ConversationWritingService.ConversationWritingExercise.KeyPhrase]) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Key Phrases Reference")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.orange)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(phrases, id: \.id) { phrase in
                        VStack(spacing: 4) {
                            Text(phrase.tamil)
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            Text(phrase.english)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.orange.opacity(0.1))
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    private func taskActionButton(task: ConversationWritingService.ConversationWritingExercise.ConversationWritingTask) -> some View {
        Button(action: {
            handleTaskAction(task)
        }) {
            HStack {
                Image(systemName: "pencil")
                    .font(.title3)
                
                Text("Start Writing")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .padding()
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [.green, .teal],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
            )
        }
        .disabled(completedTasks.contains(task.taskNumber))
    }
    
    private func taskNavigationControls(exercise: ConversationWritingService.ConversationWritingExercise) -> some View {
        HStack(spacing: 16) {
            Button("Previous") {
                if currentTaskIndex > 0 {
                    currentTaskIndex -= 1
                    updateProgress()
                }
            }
            .disabled(currentTaskIndex == 0)
            .buttonStyle(.bordered)
            
            Spacer()
            
            Button("Next") {
                if currentTaskIndex < exercise.writingTasks.count - 1 {
                    currentTaskIndex += 1
                    updateProgress()
                }
            }
            .disabled(currentTaskIndex >= exercise.writingTasks.count - 1)
            .buttonStyle(.bordered)
            
            Button("Complete Exercise") {
                completeExercise(exercise)
            }
            .disabled(completedTasks.count < exercise.writingTasks.count)
            .buttonStyle(.borderedProminent)
        }
    }
    
    // MARK: - Actions
    
    private func loadConversationExercises() {
        Task {
            await conversationService.generateConversationWritingExercises(for: lessonId)
        }
    }
    
    private func handleTaskAction(_ task: ConversationWritingService.ConversationWritingExercise.ConversationWritingTask) {
        showingWritingCanvas = true
    }
    
    private func handleTaskComplete(_ result: WritingResult) {
        completedTasks.insert(currentTaskIndex + 1)
        updateProgress()
        
        // Auto-advance to next task
        if let exercise = selectedExercise, currentTaskIndex < exercise.writingTasks.count - 1 {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                currentTaskIndex += 1
                updateProgress()
            }
        }
    }
    
    private func updateProgress() {
        if let exercise = selectedExercise {
            exerciseProgress = Double(completedTasks.count) / Double(exercise.writingTasks.count)
        }
    }
    
    private func completeExercise(_ exercise: ConversationWritingService.ConversationWritingExercise) {
        print("Conversation exercise completed: \(exercise.title)")
        selectedExercise = nil
        currentTaskIndex = 0
        completedTasks.removeAll()
        exerciseProgress = 0.0
    }
    
    private func getCharacterForTask(_ task: ConversationWritingService.ConversationWritingExercise.ConversationWritingTask) -> TamilCharacter? {
        let targetText = task.targetPhrase.tamil
        guard !targetText.isEmpty else { return nil }
        let firstChar = String(targetText.first!)
        return scriptService.allCharacters.first { $0.character == firstChar }
    }
}

// MARK: - Conversation Exercise Card

struct ConversationExerciseCard: View {
    let exercise: ConversationWritingService.ConversationWritingExercise
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(exercise.title)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Text(exercise.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    VStack(spacing: 4) {
                        Text("\(exercise.estimatedTime) min")
                            .font(.caption)
                            .foregroundColor(.green)
                        
                        Text("Level \(exercise.difficultyLevel)")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
                
                // Context preview
                Text(exercise.conversationContext.situation)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .italic()
                    .lineLimit(2)
                
                // Key phrases preview
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(exercise.keyPhrases.prefix(3), id: \.id) { phrase in
                            Text(phrase.tamil)
                                .font(.subheadline)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    RoundedRectangle(cornerRadius: 6)
                                        .fill(Color(.tertiarySystemBackground))
                                )
                        }
                    }
                    .padding(.horizontal)
                }
                
                HStack {
                    Text("\(exercise.writingTasks.count) tasks")
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.green.opacity(0.2))
                        .foregroundColor(.green)
                        .cornerRadius(8)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.green)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Conversation Preview View

struct ConversationPreviewView: View {
    let exercise: ConversationWritingService.ConversationWritingExercise
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Conversation Preview")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    // This would show the actual conversation dialogue
                    // For now, showing key phrases
                    ForEach(exercise.keyPhrases, id: \.id) { phrase in
                        VStack(alignment: .leading, spacing: 8) {
                            Text(phrase.tamil)
                                .font(.headline)
                            
                            Text(phrase.english)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.secondarySystemBackground))
                        )
                        .padding(.horizontal)
                    }
                }
                .padding(.vertical)
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    ConversationWritingPracticeView(lessonId: "A1_BASIC_GREETINGS")
}
