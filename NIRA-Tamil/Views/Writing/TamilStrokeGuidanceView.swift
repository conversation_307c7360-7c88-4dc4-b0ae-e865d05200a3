//
//  TamilStrokeGuidanceView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import SwiftUI

struct TamilStrokeGuidanceView: View {
    let character: TamilCharacter
    let strokeOrders: [TamilStrokeOrder]
    
    @State private var currentStrokeIndex = 0
    @State private var animationProgress: Double = 0
    @State private var isAnimating = false
    @State private var showAllStrokes = false
    @State private var animationSpeed: Double = 1.0
    
    private let animationDuration: Double = 2.0
    
    var body: some View {
        VStack(spacing: 20) {
            // Character display with stroke animation
            strokeAnimationView
            
            // Stroke controls
            strokeControlsView
            
            // Stroke information
            strokeInfoView
        }
        .onAppear {
            startStrokeAnimation()
        }
    }
    
    private var strokeAnimationView: some View {
        ZStack {
            // Background character outline
            characterOutlineView
            
            // Completed strokes
            completedStrokesView
            
            // Current animating stroke
            currentStrokeView
            
            // Stroke order numbers
            strokeNumbersView
        }
        .frame(width: 300, height: 300)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private var characterOutlineView: some View {
        Text(character.character)
            .font(.system(size: 200, weight: .ultraLight))
            .foregroundColor(.gray.opacity(0.2))
    }
    
    private var completedStrokesView: some View {
        ForEach(0..<currentStrokeIndex, id: \.self) { index in
            if index < strokeOrders.count {
                StrokePathShape(strokeOrder: strokeOrders[index])
                    .stroke(Color.green, lineWidth: 4)
                    .opacity(0.8)
            }
        }
    }
    
    private var currentStrokeView: some View {
        Group {
            if currentStrokeIndex < strokeOrders.count {
                let currentStroke = strokeOrders[currentStrokeIndex]
                
                // Animated stroke path
                StrokePathShape(strokeOrder: currentStroke)
                    .trim(from: 0, to: animationProgress)
                    .stroke(Color.blue, style: StrokeStyle(lineWidth: 6, lineCap: .round))
                    .animation(.easeInOut(duration: animationDuration * animationSpeed), value: animationProgress)
                
                // Stroke direction indicator
                StrokeDirectionIndicator(
                    strokeOrder: currentStroke,
                    progress: animationProgress
                )
                
                // Start point indicator
                Circle()
                    .fill(Color.blue)
                    .frame(width: 12, height: 12)
                    .position(currentStroke.startPoint)
                    .scaleEffect(isAnimating ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 0.5).repeatForever(autoreverses: true), value: isAnimating)
            }
        }
    }
    
    private var strokeNumbersView: some View {
        ForEach(Array(strokeOrders.enumerated()), id: \.offset) { index, strokeOrder in
            ZStack {
                Circle()
                    .fill(index < currentStrokeIndex ? Color.green : 
                          index == currentStrokeIndex ? Color.blue : Color.gray.opacity(0.5))
                    .frame(width: 24, height: 24)
                
                Text("\(index + 1)")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            .position(strokeOrder.startPoint)
            .scaleEffect(index == currentStrokeIndex ? 1.2 : 1.0)
            .animation(.spring(), value: currentStrokeIndex)
        }
    }
    
    private var strokeControlsView: some View {
        HStack(spacing: 16) {
            // Previous stroke
            Button(action: previousStroke) {
                Image(systemName: "chevron.left")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
            .disabled(currentStrokeIndex == 0)
            
            // Play/Pause animation
            Button(action: toggleAnimation) {
                Image(systemName: isAnimating ? "pause.fill" : "play.fill")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
            
            // Next stroke
            Button(action: nextStroke) {
                Image(systemName: "chevron.right")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
            .disabled(currentStrokeIndex >= strokeOrders.count - 1)
            
            Spacer()
            
            // Speed control
            Menu {
                Button("0.5x Speed") { animationSpeed = 2.0 }
                Button("1x Speed") { animationSpeed = 1.0 }
                Button("1.5x Speed") { animationSpeed = 0.67 }
                Button("2x Speed") { animationSpeed = 0.5 }
            } label: {
                Image(systemName: "speedometer")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
            
            // Show all strokes toggle
            Button(action: { showAllStrokes.toggle() }) {
                Image(systemName: showAllStrokes ? "eye.slash" : "eye")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }
    
    private var strokeInfoView: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Stroke \(currentStrokeIndex + 1) of \(strokeOrders.count)")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if currentStrokeIndex < strokeOrders.count {
                    let currentStroke = strokeOrders[currentStrokeIndex]
                    Text(currentStroke.strokeDirection.displayName)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.blue.opacity(0.2))
                        .foregroundColor(.blue)
                        .cornerRadius(8)
                }
            }
            
            if currentStrokeIndex < strokeOrders.count {
                let currentStroke = strokeOrders[currentStrokeIndex]
                
                if let description = currentStroke.strokeDescription {
                    Text(description)
                        .font(.body)
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Label("Type: \(currentStroke.strokeType.displayName)", systemImage: "pencil.tip")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Label("Duration: \(currentStroke.timingDuration)ms", systemImage: "timer")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.tertiarySystemBackground))
        )
    }
    
    // MARK: - Animation Control
    
    private func startStrokeAnimation() {
        guard currentStrokeIndex < strokeOrders.count else { return }
        
        isAnimating = true
        animationProgress = 0
        
        withAnimation(.easeInOut(duration: animationDuration * animationSpeed)) {
            animationProgress = 1.0
        }
        
        // Auto-advance to next stroke after animation completes
        DispatchQueue.main.asyncAfter(deadline: .now() + animationDuration * animationSpeed + 0.5) {
            if currentStrokeIndex < strokeOrders.count - 1 {
                nextStroke()
            } else {
                isAnimating = false
            }
        }
    }
    
    private func toggleAnimation() {
        if isAnimating {
            isAnimating = false
        } else {
            startStrokeAnimation()
        }
    }
    
    private func nextStroke() {
        guard currentStrokeIndex < strokeOrders.count - 1 else { return }
        
        currentStrokeIndex += 1
        startStrokeAnimation()
    }
    
    private func previousStroke() {
        guard currentStrokeIndex > 0 else { return }
        
        currentStrokeIndex -= 1
        animationProgress = 0
        isAnimating = false
    }
}

// MARK: - Stroke Path Shape

struct StrokePathShape: Shape {
    let strokeOrder: TamilStrokeOrder
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        // Convert stroke order to SwiftUI Path
        path.move(to: strokeOrder.startPoint)
        
        if let controlPoints = strokeOrder.controlPoints, !controlPoints.isEmpty {
            // Bezier curve
            if controlPoints.count == 1 {
                path.addQuadCurve(to: strokeOrder.endPoint, control: controlPoints[0])
            } else if controlPoints.count >= 2 {
                path.addCurve(to: strokeOrder.endPoint, 
                            control1: controlPoints[0], 
                            control2: controlPoints[1])
            }
        } else {
            // Straight line
            path.addLine(to: strokeOrder.endPoint)
        }
        
        return path
    }
}

// MARK: - Stroke Direction Indicator

struct StrokeDirectionIndicator: View {
    let strokeOrder: TamilStrokeOrder
    let progress: Double
    
    var body: some View {
        Group {
            if progress > 0.1 {
                // Arrow indicating stroke direction
                Image(systemName: "arrow.right")
                    .font(.caption)
                    .foregroundColor(.blue)
                    .rotationEffect(directionAngle)
                    .position(currentPosition)
                    .opacity(progress > 0.8 ? 0.0 : 1.0)
                    .animation(.easeInOut(duration: 0.3), value: progress)
            }
        }
    }
    
    private var currentPosition: CGPoint {
        // Calculate current position along the stroke path
        let startX = strokeOrder.startPoint.x
        let startY = strokeOrder.startPoint.y
        let endX = strokeOrder.endPoint.x
        let endY = strokeOrder.endPoint.y
        
        let currentX = startX + (endX - startX) * progress
        let currentY = startY + (endY - startY) * progress
        
        return CGPoint(x: currentX, y: currentY)
    }
    
    private var directionAngle: Angle {
        let deltaX = strokeOrder.endPoint.x - strokeOrder.startPoint.x
        let deltaY = strokeOrder.endPoint.y - strokeOrder.startPoint.y
        let angle = atan2(deltaY, deltaX)
        return Angle(radians: Double(angle))
    }
}

// MARK: - Interactive Stroke Practice

struct InteractiveStrokePracticeView: View {
    let character: TamilCharacter
    let strokeOrders: [TamilStrokeOrder]
    let onStrokeCompleted: (Int, Double) -> Void
    
    @State private var currentStrokeIndex = 0
    @State private var userPath: Path = Path()
    @State private var isDrawing = false
    @State private var strokeAccuracy: Double = 0
    
    var body: some View {
        ZStack {
            // Guidance overlay
            TamilStrokeGuidanceView(
                character: character,
                strokeOrders: strokeOrders
            )
            .opacity(0.3)
            
            // User drawing area
            Canvas { context, size in
                context.stroke(userPath, with: .color(.red), lineWidth: 4)
            }
            .gesture(
                DragGesture(minimumDistance: 0)
                    .onChanged { value in
                        if !isDrawing {
                            userPath = Path()
                            userPath.move(to: value.location)
                            isDrawing = true
                        } else {
                            userPath.addLine(to: value.location)
                        }
                    }
                    .onEnded { _ in
                        completeStroke()
                    }
            )
        }
    }
    
    private func completeStroke() {
        guard currentStrokeIndex < strokeOrders.count else { return }
        
        // Calculate stroke accuracy
        let expectedStroke = strokeOrders[currentStrokeIndex]
        let accuracy = calculateStrokeAccuracy(userPath: userPath, expectedStroke: expectedStroke)
        
        onStrokeCompleted(currentStrokeIndex, accuracy)
        
        // Move to next stroke
        currentStrokeIndex += 1
        userPath = Path()
        isDrawing = false
    }
    
    private func calculateStrokeAccuracy(userPath: Path, expectedStroke: TamilStrokeOrder) -> Double {
        // Simplified accuracy calculation
        // In practice, this would use the TamilStrokeAnalyzer
        return 0.85 // Placeholder
    }
}

#Preview {
    TamilStrokeGuidanceView(
        character: TamilCharacter(
            id: UUID(),
            character: "அ",
            characterType: .vowel,
            unicodeValue: "U+0B85",
            romanization: "a",
            ipaPronunciation: "/ʌ/",
            characterNameEnglish: "Short A",
            characterNameTamil: "அகரம்",
            difficultyLevel: 1,
            frequencyRank: 1,
            strokeCount: 2,
            writingComplexity: .simple,
            characterCategory: "basic_vowel",
            learningOrder: 1,
            strokeOrders: [
                TamilStrokeOrder(
                    id: UUID(),
                    characterId: UUID(),
                    strokeNumber: 1,
                    strokePath: StrokePath(pathData: "", boundingBox: .zero, pathLength: 0),
                    strokeDirection: .topToBottom,
                    strokeType: .curve,
                    timingDuration: 1000,
                    pressureVariation: [],
                    startPoint: CGPoint(x: 50, y: 50),
                    endPoint: CGPoint(x: 150, y: 100),
                    controlPoints: [CGPoint(x: 100, y: 75)],
                    strokeDescription: "Start from top and curve down",
                    createdAt: Date()
                )
            ],
            createdAt: Date(),
            updatedAt: Date()
        ),
        strokeOrders: []
    )
}
