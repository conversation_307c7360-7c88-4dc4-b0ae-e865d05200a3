//
//  TamilWritingCanvas.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import SwiftUI
import PencilKit
import UIKit

struct TamilWritingCanvas: UIViewRepresentable {
    @Binding var canvasView: PKCanvasView
    @Binding var isDirty: Bool
    
    let character: <PERSON><PERSON><PERSON><PERSON>?
    let writingMode: WritingMode
    let onStrokeAdded: (PKStroke) -> Void
    let onDrawingChanged: (PKDrawing) -> Void
    
    func makeUIView(context: Context) -> PKCanvasView {
        canvasView.delegate = context.coordinator
        canvasView.drawingPolicy = .anyInput
        canvasView.backgroundColor = UIColor.clear
        
        // Configure Tamil-optimized drawing tools
        configureTamilDrawingTools()
        
        // Set up canvas appearance
        setupCanvasAppearance()
        
        return canvasView
    }
    
    func updateUIView(_ uiView: PKCanvasView, context: Context) {
        // Update canvas based on writing mode
        updateCanvasForMode(uiView)
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    private func configureTamilDrawingTools() {
        // Tamil-optimized pen settings
        let tamilPen = PKInkingTool(.pen, color: .black, width: 3.0)
        canvasView.tool = tamilPen
        
        // Configure ruler for stroke guidance
        canvasView.isRulerActive = false
    }
    
    private func setupCanvasAppearance() {
        canvasView.backgroundColor = UIColor.systemBackground
        canvasView.isOpaque = false
        canvasView.layer.cornerRadius = 12
        canvasView.layer.borderWidth = 1
        canvasView.layer.borderColor = UIColor.systemGray4.cgColor
    }
    
    private func updateCanvasForMode(_ canvas: PKCanvasView) {
        switch writingMode {
        case .guided:
            // Show guidance overlays
            canvas.isUserInteractionEnabled = true
            
        case .freeform:
            // Full freedom
            canvas.isUserInteractionEnabled = true
            
        case .assessment:
            // Assessment mode with time limits
            canvas.isUserInteractionEnabled = true
        }
    }
    
    class Coordinator: NSObject, PKCanvasViewDelegate {
        let parent: TamilWritingCanvas
        
        init(_ parent: TamilWritingCanvas) {
            self.parent = parent
        }
        
        func canvasViewDrawingDidChange(_ canvasView: PKCanvasView) {
            parent.isDirty = !canvasView.drawing.strokes.isEmpty
            parent.onDrawingChanged(canvasView.drawing)
        }
        
        func canvasViewDidEndUsingTool(_ canvasView: PKCanvasView) {
            // Analyze the latest stroke
            if let latestStroke = canvasView.drawing.strokes.last {
                parent.onStrokeAdded(latestStroke)
            }
        }
    }
}

// MARK: - Tamil Writing Canvas Container

struct TamilWritingCanvasContainer: View {
    @StateObject private var scriptService = TamilScriptService.shared
    @StateObject private var strokeAnalyzer = TamilStrokeAnalyzer()
    @StateObject private var characterRecognizer = TamilCharacterRecognizer()
    
    @State private var canvasView = PKCanvasView()
    @State private var isDirty = false
    @State private var currentStrokes: [PKStroke] = []
    @State private var recognitionResults: [CharacterRecognitionResult] = []
    @State private var showingGuidance = true
    @State private var currentStrokeIndex = 0
    
    let character: TamilCharacter
    let writingMode: WritingMode
    let onComplete: (WritingResult) -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            // Header with character info
            characterHeaderView
            
            // Main canvas area
            ZStack {
                // Background guidance layer
                if showingGuidance && writingMode == .guided {
                    TamilCharacterGuidanceView(
                        character: character,
                        currentStrokeIndex: currentStrokeIndex,
                        strokeOrders: scriptService.getStrokeOrder(for: character.id)
                    )
                }
                
                // Main writing canvas
                TamilWritingCanvas(
                    canvasView: $canvasView,
                    isDirty: $isDirty,
                    character: character,
                    writingMode: writingMode,
                    onStrokeAdded: handleStrokeAdded,
                    onDrawingChanged: handleDrawingChanged
                )
                .frame(height: 300)
                
                // Real-time feedback overlay
                if writingMode != .assessment {
                    TamilWritingFeedbackOverlay(
                        recognitionResults: recognitionResults,
                        currentStroke: currentStrokes.last
                    )
                }
            }
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
            )
            
            // Controls
            writingControlsView
            
            // Progress and feedback
            if writingMode == .guided {
                strokeProgressView
            }
        }
        .padding()
        .onAppear {
            setupCanvas()
        }
    }
    
    private var characterHeaderView: some View {
        VStack(spacing: 8) {
            HStack {
                Text(character.character)
                    .font(.system(size: 48, weight: .medium))
                    .foregroundColor(.primary)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(character.characterNameEnglish)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(character.romanization)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    if let ipa = character.ipaPronunciation {
                        Text(ipa)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Strokes: \(character.strokeCount)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(character.writingComplexity.displayName)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(character.writingComplexity.color.opacity(0.2))
                        .foregroundColor(character.writingComplexity.color)
                        .cornerRadius(8)
                }
            }
            
            if writingMode == .guided {
                Text("Follow the stroke order shown below")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }
    
    private var writingControlsView: some View {
        HStack(spacing: 16) {
            // Clear button
            Button(action: clearCanvas) {
                Label("Clear", systemImage: "trash")
                    .foregroundColor(.red)
            }
            .buttonStyle(.bordered)
            
            // Undo button
            Button(action: undoLastStroke) {
                Label("Undo", systemImage: "arrow.uturn.backward")
            }
            .buttonStyle(.bordered)
            .disabled(currentStrokes.isEmpty)
            
            Spacer()
            
            // Guidance toggle (for guided mode)
            if writingMode == .guided {
                Button(action: { showingGuidance.toggle() }) {
                    Label(showingGuidance ? "Hide Guide" : "Show Guide", 
                          systemImage: showingGuidance ? "eye.slash" : "eye")
                }
                .buttonStyle(.bordered)
            }
            
            // Complete button
            Button(action: completeWriting) {
                Label("Complete", systemImage: "checkmark")
                    .foregroundColor(.white)
            }
            .buttonStyle(.borderedProminent)
            .disabled(!isDirty)
        }
    }
    
    private var strokeProgressView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Stroke Progress")
                .font(.headline)
            
            HStack {
                ForEach(0..<character.strokeCount, id: \.self) { index in
                    Circle()
                        .fill(index < currentStrokeIndex ? Color.green : 
                              index == currentStrokeIndex ? Color.blue : Color.gray.opacity(0.3))
                        .frame(width: 12, height: 12)
                }
                
                Spacer()
                
                Text("\(currentStrokeIndex)/\(character.strokeCount)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            if currentStrokeIndex < character.strokeCount {
                let strokeOrders = scriptService.getStrokeOrder(for: character.id)
                if currentStrokeIndex < strokeOrders.count {
                    let currentStroke = strokeOrders[currentStrokeIndex]
                    Text(currentStroke.strokeDescription ?? "Draw the next stroke")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.tertiarySystemBackground))
        )
    }
    
    // MARK: - Canvas Actions
    
    private func setupCanvas() {
        canvasView.drawing = PKDrawing()
        currentStrokes = []
        currentStrokeIndex = 0
    }
    
    private func handleStrokeAdded(_ stroke: PKStroke) {
        currentStrokes.append(stroke)
        
        // Analyze stroke in guided mode
        if writingMode == .guided {
            analyzeStrokeAccuracy(stroke)
            currentStrokeIndex = min(currentStrokeIndex + 1, character.strokeCount)
        }
        
        // Perform character recognition
        Task {
            await performCharacterRecognition()
        }
    }
    
    private func handleDrawingChanged(_ drawing: PKDrawing) {
        currentStrokes = drawing.strokes
        
        // Real-time analysis for freeform mode
        if writingMode == .freeform {
            Task {
                await performCharacterRecognition()
            }
        }
    }
    
    private func analyzeStrokeAccuracy(_ stroke: PKStroke) {
        let strokeOrders = scriptService.getStrokeOrder(for: character.id)
        guard currentStrokeIndex - 1 < strokeOrders.count else { return }
        
        let expectedStroke = strokeOrders[currentStrokeIndex - 1]
        let accuracy = strokeAnalyzer.analyzeStroke(stroke, against: expectedStroke)
        
        // Provide feedback based on accuracy
        if accuracy.overallAccuracy < 0.7 {
            // Show improvement suggestions
            print("Stroke accuracy low: \(accuracy.overallAccuracy)")
        }
    }
    
    private func performCharacterRecognition() async {
        let image = canvasView.drawing.image(from: canvasView.bounds, scale: 1.0)
        
        do {
            let results = try await characterRecognizer.recognizeCharacter(in: image)
            await MainActor.run {
                recognitionResults = results
            }
        } catch {
            print("Character recognition failed: \(error)")
        }
    }
    
    private func clearCanvas() {
        canvasView.drawing = PKDrawing()
        currentStrokes = []
        currentStrokeIndex = 0
        recognitionResults = []
        isDirty = false
    }
    
    private func undoLastStroke() {
        guard !currentStrokes.isEmpty else { return }
        
        currentStrokes.removeLast()
        currentStrokeIndex = max(0, currentStrokeIndex - 1)
        
        // Rebuild drawing without last stroke
        var newDrawing = PKDrawing()
        for stroke in currentStrokes {
            newDrawing.strokes.append(stroke)
        }
        canvasView.drawing = newDrawing
        
        isDirty = !currentStrokes.isEmpty
    }
    
    private func completeWriting() {
        Task {
            // Final character recognition
            await performCharacterRecognition()
            
            // Calculate overall accuracy
            let overallAccuracy = calculateOverallAccuracy()
            
            // Create writing result
            let result = WritingResult(
                character: character,
                writingMode: writingMode,
                strokes: currentStrokes,
                recognitionResults: recognitionResults,
                accuracy: overallAccuracy,
                completionTime: Date().timeIntervalSince(Date()), // This should be tracked properly
                strokeAccuracies: [], // This should be populated from stroke analysis
                isCompleted: true
            )
            
            onComplete(result)
        }
    }
    
    private func calculateOverallAccuracy() -> Double {
        guard !recognitionResults.isEmpty else { return 0.0 }
        
        // Find the best match for the target character
        let bestMatch = recognitionResults.first { result in
            result.recognizedCharacter == character.character
        }
        
        return bestMatch?.confidence ?? 0.0
    }
}

// MARK: - Supporting Models

struct WritingResult {
    let character: TamilCharacter
    let writingMode: WritingMode
    let strokes: [PKStroke]
    let recognitionResults: [CharacterRecognitionResult]
    let accuracy: Double
    let completionTime: TimeInterval
    let strokeAccuracies: [StrokeAccuracy]
    let isCompleted: Bool
}

struct CharacterRecognitionResult {
    let recognizedCharacter: String
    let confidence: Double
    let boundingBox: CGRect
    let alternativeMatches: [String]
}

// MARK: - Tamil Character Guidance View

struct TamilCharacterGuidanceView: View {
    let character: TamilCharacter
    let currentStrokeIndex: Int
    let strokeOrders: [TamilStrokeOrder]

    var body: some View {
        ZStack {
            // Character outline
            characterOutlineView

            // Stroke order indicators
            strokeOrderView

            // Current stroke highlight
            currentStrokeHighlight
        }
    }

    private var characterOutlineView: some View {
        Text(character.character)
            .font(.system(size: 200, weight: .ultraLight))
            .foregroundColor(.gray.opacity(0.3))
            .overlay(
                Text(character.character)
                    .font(.system(size: 200, weight: .ultraLight))
                    .foregroundColor(.clear)
                    .overlay(
                        Rectangle()
                            .stroke(Color.blue.opacity(0.3), style: StrokeStyle(lineWidth: 2, dash: [5, 5]))
                    )
            )
    }

    private var strokeOrderView: some View {
        ForEach(Array(strokeOrders.enumerated()), id: \.offset) { index, strokeOrder in
            StrokeOrderIndicator(
                strokeOrder: strokeOrder,
                index: index,
                isActive: index == currentStrokeIndex,
                isCompleted: index < currentStrokeIndex
            )
        }
    }

    private var currentStrokeHighlight: some View {
        Group {
            if currentStrokeIndex < strokeOrders.count {
                let currentStroke = strokeOrders[currentStrokeIndex]
                StrokePathView(strokeOrder: currentStroke)
                    .stroke(Color.blue, lineWidth: 3)
                    .opacity(0.6)
                    .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: currentStrokeIndex)
            }
        }
    }
}

struct StrokeOrderIndicator: View {
    let strokeOrder: TamilStrokeOrder
    let index: Int
    let isActive: Bool
    let isCompleted: Bool

    var body: some View {
        ZStack {
            Circle()
                .fill(isCompleted ? Color.green : isActive ? Color.blue : Color.gray.opacity(0.5))
                .frame(width: 24, height: 24)

            Text("\(index + 1)")
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(.white)
        }
        .position(strokeOrder.startPoint)
        .scaleEffect(isActive ? 1.2 : 1.0)
        .animation(.spring(), value: isActive)
    }
}

struct StrokePathView: Shape {
    let strokeOrder: TamilStrokeOrder

    func path(in rect: CGRect) -> Path {
        var path = Path()

        // Convert stroke path data to SwiftUI Path
        // This is a simplified implementation - in practice, you'd parse the SVG path data
        path.move(to: strokeOrder.startPoint)

        if let controlPoints = strokeOrder.controlPoints, !controlPoints.isEmpty {
            // Bezier curve
            path.addQuadCurve(to: strokeOrder.endPoint, control: controlPoints[0])
        } else {
            // Straight line
            path.addLine(to: strokeOrder.endPoint)
        }

        return path
    }
}

// MARK: - Tamil Writing Feedback Overlay

struct TamilWritingFeedbackOverlay: View {
    let recognitionResults: [CharacterRecognitionResult]
    let currentStroke: PKStroke?

    var body: some View {
        VStack {
            HStack {
                Spacer()

                if !recognitionResults.isEmpty {
                    recognitionFeedbackView
                }
            }

            Spacer()
        }
        .padding()
    }

    private var recognitionFeedbackView: some View {
        VStack(alignment: .trailing, spacing: 4) {
            ForEach(Array(recognitionResults.prefix(3).enumerated()), id: \.offset) { index, result in
                HStack(spacing: 8) {
                    Text(result.recognizedCharacter)
                        .font(.title2)

                    Text("\(Int(result.confidence * 100))%")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                )
                .opacity(index == 0 ? 1.0 : 0.7)
            }
        }
    }
}

#Preview {
    TamilWritingCanvasContainer(
        character: TamilCharacter(
            id: UUID(),
            character: "அ",
            characterType: .vowel,
            unicodeValue: "U+0B85",
            romanization: "a",
            ipaPronunciation: "/ʌ/",
            characterNameEnglish: "Short A",
            characterNameTamil: "அகரம்",
            difficultyLevel: 1,
            frequencyRank: 1,
            strokeCount: 2,
            writingComplexity: .simple,
            characterCategory: "basic_vowel",
            learningOrder: 1,
            strokeOrders: [],
            createdAt: Date(),
            updatedAt: Date()
        ),
        writingMode: .guided,
        onComplete: { _ in }
    )
}
