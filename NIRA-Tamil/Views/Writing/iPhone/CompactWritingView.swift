//
//  CompactWritingView.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import SwiftUI
import PencilKit

struct CompactWritingView: View {
    @StateObject private var scriptService = TamilScriptService.shared
    @StateObject private var writingViewModel = CompactWritingViewModel()
    
    @State private var selectedCharacter: TamilCharacter?
    @State private var writingMode: WritingMode = .guided
    @State private var showingCharacterPicker = false
    @State private var showingWritingCanvas = false
    @State private var isCompactMode = true
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // Compact header
                compactHeader
                
                // Main content area
                if isCompactMode {
                    compactContentView(geometry: geometry)
                } else {
                    expandedContentView(geometry: geometry)
                }
                
                // Bottom controls
                compactBottomControls
            }
            .background(
                LinearGradient(
                    colors: [Color.blue.opacity(0.03), Color.purple.opacity(0.03)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
        }
        .navigationBarHidden(true)
        .onAppear {
            setupForCompactDevice()
        }
        .sheet(isPresented: $showingCharacterPicker) {
            CompactCharacterPickerView(
                selectedCharacter: $selectedCharacter,
                onCharacterSelected: { character in
                    selectedCharacter = character
                    showingWritingCanvas = true
                }
            )
        }
        .fullScreenCover(isPresented: $showingWritingCanvas) {
            if let character = selectedCharacter {
                CompactWritingCanvasView(
                    character: character,
                    writingMode: writingMode,
                    onComplete: handleWritingComplete
                )
            }
        }
    }
    
    private var compactHeader: some View {
        VStack(spacing: 8) {
            HStack {
                // Mode toggle
                Button(action: { isCompactMode.toggle() }) {
                    Image(systemName: isCompactMode ? "arrow.up.left.and.arrow.down.right" : "arrow.down.right.and.arrow.up.left")
                        .font(.title3)
                        .foregroundColor(.blue)
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Tamil Writing")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    if let character = selectedCharacter {
                        Text(character.character)
                            .font(.title2)
                            .foregroundColor(.blue)
                    } else {
                        Text("Select Character")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // Writing mode selector
                CompactModeSelector(selectedMode: $writingMode)
            }
            
            // Progress indicator (if in lesson mode)
            if writingViewModel.isInLessonMode {
                CompactProgressIndicator(
                    current: writingViewModel.currentStep,
                    total: writingViewModel.totalSteps,
                    progress: writingViewModel.progress
                )
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
    }
    
    private func compactContentView(geometry: GeometryProxy) -> some View {
        VStack(spacing: 12) {
            // Character display
            if let character = selectedCharacter {
                CompactCharacterDisplayView(character: character)
                    .frame(height: geometry.size.height * 0.25)
            } else {
                CharacterSelectionPrompt()
                    .frame(height: geometry.size.height * 0.25)
            }
            
            // Quick actions
            CompactQuickActionsView(
                selectedCharacter: selectedCharacter,
                onSelectCharacter: { showingCharacterPicker = true },
                onStartWriting: { showingWritingCanvas = true },
                onPracticeMode: { writingMode = .freeform }
            )
            
            // Recent characters
            if !writingViewModel.recentCharacters.isEmpty {
                CompactRecentCharactersView(
                    characters: writingViewModel.recentCharacters,
                    onCharacterTap: { character in
                        selectedCharacter = character
                        showingWritingCanvas = true
                    }
                )
            }
            
            Spacer()
        }
        .padding(.horizontal)
    }
    
    private func expandedContentView(geometry: GeometryProxy) -> some View {
        ScrollView {
            VStack(spacing: 16) {
                // Character grid
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 12) {
                    ForEach(scriptService.allCharacters.prefix(16), id: \.id) { character in
                        CompactCharacterCell(
                            character: character,
                            isSelected: selectedCharacter?.id == character.id,
                            onTap: {
                                selectedCharacter = character
                                showingWritingCanvas = true
                            }
                        )
                    }
                }
                .padding(.horizontal)
                
                // Writing tips
                CompactWritingTipsView()
            }
            .padding(.vertical)
        }
    }
    
    private var compactBottomControls: some View {
        HStack(spacing: 16) {
            // Character picker
            Button(action: { showingCharacterPicker = true }) {
                VStack(spacing: 4) {
                    Image(systemName: "textformat")
                        .font(.title3)
                    
                    Text("Characters")
                        .font(.caption2)
                }
                .foregroundColor(.blue)
            }
            
            Spacer()
            
            // Start writing
            Button(action: { 
                if selectedCharacter != nil {
                    showingWritingCanvas = true
                } else {
                    showingCharacterPicker = true
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "pencil")
                        .font(.title3)
                    
                    Text(selectedCharacter != nil ? "Write" : "Start")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(Color.blue)
                )
            }
            
            Spacer()
            
            // Settings/More
            Button(action: { /* Show settings */ }) {
                VStack(spacing: 4) {
                    Image(systemName: "ellipsis")
                        .font(.title3)
                    
                    Text("More")
                        .font(.caption2)
                }
                .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Actions
    
    private func setupForCompactDevice() {
        // Configure for iPhone-specific optimizations
        writingViewModel.configureForCompactDevice()
    }
    
    private func handleWritingComplete(_ result: WritingResult) {
        writingViewModel.recordWritingResult(result)
        
        // Auto-advance in lesson mode
        if writingViewModel.isInLessonMode {
            writingViewModel.advanceToNextCharacter()
            selectedCharacter = writingViewModel.currentCharacter
        }
    }
}

// MARK: - Compact Mode Selector

struct CompactModeSelector: View {
    @Binding var selectedMode: WritingMode
    
    var body: some View {
        Menu {
            ForEach(WritingMode.allCases, id: \.self) { mode in
                Button(action: { selectedMode = mode }) {
                    HStack {
                        Text(mode.displayName)
                        if selectedMode == mode {
                            Image(systemName: "checkmark")
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 4) {
                Image(systemName: selectedMode.icon)
                    .font(.caption)
                
                Text(selectedMode.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                
                Image(systemName: "chevron.down")
                    .font(.caption2)
            }
            .foregroundColor(.blue)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.blue.opacity(0.1))
            )
        }
    }
}

// MARK: - Compact Progress Indicator

struct CompactProgressIndicator: View {
    let current: Int
    let total: Int
    let progress: Double
    
    var body: some View {
        VStack(spacing: 4) {
            HStack {
                Text("Step \(current) of \(total)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("\(Int(progress * 100))%")
                    .font(.caption2)
                    .fontWeight(.semibold)
                    .foregroundColor(.blue)
            }
            
            ProgressView(value: progress, total: 1.0)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                .frame(height: 2)
        }
        .padding(.horizontal, 4)
    }
}

// MARK: - Compact Character Display

struct CompactCharacterDisplayView: View {
    let character: TamilCharacter
    
    var body: some View {
        VStack(spacing: 8) {
            Text(character.character)
                .font(.system(size: 80, weight: .light))
                .foregroundColor(.primary)
            
            VStack(spacing: 2) {
                Text(character.romanization)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
                
                Text(character.characterNameEnglish)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // Difficulty indicator
            HStack(spacing: 2) {
                ForEach(1...character.difficultyLevel, id: \.self) { _ in
                    Circle()
                        .fill(character.writingComplexity.color)
                        .frame(width: 6, height: 6)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.secondarySystemBackground))
        )
    }
}

// MARK: - Character Selection Prompt

struct CharacterSelectionPrompt: View {
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: "textformat.abc")
                .font(.system(size: 40))
                .foregroundColor(.gray)
            
            Text("Select a Tamil character to practice")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Choose Character") {
                // This would trigger character picker
            }
            .buttonStyle(.bordered)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.secondarySystemBackground))
        )
    }
}

// MARK: - Compact Quick Actions

struct CompactQuickActionsView: View {
    let selectedCharacter: TamilCharacter?
    let onSelectCharacter: () -> Void
    let onStartWriting: () -> Void
    let onPracticeMode: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            QuickActionButton(
                icon: "textformat",
                title: "Select",
                subtitle: "Character",
                color: .blue,
                action: onSelectCharacter
            )
            
            QuickActionButton(
                icon: "pencil",
                title: "Write",
                subtitle: "Practice",
                color: .green,
                action: onStartWriting,
                isDisabled: selectedCharacter == nil
            )
            
            QuickActionButton(
                icon: "play.fill",
                title: "Free",
                subtitle: "Mode",
                color: .orange,
                action: onPracticeMode
            )
        }
    }
}

struct QuickActionButton: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void
    let isDisabled: Bool
    
    init(icon: String, title: String, subtitle: String, color: Color, action: @escaping () -> Void, isDisabled: Bool = false) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.color = color
        self.action = action
        self.isDisabled = isDisabled
    }
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(isDisabled ? .gray : color)
                
                VStack(spacing: 1) {
                    Text(title)
                        .font(.caption)
                        .fontWeight(.semibold)
                    
                    Text(subtitle)
                        .font(.caption2)
                        .opacity(0.8)
                }
                .foregroundColor(isDisabled ? .gray : .primary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isDisabled ? Color(.tertiarySystemBackground) : color.opacity(0.1))
            )
        }
        .disabled(isDisabled)
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Compact Recent Characters

struct CompactRecentCharactersView: View {
    let characters: [TamilCharacter]
    let onCharacterTap: (TamilCharacter) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Recent Practice")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.secondary)
                .padding(.horizontal, 4)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(characters.prefix(6), id: \.id) { character in
                        Button(action: { onCharacterTap(character) }) {
                            Text(character.character)
                                .font(.title3)
                                .foregroundColor(.primary)
                                .frame(width: 40, height: 40)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color(.tertiarySystemBackground))
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal, 4)
            }
        }
    }
}

// MARK: - Compact Character Cell

struct CompactCharacterCell: View {
    let character: TamilCharacter
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 4) {
                Text(character.character)
                    .font(.title2)
                    .foregroundColor(.primary)
                
                Text(character.romanization)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(height: 60)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.blue.opacity(0.2) : Color(.secondarySystemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Compact Writing Tips

struct CompactWritingTipsView: View {
    private let tips = [
        "Hold your device comfortably",
        "Use your finger or Apple Pencil",
        "Follow the stroke order guides",
        "Practice regularly for best results"
    ]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Writing Tips")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.orange)
            
            ForEach(tips.prefix(2), id: \.self) { tip in
                HStack(alignment: .top, spacing: 8) {
                    Image(systemName: "lightbulb.fill")
                        .font(.caption)
                        .foregroundColor(.yellow)
                    
                    Text(tip)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.orange.opacity(0.05))
        )
        .padding(.horizontal)
    }
}

// MARK: - Compact Writing View Model

@MainActor
class CompactWritingViewModel: ObservableObject {
    @Published var recentCharacters: [TamilCharacter] = []
    @Published var isInLessonMode = false
    @Published var currentStep = 1
    @Published var totalSteps = 1
    @Published var progress: Double = 0.0
    @Published var currentCharacter: TamilCharacter?
    
    private let scriptService = TamilScriptService.shared
    
    func configureForCompactDevice() {
        // Load recent characters from user defaults or database
        loadRecentCharacters()
    }
    
    func recordWritingResult(_ result: WritingResult) {
        // Add to recent characters
        if !recentCharacters.contains(where: { $0.id == result.character.id }) {
            recentCharacters.insert(result.character, at: 0)
            recentCharacters = Array(recentCharacters.prefix(6))
        }
        
        // Save to user defaults
        saveRecentCharacters()
    }
    
    func advanceToNextCharacter() {
        if isInLessonMode && currentStep < totalSteps {
            currentStep += 1
            progress = Double(currentStep) / Double(totalSteps)
            
            // Load next character from lesson
            currentCharacter = getNextLessonCharacter()
        }
    }
    
    private func loadRecentCharacters() {
        // Load from UserDefaults or database
        recentCharacters = Array(scriptService.allCharacters.prefix(3))
    }
    
    private func saveRecentCharacters() {
        // Save to UserDefaults
    }
    
    private func getNextLessonCharacter() -> TamilCharacter? {
        // Get next character from current lesson
        return scriptService.allCharacters.randomElement()
    }
}

// MARK: - Compact Character Picker View

struct CompactCharacterPickerView: View {
    @Binding var selectedCharacter: TamilCharacter?
    let onCharacterSelected: (TamilCharacter) -> Void
    @Environment(\.dismiss) private var dismiss

    @StateObject private var scriptService = TamilScriptService.shared
    @State private var selectedCategory: CharacterCategory = .vowels
    @State private var searchText = ""

    enum CharacterCategory: String, CaseIterable {
        case vowels = "Vowels"
        case consonants = "Consonants"
        case combined = "Combined"
        case recent = "Recent"

        var icon: String {
            switch self {
            case .vowels: return "a.circle"
            case .consonants: return "textformat"
            case .combined: return "textformat.abc"
            case .recent: return "clock"
            }
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)

                    TextField("Search characters...", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                }
                .padding()
                .background(Color(.secondarySystemBackground))

                // Category selector
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(CharacterCategory.allCases, id: \.self) { category in
                            CategoryChip(
                                category: category,
                                isSelected: selectedCategory == category,
                                action: { selectedCategory = category }
                            )
                        }
                    }
                    .padding(.horizontal)
                }
                .padding(.vertical, 8)

                // Character grid
                ScrollView {
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 12) {
                        ForEach(filteredCharacters, id: \.id) { character in
                            CompactCharacterPickerCell(
                                character: character,
                                isSelected: selectedCharacter?.id == character.id,
                                onTap: {
                                    selectedCharacter = character
                                    onCharacterSelected(character)
                                    dismiss()
                                }
                            )
                        }
                    }
                    .padding()
                }
            }
            .navigationTitle("Select Character")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }

    private var filteredCharacters: [TamilCharacter] {
        let characters: [TamilCharacter]

        switch selectedCategory {
        case .vowels:
            characters = scriptService.vowels
        case .consonants:
            characters = scriptService.consonants
        case .combined:
            characters = scriptService.allCharacters.filter { $0.characterType == .combined }
        case .recent:
            characters = Array(scriptService.allCharacters.prefix(12)) // Mock recent
        }

        if searchText.isEmpty {
            return characters
        } else {
            return characters.filter { character in
                character.character.contains(searchText) ||
                character.romanization.lowercased().contains(searchText.lowercased()) ||
                character.characterNameEnglish.lowercased().contains(searchText.lowercased())
            }
        }
    }
}

struct CategoryChip: View {
    let category: CompactCharacterPickerView.CharacterCategory
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: category.icon)
                    .font(.caption)

                Text(category.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? Color.blue : Color(.tertiarySystemBackground))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct CompactCharacterPickerCell: View {
    let character: TamilCharacter
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 6) {
                Text(character.character)
                    .font(.title)
                    .foregroundColor(.primary)

                Text(character.romanization)
                    .font(.caption2)
                    .foregroundColor(.secondary)

                // Difficulty dots
                HStack(spacing: 2) {
                    ForEach(1...character.difficultyLevel, id: \.self) { _ in
                        Circle()
                            .fill(character.writingComplexity.color)
                            .frame(width: 4, height: 4)
                    }
                }
            }
            .frame(height: 80)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.blue.opacity(0.2) : Color(.secondarySystemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Compact Writing Canvas View

struct CompactWritingCanvasView: View {
    let character: TamilCharacter
    let writingMode: WritingMode
    let onComplete: (WritingResult) -> Void

    @Environment(\.dismiss) private var dismiss
    @StateObject private var strokeAnalyzer = TamilStrokeAnalyzer()
    @StateObject private var scriptService = TamilScriptService.shared

    @State private var canvasView = PKCanvasView()
    @State private var isDirty = false
    @State private var currentStrokes: [PKStroke] = []
    @State private var showingGuidance = true
    @State private var recognitionResults: [CharacterRecognitionResult] = []
    @State private var writingStartTime = Date()

    var body: some View {
        VStack(spacing: 0) {
            // Compact header
            compactCanvasHeader

            // Main canvas area
            ZStack {
                // Background guidance
                if showingGuidance && writingMode == .guided {
                    CompactCharacterGuidanceView(
                        character: character,
                        strokeOrders: scriptService.getStrokeOrder(for: character.id)
                    )
                }

                // Writing canvas
                TamilWritingCanvas(
                    canvasView: $canvasView,
                    isDirty: $isDirty,
                    character: character,
                    writingMode: writingMode,
                    onStrokeAdded: handleStrokeAdded,
                    onDrawingChanged: handleDrawingChanged
                )

                // Recognition feedback overlay
                if !recognitionResults.isEmpty && writingMode != .assessment {
                    CompactRecognitionFeedbackView(results: recognitionResults)
                }
            }
            .frame(maxHeight: .infinity)
            .background(Color(.systemBackground))

            // Compact controls
            compactCanvasControls
        }
        .background(Color(.systemBackground))
        .onAppear {
            setupCanvas()
        }
    }

    private var compactCanvasHeader: some View {
        VStack(spacing: 8) {
            HStack {
                Button("Cancel") {
                    dismiss()
                }
                .foregroundColor(.red)

                Spacer()

                VStack(spacing: 2) {
                    Text(character.character)
                        .font(.title2)
                        .fontWeight(.semibold)

                    Text(character.romanization)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Button("Done") {
                    completeWriting()
                }
                .foregroundColor(.blue)
                .disabled(!isDirty)
            }

            // Writing mode indicator
            HStack {
                Image(systemName: writingMode.icon)
                    .font(.caption)

                Text(writingMode.displayName)
                    .font(.caption)
                    .fontWeight(.medium)

                Spacer()

                if showingGuidance {
                    Text("Guidance: ON")
                        .font(.caption2)
                        .foregroundColor(.green)
                } else {
                    Text("Guidance: OFF")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemBackground))
    }

    private var compactCanvasControls: some View {
        HStack(spacing: 20) {
            // Clear
            Button(action: clearCanvas) {
                VStack(spacing: 2) {
                    Image(systemName: "trash")
                        .font(.title3)

                    Text("Clear")
                        .font(.caption2)
                }
                .foregroundColor(.red)
            }

            // Undo
            Button(action: undoLastStroke) {
                VStack(spacing: 2) {
                    Image(systemName: "arrow.uturn.backward")
                        .font(.title3)

                    Text("Undo")
                        .font(.caption2)
                }
                .foregroundColor(.blue)
            }
            .disabled(currentStrokes.isEmpty)

            Spacer()

            // Guidance toggle
            Button(action: { showingGuidance.toggle() }) {
                VStack(spacing: 2) {
                    Image(systemName: showingGuidance ? "eye.slash" : "eye")
                        .font(.title3)

                    Text("Guide")
                        .font(.caption2)
                }
                .foregroundColor(.orange)
            }

            // Complete
            Button(action: completeWriting) {
                VStack(spacing: 2) {
                    Image(systemName: "checkmark")
                        .font(.title3)

                    Text("Done")
                        .font(.caption2)
                }
                .foregroundColor(.green)
            }
            .disabled(!isDirty)
        }
        .padding()
        .background(Color(.secondarySystemBackground))
    }

    // MARK: - Canvas Actions

    private func setupCanvas() {
        canvasView.drawing = PKDrawing()
        currentStrokes = []
        writingStartTime = Date()
    }

    private func handleStrokeAdded(_ stroke: PKStroke) {
        currentStrokes.append(stroke)

        // Perform real-time recognition for feedback
        Task {
            await performCharacterRecognition()
        }
    }

    private func handleDrawingChanged(_ drawing: PKDrawing) {
        currentStrokes = drawing.strokes
    }

    private func performCharacterRecognition() async {
        let image = canvasView.drawing.image(from: canvasView.bounds, scale: 1.0)

        // Mock recognition results for now
        recognitionResults = [
            CharacterRecognitionResult(
                recognizedCharacter: character.character,
                confidence: 0.85,
                boundingBox: .zero,
                alternativeMatches: []
            )
        ]
    }

    private func clearCanvas() {
        canvasView.drawing = PKDrawing()
        currentStrokes = []
        recognitionResults = []
        isDirty = false
    }

    private func undoLastStroke() {
        guard !currentStrokes.isEmpty else { return }

        currentStrokes.removeLast()

        let newDrawing = PKDrawing()
        for stroke in currentStrokes {
            newDrawing.strokes.append(stroke)
        }
        canvasView.drawing = newDrawing

        isDirty = !currentStrokes.isEmpty
    }

    private func completeWriting() {
        let completionTime = Date().timeIntervalSince(writingStartTime)
        let accuracy = recognitionResults.first?.confidence ?? 0.0

        let result = WritingResult(
            character: character,
            writingMode: writingMode,
            strokes: currentStrokes,
            recognitionResults: recognitionResults,
            accuracy: accuracy,
            completionTime: completionTime,
            strokeAccuracies: [],
            isCompleted: true
        )

        onComplete(result)
        dismiss()
    }
}

// MARK: - Compact Character Guidance

struct CompactCharacterGuidanceView: View {
    let character: TamilCharacter
    let strokeOrders: [TamilStrokeOrder]

    var body: some View {
        Text(character.character)
            .font(.system(size: 150, weight: .ultraLight))
            .foregroundColor(.gray.opacity(0.2))
    }
}

// MARK: - Compact Recognition Feedback

struct CompactRecognitionFeedbackView: View {
    let results: [CharacterRecognitionResult]

    var body: some View {
        VStack {
            HStack {
                Spacer()

                if let bestResult = results.first {
                    VStack(spacing: 2) {
                        Text(bestResult.recognizedCharacter)
                            .font(.title3)

                        Text("\(Int(bestResult.confidence * 100))%")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(.systemBackground))
                            .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                    )
                }
            }

            Spacer()
        }
        .padding()
    }
}

#Preview {
    CompactWritingView()
}
